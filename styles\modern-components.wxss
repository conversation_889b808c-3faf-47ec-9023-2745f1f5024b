/**
 * 简约现代风格通用组件样式
 * 基于设计Token系统，提供一致的现代化组件样式
 */

@import './design-tokens.wxss';

/* ==================== 页面布局 ==================== */
.modern-page {
  min-height: 100vh;
  background-color: var(--bg-page);
  padding-bottom: 40rpx;
}

.modern-container {
  max-width: var(--content-max-width);
  margin: 0 auto;
  padding: 0 var(--page-padding);
}

/* ==================== 导航栏 ==================== */
.modern-navbar {
  background-color: var(--bg-primary);
  padding: var(--navbar-padding) var(--page-padding);
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-xs);
}

.modern-navbar-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.modern-navbar-actions {
  display: flex;
  gap: var(--spacing-base);
}

/* ==================== 按钮组件 ==================== */
.modern-btn {
  height: var(--btn-height-base);
  padding: 0 var(--btn-padding-x);
  border-radius: var(--btn-border-radius);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-base) var(--ease-in-out);
  box-shadow: var(--shadow-xs);
  position: relative;
  overflow: hidden;
}

.modern-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
}

.modern-btn:active::before {
  left: 100%;
}

.modern-btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: var(--text-inverse);
  box-shadow: var(--shadow-sm);
}

.modern-btn-primary:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: var(--shadow-xs);
}

.modern-btn-secondary {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  border: 2rpx solid var(--border-color);
}

.modern-btn-secondary:active {
  background-color: var(--bg-secondary);
  transform: translateY(2rpx) scale(0.98);
}

.modern-btn-ghost {
  background-color: transparent;
  color: var(--primary-color);
  border: 2rpx solid var(--primary-color);
}

.modern-btn-ghost:active {
  background-color: var(--primary-ultra-light);
  transform: scale(0.98);
}

.modern-btn-small {
  height: var(--btn-height-sm);
  padding: 0 var(--spacing-lg);
  font-size: var(--font-size-sm);
}

.modern-btn-large {
  height: var(--btn-height-lg);
  padding: 0 var(--spacing-4xl);
  font-size: var(--font-size-lg);
}

/* ==================== 卡片组件 ==================== */
.modern-card {
  background-color: var(--bg-primary);
  border-radius: var(--card-border-radius);
  padding: var(--card-padding);
  box-shadow: var(--card-shadow);
  transition: all var(--transition-base) var(--ease-in-out);
  position: relative;
  overflow: hidden;
  margin-bottom: var(--spacing-xl);
}

.modern-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

.modern-card:active {
  transform: translateY(-4rpx) scale(0.99);
  box-shadow: var(--card-hover-shadow);
}

.modern-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--section-spacing);
  padding-bottom: var(--spacing-lg);
  border-bottom: 2rpx solid var(--bg-secondary);
}

.modern-card-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.modern-card-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
}

.modern-card-body {
  margin-bottom: var(--spacing-lg);
}

.modern-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--spacing-lg);
  border-top: 2rpx solid var(--bg-secondary);
}

/* ==================== 输入框组件 ==================== */
.modern-input-group {
  margin-bottom: var(--spacing-lg);
}

.modern-input-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.modern-input {
  width: 100%;
  height: var(--input-height);
  padding: 0 var(--input-padding);
  border-radius: var(--input-border-radius);
  border: 2rpx solid var(--border-color);
  background-color: var(--bg-primary);
  font-size: var(--font-size-base);
  color: var(--text-primary);
  transition: all var(--transition-base);
}

.modern-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 6rpx var(--primary-ultra-light);
}

.modern-input-search {
  background-color: var(--bg-secondary);
  border: none;
  box-shadow: var(--shadow-xs);
}

/* ==================== 标签组件 ==================== */
.modern-tag {
  display: inline-flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 1rpx;
}

.modern-tag-primary {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.modern-tag-success {
  background-color: var(--success-light);
  color: var(--success-color);
}

.modern-tag-warning {
  background-color: var(--warning-light);
  color: var(--warning-color);
}

.modern-tag-error {
  background-color: var(--error-light);
  color: var(--error-color);
}

.modern-tag-neutral {
  background-color: var(--bg-secondary);
  color: var(--text-tertiary);
}

/* ==================== 信息区块组件 ==================== */
.modern-section {
  margin-bottom: var(--section-spacing);
  padding: var(--spacing-lg);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border-left: 6rpx solid var(--primary-color);
}

.modern-section-title {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-base);
  gap: var(--spacing-sm);
}

.modern-section-icon {
  font-size: var(--font-size-lg);
  opacity: 0.8;
}

.modern-section-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 1rpx;
}

.modern-section-content {
  font-size: var(--font-size-base);
  color: var(--text-primary);
  line-height: var(--line-height-relaxed);
}

/* ==================== 列表组件 ==================== */
.modern-list {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-xs);
}

.modern-list-item {
  padding: var(--list-item-padding);
  border-bottom: 1rpx solid var(--border-color);
  transition: background-color var(--transition-base);
}

.modern-list-item:last-child {
  border-bottom: none;
}

.modern-list-item:active {
  background-color: var(--bg-secondary);
}

/* ==================== 空状态组件 ==================== */
.modern-empty {
  text-align: center;
  padding: var(--spacing-4xl) var(--spacing-xl);
  color: var(--text-tertiary);
}

.modern-empty-icon {
  font-size: 120rpx;
  margin-bottom: var(--spacing-lg);
  opacity: 0.5;
}

.modern-empty-text {
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-xl);
}

/* ==================== 加载状态组件 ==================== */
.modern-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  color: var(--text-tertiary);
}

.modern-loading-text {
  font-size: var(--font-size-base);
  margin-left: var(--spacing-sm);
}

/* ==================== 工具类 ==================== */
.modern-text-primary { color: var(--text-primary); }
.modern-text-secondary { color: var(--text-secondary); }
.modern-text-tertiary { color: var(--text-tertiary); }
.modern-text-center { text-align: center; }
.modern-text-right { text-align: right; }

.modern-bg-primary { background-color: var(--bg-primary); }
.modern-bg-secondary { background-color: var(--bg-secondary); }

.modern-shadow-sm { box-shadow: var(--shadow-sm); }
.modern-shadow-base { box-shadow: var(--shadow-base); }
.modern-shadow-lg { box-shadow: var(--shadow-lg); }

.modern-rounded-sm { border-radius: var(--radius-sm); }
.modern-rounded-base { border-radius: var(--radius-base); }
.modern-rounded-lg { border-radius: var(--radius-lg); }
.modern-rounded-xl { border-radius: var(--radius-xl); }

.modern-mb-sm { margin-bottom: var(--spacing-sm); }
.modern-mb-base { margin-bottom: var(--spacing-base); }
.modern-mb-lg { margin-bottom: var(--spacing-lg); }
.modern-mb-xl { margin-bottom: var(--spacing-xl); }
