<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>风格3 - 活泼清新风格</title>
    <style>
        /*
        设计理念：活泼清新风格
        - 色彩：绿色主色调，彩虹色点缀，清新自然
        - 特点：活泼、年轻、充满活力
        - 适用：创新型企业、年轻团队、互联网公司
        - 字体：圆润字体，亲和力强
        - 圆角：大圆角，亲和感
        */
        
        :root {
            --primary-color: #00C896;
            --secondary-color: #00B4D8;
            --accent-color: #FF6B6B;
            --success-color: #51CF66;
            --warning-color: #FFD43B;
            --error-color: #FF8787;
            
            --text-primary: #2D3748;
            --text-secondary: #4A5568;
            --text-tertiary: #718096;
            --text-inverse: #FFFFFF;
            
            --bg-primary: #FFFFFF;
            --bg-secondary: #F7FAFC;
            --bg-tertiary: #EDF2F7;
            --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            
            --border-color: #E2E8F0;
            --shadow-light: 0 4px 6px rgba(0, 200, 150, 0.1);
            --shadow-medium: 0 10px 25px rgba(0, 200, 150, 0.15);
            --shadow-colorful: 0 8px 32px rgba(102, 126, 234, 0.2);
            
            --radius-sm: 12px;
            --radius-md: 16px;
            --radius-lg: 24px;
            --radius-xl: 32px;
            
            --spacing-xs: 8px;
            --spacing-sm: 12px;
            --spacing-md: 16px;
            --spacing-lg: 24px;
            --spacing-xl: 32px;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'SF Pro Rounded', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: var(--text-primary);
            line-height: 1.6;
            width: 375px;
            margin: 0 auto;
            min-height: 100vh;
        }
        
        .container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        /* 导航栏 */
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: var(--spacing-lg) var(--spacing-md);
            border-radius: 0 0 var(--radius-lg) var(--radius-lg);
            margin: 0 var(--spacing-sm);
            margin-top: var(--spacing-sm);
            position: sticky;
            top: var(--spacing-sm);
            z-index: 100;
            box-shadow: var(--shadow-colorful);
        }
        
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .nav-title {
            font-size: 24px;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .nav-emoji {
            font-size: 28px;
            margin-right: var(--spacing-sm);
        }
        
        .nav-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn {
            padding: var(--spacing-sm) var(--spacing-md);
            border: none;
            border-radius: var(--radius-lg);
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: var(--shadow-light);
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.8);
            color: var(--text-primary);
            backdrop-filter: blur(10px);
        }
        
        .btn:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: var(--shadow-medium);
        }
        
        .btn:active {
            transform: translateY(0) scale(0.98);
        }
        
        /* 订单列表 */
        .order-list {
            padding: var(--spacing-md);
        }
        
        .order-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-xl);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-md);
            box-shadow: var(--shadow-light);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        
        .order-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
        }
        
        .order-card:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: var(--shadow-colorful);
        }
        
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--spacing-md);
        }
        
        .order-number {
            font-size: 18px;
            font-weight: 800;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }
        
        .order-emoji {
            font-size: 20px;
        }
        
        .order-status {
            padding: 8px 16px;
            border-radius: var(--radius-lg);
            font-size: 12px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-pending {
            background: linear-gradient(135deg, #FFD43B, #FFA726);
            color: #BF360C;
        }
        
        .status-confirmed {
            background: linear-gradient(135deg, #51CF66, #00C896);
            color: #1B5E20;
        }
        
        .order-section {
            margin-bottom: var(--spacing-md);
            padding: var(--spacing-md);
            background: rgba(255, 255, 255, 0.5);
            border-radius: var(--radius-md);
            border-left: 4px solid;
            border-image: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) 1;
        }
        
        .section-title {
            font-size: 12px;
            font-weight: 700;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: var(--spacing-xs);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }
        
        .section-emoji {
            font-size: 14px;
        }
        
        .section-content {
            font-size: 14px;
            color: var(--text-primary);
            line-height: 1.5;
            font-weight: 500;
        }
        
        .highlight {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
        }
        
        .order-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-md);
            background: linear-gradient(135deg, rgba(0, 200, 150, 0.1), rgba(0, 180, 216, 0.1));
            border-radius: var(--radius-md);
            font-size: 13px;
            color: var(--text-secondary);
            font-weight: 600;
        }
        
        .order-date {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }
        
        .order-amount {
            font-size: 16px;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: var(--spacing-sm);
            left: 50%;
            transform: translateX(-50%);
            width: calc(375px - 16px);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-lg);
            padding: var(--spacing-sm) 0;
            display: flex;
            justify-content: space-around;
            box-shadow: var(--shadow-colorful);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: var(--spacing-xs);
            color: var(--text-tertiary);
            text-decoration: none;
            font-size: 10px;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: var(--spacing-sm);
        }
        
        .nav-item.active {
            background: linear-gradient(135deg, rgba(0, 200, 150, 0.2), rgba(0, 180, 216, 0.2));
            color: var(--primary-color);
            transform: scale(1.1);
        }
        
        .nav-icon {
            width: 24px;
            height: 24px;
            background: currentColor;
            border-radius: var(--spacing-xs);
            transition: all 0.3s ease;
        }
        
        .nav-item:hover .nav-icon {
            transform: scale(1.2) rotate(5deg);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 导航栏 -->
        <div class="navbar">
            <div class="nav-header">
                <h1 class="nav-title">
                    <span class="nav-emoji">📋</span>订单管理
                </h1>
                <div class="nav-actions">
                    <button class="btn btn-primary">✨ 新建</button>
                    <button class="btn btn-secondary">⚙️ 设置</button>
                </div>
            </div>
        </div>
        
        <!-- 订单列表 -->
        <div class="order-list">
            <div class="order-card">
                <div class="order-header">
                    <div class="order-number">
                        <span class="order-emoji">📦</span>
                        PO20240101001
                    </div>
                    <div class="order-status status-pending">待确认</div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">
                        <span class="section-emoji">🏢</span>
                        客户信息
                    </div>
                    <div class="section-content">
                        <div class="highlight">华东机械制造有限公司</div>
                        联系人：张工 | 📞 13800138000
                    </div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">
                        <span class="section-emoji">🔧</span>
                        产品信息
                    </div>
                    <div class="section-content">
                        <div class="highlight">精密齿轮 (GEAR001)</div>
                        数量：100件 | 高精度传动齿轮
                    </div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">
                        <span class="section-emoji">📦</span>
                        包装信息
                    </div>
                    <div class="section-content">
                        防潮包装 | ⏰ 预计3-5个工作日
                    </div>
                </div>
                
                <div class="order-meta">
                    <span class="order-date">
                        🕐 2024-01-15 09:30
                    </span>
                    <span class="order-amount">💰 ￥15,000.00</span>
                </div>
            </div>
            
            <div class="order-card">
                <div class="order-header">
                    <div class="order-number">
                        <span class="order-emoji">📋</span>
                        PO20240101002
                    </div>
                    <div class="order-status status-confirmed">已确认</div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">
                        <span class="section-emoji">🏢</span>
                        客户信息
                    </div>
                    <div class="section-content">
                        <div class="highlight">江南精工科技股份有限公司</div>
                        联系人：李经理 | 📞 13900139000
                    </div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">
                        <span class="section-emoji">🔧</span>
                        产品信息
                    </div>
                    <div class="section-content">
                        <div class="highlight">不锈钢板材 (STEEL002)</div>
                        数量：50平方米 | 304不锈钢板材，厚度3mm
                    </div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">
                        <span class="section-emoji">📦</span>
                        包装信息
                    </div>
                    <div class="section-content">
                        标准包装 | ⏰ 预计2-3个工作日
                    </div>
                </div>
                
                <div class="order-meta">
                    <span class="order-date">
                        🕐 2024-01-14 14:20
                    </span>
                    <span class="order-amount">💰 ￥4,275.00</span>
                </div>
            </div>
        </div>
        
        <!-- 底部导航 -->
        <div class="bottom-nav">
            <a href="#" class="nav-item">
                <div class="nav-icon"></div>
                <span>首页</span>
            </a>
            <a href="#" class="nav-item active">
                <div class="nav-icon"></div>
                <span>订单</span>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon"></div>
                <span>工单</span>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon"></div>
                <span>包装</span>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon"></div>
                <span>出入库</span>
            </a>
        </div>
    </div>
</body>
</html>
