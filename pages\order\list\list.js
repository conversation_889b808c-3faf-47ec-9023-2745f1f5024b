// 订单列表页面逻辑
const OrderService = require('../../../services/orderService')
const { formatDate } = require('../../../utils/dateUtils')
const { getStatusText, getPriorityText, debounce } = require('../../../utils/common')

Page({
  data: {
    // 搜索和筛选
    searchKeyword: '',
    activeStatus: 'all',
    statusOptions: [
      { label: '全部', value: 'all' },
      { label: '待确认', value: 'pending' },
      { label: '已确认', value: 'confirmed' },
      { label: '生产中', value: 'processing' },
      { label: '已完成', value: 'completed' },
      { label: '已取消', value: 'cancelled' }
    ],
    
    // 列表数据
    orderList: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    
    // UI状态
    showSettingsMenu: false
  },

  onLoad(options) {
    console.log('订单列表页面加载', options)
    
    // 处理从其他页面传入的状态参数
    if (options.status) {
      this.setData({
        activeStatus: options.status
      })
    }
    
    this.loadOrderList()
  },

  onShow() {
    console.log('订单列表页面显示')
    // 每次显示时刷新第一页数据
    this.refreshData()
  },

  onPullDownRefresh() {
    console.log('订单列表下拉刷新')
    this.refreshData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  onReachBottom() {
    console.log('订单列表触底加载')
    if (this.data.hasMore && !this.data.loading) {
      this.loadMore()
    }
  },

  /**
   * 加载订单列表
   */
  async loadOrderList(isLoadMore = false) {
    if (this.data.loading) return
    
    this.setData({ loading: true })
    
    try {
      const params = {
        page: isLoadMore ? this.data.page + 1 : 1,
        pageSize: this.data.pageSize,
        status: this.data.activeStatus,
        keyword: this.data.searchKeyword
      }
      
      const result = await OrderService.getOrderList(params)
      
      if (result.success) {
        const processedOrders = result.data.map(order => ({
          ...order,
          statusText: getStatusText(order.status, 'order'),
          priorityText: getPriorityText(order.priority),
          deliveryDateText: formatDate(order.deliveryDate, 'MM-DD')
        }))
        
        this.setData({
          orderList: isLoadMore ? [...this.data.orderList, ...processedOrders] : processedOrders,
          hasMore: result.hasMore,
          page: params.page
        })
        
        console.log('订单列表加载成功:', processedOrders)
      }
    } catch (error) {
      console.error('加载订单列表失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    this.setData({
      page: 1,
      hasMore: true
    })
    await this.loadOrderList()
  },

  /**
   * 加载更多
   */
  loadMore() {
    this.loadOrderList(true)
  },

  /**
   * 搜索输入
   */
  onSearchInput: debounce(function(e) {
    const keyword = e.detail.value.trim()
    this.setData({ searchKeyword: keyword })
    this.refreshData()
  }, 500),

  /**
   * 搜索确认
   */
  onSearchConfirm(e) {
    const keyword = e.detail.value.trim()
    this.setData({ searchKeyword: keyword })
    this.refreshData()
  },

  /**
   * 清除搜索
   */
  clearSearch() {
    this.setData({ searchKeyword: '' })
    this.refreshData()
  },

  /**
   * 状态筛选
   */
  onStatusChange(e) {
    const status = e.currentTarget.dataset.status
    if (status !== this.data.activeStatus) {
      this.setData({ activeStatus: status })
      this.refreshData()
    }
  },

  /**
   * 查看订单详情
   */
  viewOrderDetail(e) {
    const order = e.currentTarget.dataset.order
    wx.navigateTo({
      url: `/pages/order/detail/detail?id=${order.id}`
    })
  },

  /**
   * 编辑订单
   */
  editOrder(e) {
    const order = e.currentTarget.dataset.order
    wx.navigateTo({
      url: `/pages/order/create/create?id=${order.id}&mode=edit`
    })
  },

  /**
   * 创建工单
   */
  createWorkOrder(e) {
    const order = e.currentTarget.dataset.order
    wx.navigateTo({
      url: `/pages/workorder/create/create?orderId=${order.id}`
    })
  },

  /**
   * 创建订单
   */
  createOrder() {
    // 清除之前的草稿数据
    wx.removeStorageSync('orderStepData')
    wx.removeStorageSync('orderDraft')

    wx.navigateTo({
      url: '/pages/order/create/step1/step1'
    })
  },

  /**
   * 打开设置页面
   */
  openSettings() {
    wx.navigateTo({
      url: '/pages/settings/index/index'
    })
  }
})
