/* 工单创建页面样式 - 简约现代风格 */
@import '../../../styles/modern-simple.wxss';

.page-container {
  background-color: #FAFAFA;
  min-height: 100vh;
  padding-bottom: 140rpx;
}

/* 创建模式选择 */
.creation-mode {
  padding: 40rpx 20rpx;
}

.mode-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.mode-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1D1D1F;
  display: block;
  margin-bottom: 12rpx;
}

.mode-subtitle {
  font-size: 28rpx;
  color: #8E8E93;
}

.mode-options {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.mode-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.mode-card:active {
  transform: scale(0.98);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
}

.mode-icon {
  font-size: 48rpx;
  width: 80rpx;
  text-align: center;
}

.mode-info {
  flex: 1;
}

.mode-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #1D1D1F;
  display: block;
  margin-bottom: 8rpx;
}

.mode-desc {
  font-size: 24rpx;
  color: #8E8E93;
  line-height: 1.4;
}

.mode-arrow {
  font-size: 28rpx;
  color: #C7C7CC;
}

/* 表单容器 */
.form-container {
  padding: 20rpx;
}

.form-section {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #1D1D1F;
  margin-bottom: 24rpx;
  padding-bottom: 12rpx;
  border-bottom: 2rpx solid #E3F2FD;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 表单项 */
.form-item {
  margin-bottom: 24rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #1D1D1F;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.required {
  color: #FF3B30;
}

/* 输入框 */
.input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #E5E5EA;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #1D1D1F;
  background-color: #FFFFFF;
  box-sizing: border-box;
}

.input:focus {
  border-color: #007AFF;
}

.textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 16rpx 20rpx;
  border: 2rpx solid #E5E5EA;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #1D1D1F;
  background-color: #FFFFFF;
  box-sizing: border-box;
}

.textarea:focus {
  border-color: #007AFF;
}

/* 选择器 */
.picker-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #E5E5EA;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #1D1D1F;
  background-color: #FFFFFF;
}

.picker-arrow {
  color: #C7C7CC;
  font-size: 24rpx;
}

/* 数量输入 */
.quantity-input {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  border: 2rpx solid #E5E5EA;
  border-radius: 12rpx;
  background-color: #FFFFFF;
  color: #1D1D1F;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.quantity-btn:active {
  background-color: #F2F2F7;
}

.quantity-value {
  flex: 1;
  height: 60rpx;
  text-align: center;
  border: 2rpx solid #E5E5EA;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #1D1D1F;
}

.unit {
  font-size: 24rpx;
  color: #8E8E93;
  min-width: 40rpx;
}

.unit-input {
  width: 80rpx;
  height: 60rpx;
  text-align: center;
  border: 2rpx solid #E5E5EA;
  border-radius: 12rpx;
  font-size: 24rpx;
  color: #1D1D1F;
}

/* 订单信息展示 */
.order-info {
  background-color: #F2F2F7;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-top: 16rpx;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 24rpx;
  color: #8E8E93;
  min-width: 140rpx;
}

.info-value {
  font-size: 24rpx;
  color: #1D1D1F;
  font-weight: 500;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  padding: 20rpx;
  border-top: 1rpx solid #E5E5EA;
  z-index: 100;
}

.action-buttons {
  display: flex;
  gap: 16rpx;
}

.action-buttons button {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-back {
  background-color: #F2F2F7;
  color: #1D1D1F;
  border: 2rpx solid #E5E5EA;
}

.btn-submit {
  background-color: #007AFF;
  color: #FFFFFF;
}

.btn-submit[disabled] {
  background-color: #F2F2F7;
  color: #C7C7CC;
}

/* 工序管理样式 */
.section-actions {
  display: flex;
  gap: 12rpx;
  margin-left: auto;
}

.btn-route,
.btn-process {
  background-color: #007AFF;
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 12rpx 20rpx;
  border-radius: 12rpx;
  border: none;
  transition: all 0.2s ease;
}

.btn-route:active,
.btn-process:active {
  background-color: #0056CC;
  transform: scale(0.95);
}

/* 工序列表样式 */
.process-list {
  margin-top: 20rpx;
}

.process-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  width: 100%;
  box-sizing: border-box;
}

.process-item:active {
  transform: translateY(2rpx) scale(0.99);
}

.process-order {
  width: 40rpx;
  height: 40rpx;
  background-color: #007AFF;
  color: #FFFFFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: 600;
  margin-right: 12rpx;
  flex-shrink: 0;
}

.process-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 200rpx;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.process-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #1D1D1F;
  margin-bottom: 8rpx;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
}

.process-desc {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 8rpx;
  line-height: 1.4;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
}

.process-hours {
  font-size: 22rpx;
  color: #007AFF;
  font-weight: 500;
}

.process-controls {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 6rpx;
  margin-left: 16rpx;
  flex-shrink: 0;
  width: 120rpx;
  justify-content: flex-end;
}

.control-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 8rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  transition: all 0.2s ease;
}

.control-btn:not(.edit-btn):not(.remove-btn) {
  background-color: #F2F2F7;
  color: #007AFF;
}

.control-btn:not(.edit-btn):not(.remove-btn):active {
  background-color: #E5E5EA;
}

.control-btn:disabled {
  background-color: #F2F2F7;
  color: #C7C7CC;
}

.edit-btn {
  background-color: #E3F2FD;
  color: #007AFF;
}

.edit-btn:active {
  background-color: #BBDEFB;
}

.remove-btn {
  background-color: #FFEBEE;
  color: #FF3B30;
}

.remove-btn:active {
  background-color: #FFCDD2;
}

/* 空状态样式 */
.empty-processes {
  padding: 60rpx 40rpx;
  text-align: center;
  background-color: #F8F9FA;
  border: 2rpx dashed #E5E5EA;
  border-radius: 16rpx;
  margin-top: 20rpx;
}

.empty-icon {
  font-size: 64rpx;
  display: block;
  margin-bottom: 16rpx;
}

.empty-text {
  color: #8E8E93;
}

.empty-text > view:first-child {
  font-size: 28rpx;
  margin-bottom: 8rpx;
}

.empty-text > view:last-child {
  font-size: 24rpx;
}

/* 总工时显示 */
.total-hours {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx;
  background-color: #F0F8FF;
  border-radius: 12rpx;
  margin-top: 16rpx;
}

.total-label {
  font-size: 28rpx;
  color: #1D1D1F;
  margin-right: 8rpx;
}

.total-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #007AFF;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
}

.modal-container {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 16rpx 32rpx rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E5EA;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1D1D1F;
}

.modal-close {
  width: 64rpx;
  height: 64rpx;
  border-radius: 12rpx;
  border: none;
  background-color: #F2F2F7;
  color: #8E8E93;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:active {
  background-color: #E5E5EA;
}

.modal-body {
  padding: 32rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 1rpx solid #E5E5EA;
}

.btn-cancel,
.btn-confirm {
  flex: 1;
  height: 88rpx;
  border-radius: 16rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
}

.btn-cancel {
  background-color: #F2F2F7;
  color: #1D1D1F;
}

.btn-cancel:active {
  background-color: #E5E5EA;
}

.btn-confirm {
  background-color: #007AFF;
  color: #FFFFFF;
}

.btn-confirm:active {
  background-color: #0056CC;
}

/* 搜索输入框 */
.route-search,
.process-search {
  margin-bottom: 20rpx;
}

.search-input {
  width: 100%;
  height: 80rpx;
  padding: 0 24rpx;
  background-color: #F8F9FA;
  border: 2rpx solid #E5E5EA;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #1D1D1F;
  box-sizing: border-box;
}

.search-input:focus {
  border-color: #007AFF;
  box-shadow: 0 0 0 6rpx rgba(0, 122, 255, 0.1);
}

/* 可选项列表样式 */
.available-routes,
.available-processes {
  max-height: 400rpx;
  overflow-y: auto;
}

.available-route-item,
.available-process-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background-color: #FFFFFF;
  border: 1rpx solid #E5E5EA;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
  transition: all 0.2s ease;
}

.available-route-item:active,
.available-process-item:active {
  background-color: #F8F9FA;
  transform: scale(0.98);
}

.available-route-item:last-child,
.available-process-item:last-child {
  margin-bottom: 0;
}

.route-info,
.available-process-item .process-info {
  flex: 1;
}

.route-name,
.available-process-item .process-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #1D1D1F;
  margin-bottom: 8rpx;
  display: block;
}

.route-desc,
.available-process-item .process-category {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 8rpx;
  display: block;
}

.route-stats {
  display: flex;
  gap: 16rpx;
}

.route-processes,
.route-hours,
.available-process-item .process-hours {
  font-size: 22rpx;
  color: #007AFF;
  font-weight: 500;
}

/* 表单组样式 */
.form-group {
  margin-bottom: 32rpx;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #1D1D1F;
  margin-bottom: 12rpx;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 24rpx;
  background-color: #FFFFFF;
  border: 2rpx solid #E5E5EA;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #1D1D1F;
  box-sizing: border-box;
}

.form-input:focus,
.form-textarea:focus {
  border-color: #007AFF;
  box-shadow: 0 0 0 6rpx rgba(0, 122, 255, 0.1);
}

.form-textarea {
  min-height: 120rpx;
  line-height: 1.5;
}

/* 空状态通用样式 */
.empty-state {
  padding: 60rpx 40rpx;
  text-align: center;
}

.empty-state .empty-icon {
  font-size: 64rpx;
  display: block;
  margin-bottom: 16rpx;
  color: #C7C7CC;
}

.empty-state .empty-text {
  color: #8E8E93;
}

.empty-state .empty-text > view:first-child {
  font-size: 28rpx;
  margin-bottom: 8rpx;
}

.empty-state .empty-text > view:last-child {
  font-size: 24rpx;
}

/* 响应式调整 */
@media (max-width: 400px) {
  .section-actions {
    flex-direction: column;
    gap: 8rpx;
  }

  .btn-route,
  .btn-process {
    font-size: 22rpx;
    padding: 10rpx 16rpx;
  }

  .process-controls {
    flex-direction: row;
    gap: 4rpx;
  }

  .control-btn {
    width: 48rpx;
    height: 48rpx;
    font-size: 20rpx;
  }
}

/* 数量说明样式 */
.quantity-note {
  margin-top: 20rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #E3F2FD 0%, #F3E5F5 100%);
  border-radius: 16rpx;
  border-left: 6rpx solid #007AFF;
}

.note-text {
  font-size: 28rpx;
  color: #1D1D1F;
  line-height: 1.4;
  display: block;
}
