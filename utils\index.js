/**
 * 工具函数统一入口
 * 整合所有工具函数，提供统一的导入接口
 */

// 导入现有的工具函数
const commonUtils = require('./common')
const dateUtils = require('./dateUtils')

// 导入新增的工具函数
const validators = require('./validators')
const constants = require('./constants')
const errorHandler = require('./errorHandler')

/**
 * 统一的工具函数对象
 */
const utils = {
  // 通用工具函数
  ...commonUtils,
  
  // 日期工具函数
  ...dateUtils,
  
  // 数据验证工具
  ...validators,
  
  // 常量
  ...constants,
  
  // 错误处理工具
  ...errorHandler,

  /**
   * 格式化金额
   * @param {number} amount 金额
   * @param {number} decimals 小数位数
   * @returns {string} 格式化后的金额
   */
  formatCurrency(amount, decimals = 2) {
    if (typeof amount !== 'number') return '0.00'
    return amount.toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  },

  /**
   * 格式化百分比
   * @param {number} value 数值
   * @param {number} total 总数
   * @param {number} decimals 小数位数
   * @returns {string} 百分比字符串
   */
  formatPercentage(value, total, decimals = 1) {
    if (!total || total === 0) return '0%'
    const percentage = (value / total) * 100
    return `${percentage.toFixed(decimals)}%`
  },

  /**
   * 生成随机颜色
   * @returns {string} 十六进制颜色值
   */
  generateRandomColor() {
    return '#' + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0')
  },

  /**
   * 获取文件扩展名
   * @param {string} filename 文件名
   * @returns {string} 扩展名
   */
  getFileExtension(filename) {
    if (!filename || typeof filename !== 'string') return ''
    const lastDotIndex = filename.lastIndexOf('.')
    return lastDotIndex === -1 ? '' : filename.slice(lastDotIndex + 1).toLowerCase()
  },

  /**
   * 检查是否为移动设备
   * @returns {boolean} 是否为移动设备
   */
  isMobile() {
    try {
      const systemInfo = wx.getSystemInfoSync()
      return systemInfo.platform === 'android' || systemInfo.platform === 'ios'
    } catch (error) {
      return false
    }
  },

  /**
   * 获取设备信息
   * @returns {Object} 设备信息
   */
  getDeviceInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync()
      return {
        platform: systemInfo.platform,
        system: systemInfo.system,
        version: systemInfo.version,
        model: systemInfo.model,
        brand: systemInfo.brand,
        screenWidth: systemInfo.screenWidth,
        screenHeight: systemInfo.screenHeight,
        windowWidth: systemInfo.windowWidth,
        windowHeight: systemInfo.windowHeight,
        pixelRatio: systemInfo.pixelRatio,
        language: systemInfo.language
      }
    } catch (error) {
      console.error('获取设备信息失败:', error)
      return {}
    }
  },

  /**
   * 复制文本到剪贴板
   * @param {string} text 要复制的文本
   * @returns {Promise} 复制结果
   */
  copyToClipboard(text) {
    return new Promise((resolve, reject) => {
      wx.setClipboardData({
        data: text,
        success: () => {
          wx.showToast({
            title: '复制成功',
            icon: 'success'
          })
          resolve(true)
        },
        fail: (error) => {
          wx.showToast({
            title: '复制失败',
            icon: 'none'
          })
          reject(error)
        }
      })
    })
  },

  /**
   * 显示加载提示
   * @param {string} title 提示文字
   * @param {boolean} mask 是否显示透明蒙层
   */
  showLoading(title = '加载中...', mask = true) {
    wx.showLoading({
      title,
      mask
    })
  },

  /**
   * 隐藏加载提示
   */
  hideLoading() {
    wx.hideLoading()
  },

  /**
   * 显示成功提示
   * @param {string} title 提示文字
   * @param {number} duration 显示时长
   */
  showSuccess(title = '操作成功', duration = 2000) {
    wx.showToast({
      title,
      icon: 'success',
      duration
    })
  },

  /**
   * 显示错误提示
   * @param {string} title 提示文字
   * @param {number} duration 显示时长
   */
  showError(title = '操作失败', duration = 2000) {
    wx.showToast({
      title,
      icon: 'none',
      duration
    })
  },

  /**
   * 显示确认对话框
   * @param {string} content 对话框内容
   * @param {string} title 对话框标题
   * @returns {Promise} 用户选择结果
   */
  showConfirm(content, title = '提示') {
    return new Promise((resolve) => {
      wx.showModal({
        title,
        content,
        success: (res) => {
          resolve(res.confirm)
        },
        fail: () => {
          resolve(false)
        }
      })
    })
  },

  /**
   * 页面跳转
   * @param {string} url 页面路径
   * @param {Object} params 参数对象
   */
  navigateTo(url, params = {}) {
    let fullUrl = url
    
    // 添加参数
    if (Object.keys(params).length > 0) {
      const queryString = Object.keys(params)
        .map(key => `${key}=${encodeURIComponent(params[key])}`)
        .join('&')
      fullUrl += (url.includes('?') ? '&' : '?') + queryString
    }
    
    wx.navigateTo({
      url: fullUrl,
      fail: (error) => {
        console.error('页面跳转失败:', error)
        this.showError('页面跳转失败')
      }
    })
  },

  /**
   * 页面重定向
   * @param {string} url 页面路径
   * @param {Object} params 参数对象
   */
  redirectTo(url, params = {}) {
    let fullUrl = url
    
    if (Object.keys(params).length > 0) {
      const queryString = Object.keys(params)
        .map(key => `${key}=${encodeURIComponent(params[key])}`)
        .join('&')
      fullUrl += (url.includes('?') ? '&' : '?') + queryString
    }
    
    wx.redirectTo({
      url: fullUrl,
      fail: (error) => {
        console.error('页面重定向失败:', error)
        this.showError('页面跳转失败')
      }
    })
  },

  /**
   * 返回上一页
   * @param {number} delta 返回的页面数
   */
  navigateBack(delta = 1) {
    wx.navigateBack({
      delta,
      fail: (error) => {
        console.error('页面返回失败:', error)
      }
    })
  },

  /**
   * 切换到TabBar页面
   * @param {string} url TabBar页面路径
   */
  switchTab(url) {
    wx.switchTab({
      url,
      fail: (error) => {
        console.error('TabBar切换失败:', error)
        this.showError('页面切换失败')
      }
    })
  },

  /**
   * 获取当前页面路径
   * @returns {string} 当前页面路径
   */
  getCurrentPagePath() {
    const pages = getCurrentPages()
    if (pages.length > 0) {
      return pages[pages.length - 1].route
    }
    return ''
  },

  /**
   * 获取当前页面参数
   * @returns {Object} 当前页面参数
   */
  getCurrentPageOptions() {
    const pages = getCurrentPages()
    if (pages.length > 0) {
      return pages[pages.length - 1].options || {}
    }
    return {}
  },

  /**
   * 数组去重
   * @param {Array} array 原数组
   * @param {string} key 去重的键名（可选）
   * @returns {Array} 去重后的数组
   */
  uniqueArray(array, key = null) {
    if (!Array.isArray(array)) return []
    
    if (key) {
      const seen = new Set()
      return array.filter(item => {
        const value = item[key]
        if (seen.has(value)) {
          return false
        }
        seen.add(value)
        return true
      })
    } else {
      return [...new Set(array)]
    }
  },

  /**
   * 数组分组
   * @param {Array} array 原数组
   * @param {string|Function} key 分组键或分组函数
   * @returns {Object} 分组后的对象
   */
  groupBy(array, key) {
    if (!Array.isArray(array)) return {}
    
    return array.reduce((groups, item) => {
      const groupKey = typeof key === 'function' ? key(item) : item[key]
      if (!groups[groupKey]) {
        groups[groupKey] = []
      }
      groups[groupKey].push(item)
      return groups
    }, {})
  }
}

module.exports = utils
