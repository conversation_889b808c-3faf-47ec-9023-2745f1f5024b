<execution>
  <constraint>
    ## 工业系统开发约束
    - **数据安全约束**：生产数据必须加密传输和存储
    - **实时性约束**：关键生产数据延迟不超过3秒
    - **可用性约束**：系统可用性不低于99.5%
    - **兼容性约束**：必须兼容主流工业系统接口标准
    - **合规性约束**：符合工业数据安全和隐私保护法规
  </constraint>

  <rule>
    ## 开发强制规则
    - **数据库设计**：必须按照工业标准设计数据模型
    - **API设计**：必须遵循RESTful规范和工业接口标准
    - **权限控制**：必须实现基于角色的细粒度权限控制
    - **错误处理**：必须有完善的异常处理和日志记录
    - **性能要求**：页面加载时间不超过2秒
    - **安全规范**：所有敏感操作必须有审计日志
  </rule>

  <guideline>
    ## 开发指导原则
    - **业务优先**：技术选择服务于业务需求
    - **用户体验**：界面设计符合工业现场操作习惯
    - **渐进式开发**：从核心功能开始，逐步完善
    - **标准化优先**：优先使用行业标准和最佳实践
    - **可维护性**：代码结构清晰，便于后期维护
    - **可扩展性**：架构设计考虑未来功能扩展
  </guideline>

  <process>
    ## 工业小程序开发流程
    
    ### Phase 1: 需求分析与设计 (1周)
    ```mermaid
    flowchart TD
        A[业务需求调研] --> B[系统架构设计]
        B --> C[数据库设计]
        C --> D[接口设计]
        D --> E[UI/UX设计]
        E --> F[技术方案确认]
    ```
    
    #### 具体执行步骤
    1. **业务流程梳理**
       - 调研现有生产管理流程
       - 识别数字化改造痛点
       - 确定核心功能需求
       - 制定功能优先级
    
    2. **技术架构设计**
       - 确定小程序 + CloudBase架构
       - 设计微服务拆分策略
       - 规划数据同步方案
       - 制定安全策略
    
    3. **数据模型设计**
       ```mermaid
       erDiagram
           USER ||--o{ ORDER : creates
           ORDER ||--|{ ORDER_ITEM : contains
           ORDER_ITEM }|--|| PRODUCT : references
           PRODUCT ||--o{ INVENTORY : has
           INVENTORY }|--|| WAREHOUSE : stored_in
           ORDER ||--o{ QUALITY_CHECK : requires
       ```
    
    ### Phase 2: 核心功能开发 (3周)
    ```mermaid
    gantt
        title 核心功能开发计划
        dateFormat  YYYY-MM-DD
        section 前端开发
        页面框架搭建    :active, frontend1, 2024-01-08, 3d
        核心页面开发    :frontend2, after frontend1, 5d
        组件库开发      :frontend3, after frontend2, 3d
        
        section 后端开发
        云函数开发      :backend1, 2024-01-08, 4d
        数据库设计      :backend2, after backend1, 3d
        API接口开发     :backend3, after backend2, 4d
        
        section 集成测试
        功能测试        :test1, after frontend3, 2d
        集成测试        :test2, after test1, 2d
        性能测试        :test3, after test2, 1d
    ```
    
    #### 开发优先级
    1. **第一优先级**：生产计划管理、库存查询、质量记录
    2. **第二优先级**：设备监控、报表统计、权限管理
    3. **第三优先级**：数据分析、移动审批、消息推送
    
    ### Phase 3: 系统集成与优化 (2周)
    ```mermaid
    flowchart LR
        A[ERP系统集成] --> B[MES系统集成]
        B --> C[WMS系统集成]
        C --> D[QMS系统集成]
        D --> E[IoTS系统集成]
        E --> F[数据同步测试]
        F --> G[性能优化]
        G --> H[安全加固]
    ```
    
    #### 集成要点
    - **数据映射**：建立系统间数据字段映射关系
    - **接口适配**：开发适配器处理不同系统接口差异
    - **错误处理**：设计集成失败的降级和重试机制
    - **监控告警**：建立集成状态监控和异常告警
    
    ### Phase 4: 测试部署上线 (1周)
    ```mermaid
    graph TD
        A[功能测试] --> B[性能测试]
        B --> C[安全测试]
        C --> D[用户验收测试]
        D --> E[生产环境部署]
        E --> F[用户培训]
        F --> G[正式上线]
    ```
    
    #### 部署检查清单
    - [ ] 数据库备份策略确认
    - [ ] 云函数性能监控配置
    - [ ] 安全策略和权限配置验证
    - [ ] 第三方系统连接测试
    - [ ] 用户培训材料准备
    - [ ] 应急预案制定
    - [ ] 上线回滚方案确认
  </process>

  <criteria>
    ## 开发质量标准
    
    ### 功能完整性
    - ✅ 核心业务流程100%覆盖
    - ✅ 用户权限体系完善
    - ✅ 数据同步机制稳定
    - ✅ 异常处理机制完备
    
    ### 性能指标
    - ✅ 页面加载时间 < 2秒
    - ✅ 接口响应时间 < 1秒
    - ✅ 系统可用性 > 99.5%
    - ✅ 并发用户数 > 100
    
    ### 安全标准
    - ✅ 数据传输加密
    - ✅ 身份认证机制
    - ✅ 权限控制完善
    - ✅ 操作审计日志
    
    ### 用户体验
    - ✅ 界面简洁直观
    - ✅ 操作流程顺畅
    - ✅ 错误提示友好
    - ✅ 离线功能可用
    
    ### 可维护性
    - ✅ 代码结构清晰
    - ✅ 文档完整详细
    - ✅ 测试覆盖率 > 80%
    - ✅ 部署自动化
  </criteria>
</execution>
