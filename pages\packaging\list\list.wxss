/* 生产管理系统 - 任务管理页面样式 - 简约现代风格 */
@import '../../../styles/modern-simple.wxss';

.page-container {
  min-height: 100vh;
  background-color: var(--bg-page);
  padding-bottom: 40rpx;
}

/* 页面头部操作区 - 简约现代风格 */
.header-actions {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 20rpx;
  background-color: #FFFFFF;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.btn {
  height: 80rpx;
  border-radius: 12rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  font-size: 28rpx;
  padding: 0 24rpx;
  min-width: 160rpx;
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-base) var(--ease-in-out);
  box-shadow: var(--shadow-xs);
}

.btn-primary {
  background-color: #007AFF;
  color: #FFFFFF;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.btn-primary:active {
  background-color: #0056CC;
  transform: scale(0.98);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  background-color: #F2F2F7;
  color: #1D1D1F;
  border: 2rpx solid #E5E5EA;
}

.btn-secondary:active {
  background-color: #E5E5EA;
  transform: scale(0.98);
}

/* 搜索栏 - 简约现代风格 */
.search-container {
  background-color: var(--bg-primary);
  padding: var(--spacing-lg) var(--page-padding);
  margin-bottom: var(--spacing-sm);
}

.search-box {
  display: flex;
  align-items: center;
  background-color: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: 0 var(--spacing-lg);
  height: 80rpx;
  box-shadow: var(--shadow-xs);
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: var(--text-primary);
  height: 100%;
}

.clear-btn {
  font-size: 24rpx;
  color: var(--text-secondary);
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  background-color: var(--bg-tertiary);
}

/* 筛选栏 - 简约现代风格 */
.filter-container {
  background-color: var(--bg-primary);
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  border-radius: var(--radius-lg);
  margin: 0 var(--page-padding) var(--spacing-lg);
  box-shadow: var(--shadow-xs);
}

.filter-scroll {
  flex: 1;
  white-space: nowrap;
}

.filter-tabs {
  display: flex;
  padding: 0 20rpx;
}

.filter-tab {
  padding: 24rpx 32rpx;
  font-size: 28rpx;
  color: var(--text-secondary);
  white-space: nowrap;
  position: relative;
  transition: all 0.2s ease;
}

.filter-tab.active {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

.filter-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: var(--primary-color);
  border-radius: 2rpx;
}

/* 任务列表 */
.task-list {
  padding: 0 var(--page-padding);
}

/* 任务卡片 - 简约现代风格 */
.task-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #E5E5EA;
  transition: all 0.3s ease;
}

.task-card:active {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.12);
}

/* 任务头部 */
.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-lg);
}

.task-name {
  font-size: 32rpx;
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  line-height: 1.4;
  flex: 1;
  margin-right: var(--spacing-md);
}

.task-status {
  font-size: 24rpx;
  padding: 8rpx 24rpx;
  border-radius: 40rpx;
  font-weight: 500;
  white-space: nowrap;
}

.task-status.pending {
  background-color: #FFF3CD;
  color: #FF9500;
}

.task-status.in_progress {
  background-color: #E3F2FD;
  color: #007AFF;
}

.task-status.completed {
  background-color: #E8F5E8;
  color: #34C759;
}

.task-status.paused {
  background-color: #FFEBEE;
  color: #FF3B30;
}

/* 工单信息区域 */
.workorder-section {
  margin-bottom: var(--spacing-lg);
}

.section-title {
  margin-bottom: var(--spacing-md);
}

.title-text {
  font-size: 24rpx;
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
}

.workorder-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.workorder-no {
  font-size: 28rpx;
  font-weight: var(--font-weight-bold);
  color: #007AFF;
}

.product-name,
.product-code {
  font-size: 26rpx;
  color: var(--text-secondary);
}

/* 进度信息区域 */
.progress-section {
  margin-bottom: var(--spacing-lg);
}

.progress-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.progress-main {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.progress-bar {
  flex: 1;
  height: 8rpx;
  background-color: var(--bg-tertiary);
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-fill.pending {
  background-color: #FF9500;
}

.progress-fill.in_progress {
  background-color: #007AFF;
}

.progress-fill.completed {
  background-color: #34C759;
}

.progress-fill.paused {
  background-color: #FF3B30;
}

.progress-text {
  font-size: 24rpx;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  min-width: 60rpx;
}

.progress-data {
  display: flex;
  gap: var(--spacing-lg);
}

.data-item {
  font-size: 22rpx;
  color: var(--text-secondary);
}

.data-item.qualified {
  color: #34C759;
}

.data-item.defective {
  color: #FF3B30;
}

/* 分配信息区域 */
.assignment-section {
  margin-bottom: var(--spacing-lg);
}

.assignment-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.assignment-status {
  font-size: 26rpx;
  font-weight: var(--font-weight-medium);
}

.assignment-status.assigned {
  color: #34C759;
}

.assignment-status.unassigned {
  color: #FF9500;
}

.workstation {
  font-size: 22rpx;
  color: var(--text-secondary);
}

/* 任务底部 */
.task-footer {
  margin-bottom: var(--spacing-lg);
  padding-top: var(--spacing-md);
  border-top: 1rpx solid var(--border-color);
}

.task-date {
  font-size: 22rpx;
  color: var(--text-secondary);
}

/* 操作按钮 */
.task-actions {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-base);
}

.task-actions .action-btn {
  flex: 1;
  height: var(--btn-height-sm);
  border-radius: var(--radius-base);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  border: none;
  transition: all var(--transition-base);
}

.task-actions .action-btn.primary {
  background-color: var(--primary-color);
  color: white;
}

.task-actions .action-btn.secondary {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: var(--border);
}

/* 空状态和加载状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-text {
  font-size: 32rpx;
  color: var(--text-secondary);
  display: block;
  margin-bottom: 16rpx;
}

.empty-hint {
  font-size: 26rpx;
  color: var(--text-tertiary);
  display: block;
}

.loading-state {
  text-align: center;
  padding: 40rpx;
}

.loading-text {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.load-more {
  text-align: center;
  padding: 40rpx;
}

.load-more-text {
  font-size: 26rpx;
  color: var(--text-tertiary);
}

/* 弹窗样式 - 简约现代风格 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filter-modal,
.assign-modal {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xl);
  border-bottom: 1rpx solid var(--border-color);
}

.modal-title {
  font-size: 32rpx;
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background: var(--bg-secondary);
  border: none;
  font-size: 32rpx;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close::after {
  border: none;
}

/* 筛选弹窗内容 */
.filter-content {
  padding: var(--spacing-xl);
  max-height: 60vh;
  overflow-y: auto;
}

.filter-section {
  margin-bottom: var(--spacing-xl);
}

.filter-section-title {
  font-size: 28rpx;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.filter-option {
  padding: 16rpx 24rpx;
  border-radius: var(--radius-md);
  font-size: 26rpx;
  color: var(--text-secondary);
  background-color: var(--bg-secondary);
  border: 1rpx solid var(--border-color);
  transition: all var(--transition-base) var(--ease-in-out);
}

.filter-option.active {
  color: #007AFF;
  background-color: rgba(0, 122, 255, 0.1);
  border-color: #007AFF;
}

.filter-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xl);
}

/* 分配弹窗内容 */
.assign-content {
  padding: var(--spacing-xl);
}

.task-info {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
}

.task-name {
  font-size: 30rpx;
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  display: block;
  margin-bottom: 8rpx;
}

.task-order {
  font-size: 26rpx;
  color: #007AFF;
}

.task-actions {
  display: flex;
  gap: 8rpx;
  flex-shrink: 0;
}

.action-btn {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 500;
  border: none;
  white-space: nowrap;
  min-width: 60rpx;
}

.action-btn::after {
  border: none;
}

.claim-btn {
  background: #34C759;
  color: white;
}

.assign-btn {
  background: #007AFF;
  color: white;
}

/* 时间信息 */
.time-info {
  display: flex;
  justify-content: space-between;
  padding-top: 16rpx;
  border-top: 1rpx solid #F2F2F7;
  flex-wrap: wrap;
  gap: 8rpx;
}

.time-item {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.time-label {
  font-size: 20rpx;
  color: #8E8E93;
  margin-right: 6rpx;
  white-space: nowrap;
}

.time-value {
  font-size: 22rpx;
  color: #1D1D1F;
  word-break: break-all;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #8E8E93;
  display: block;
  margin-bottom: 16rpx;
}

.empty-hint {
  font-size: 26rpx;
  color: #C7C7CC;
  display: block;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 40rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #8E8E93;
}

.load-more {
  text-align: center;
  padding: 40rpx;
}

.load-more-text {
  font-size: 26rpx;
  color: #C7C7CC;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-modal,
.filter-modal,
.assign-modal {
  background: white;
  border-radius: 20rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 2rpx solid #F2F2F7;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1D1D1F;
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background: #F2F2F7;
  border: none;
  font-size: 32rpx;
  color: #8E8E93;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close::after {
  border: none;
}

/* 搜索弹窗内容 */
.search-content {
  padding: 32rpx;
}

.search-input-container {
  position: relative;
  margin-bottom: 40rpx;
}

.search-input-modal {
  width: 100%;
  padding: 24rpx;
  background: #F8F9FA;
  border: 2rpx solid #E9ECEF;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #1D1D1F;
}

.clear-search-btn {
  position: absolute;
  right: 16rpx;
  top: 50%;
  transform: translateY(-50%);
  background: #8E8E93;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 8rpx 16rpx;
  font-size: 22rpx;
}

.clear-search-btn::after {
  border: none;
}

.search-actions {
  display: flex;
  gap: 20rpx;
}

.search-confirm-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  background: #007AFF;
  color: white;
}

.search-confirm-btn::after {
  border: none;
}

/* 筛选弹窗内容 */
.filter-content {
  padding: 32rpx;
}

.filter-item {
  margin-bottom: 32rpx;
}

.filter-label {
  font-size: 28rpx;
  color: #1D1D1F;
  font-weight: 500;
  display: block;
  margin-bottom: 16rpx;
}

.picker-display {
  padding: 24rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #1D1D1F;
  border: 2rpx solid transparent;
}

/* 分配弹窗内容 */
.assign-content {
  padding: 32rpx;
}

.task-info {
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
}

.task-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #1D1D1F;
  display: block;
  margin-bottom: 8rpx;
}

.task-order {
  font-size: 26rpx;
  color: #007AFF;
}

.employee-select {
  margin-bottom: 40rpx;
}

.select-label {
  font-size: 28rpx;
  color: #1D1D1F;
  font-weight: 500;
  display: block;
  margin-bottom: 16rpx;
}

.modal-actions {
  display: flex;
  gap: 20rpx;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
}

.cancel-btn {
  background: #F2F2F7;
  color: #8E8E93;
}

.confirm-btn {
  background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
  color: white;
}

.cancel-btn::after,
.confirm-btn::after {
  border: none;
}

/* 响应式设计 */
@media screen and (max-width: 375px) {
  .task-card {
    padding: 20rpx;
    margin-bottom: 16rpx;
  }

  .task-title {
    font-size: 28rpx;
  }

  .work-order {
    font-size: 22rpx;
  }

  .product-name {
    font-size: 20rpx;
  }

  .data-label {
    font-size: 18rpx;
  }

  .data-value {
    font-size: 22rpx;
  }

  .assignment-label {
    font-size: 20rpx;
  }

  .assignment-value {
    font-size: 22rpx;
  }

  .action-btn {
    padding: 6rpx 12rpx;
    font-size: 20rpx;
    min-width: 50rpx;
  }

  .time-label {
    font-size: 18rpx;
  }

  .time-value {
    font-size: 20rpx;
  }

  .task-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 8rpx;
  }

  .task-actions {
    align-self: flex-end;
  }
}
