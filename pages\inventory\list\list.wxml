<!--库存列表页面-->
<view class="page-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">库存管理</text>
    <text class="page-subtitle">产品库存查询</text>
  </view>

  <!-- 库存列表 -->
  <view class="inventory-list">
    <view class="inventory-card" wx:for="{{ inventoryList }}" wx:key="id">
      <view class="inventory-header">
        <view class="product-info">
          <text class="product-name">{{ item.productName }}</text>
          <text class="product-code">{{ item.productCode }}</text>
        </view>
        <view class="inventory-status {{ item.status }}">
          {{ item.statusText }}
        </view>
      </view>
      
      <view class="inventory-details">
        <view class="detail-item">
          <text class="detail-label">当前库存</text>
          <text class="detail-value">{{ item.currentStock }} {{ item.unit }}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">安全库存</text>
          <text class="detail-value">{{ item.safetyStock }} {{ item.unit }}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">最后更新</text>
          <text class="detail-value">{{ item.lastUpdateTime }}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{ inventoryList.length === 0 }}">
    <view class="empty-icon">📦</view>
    <text class="empty-text">暂无库存数据</text>
  </view>
</view>
