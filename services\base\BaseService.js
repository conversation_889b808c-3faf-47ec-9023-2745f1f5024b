/**
 * 服务层基类
 * 提供统一的数据处理、错误处理、响应格式等功能
 */

class BaseService {
  constructor(entityName = 'entity') {
    this.entityName = entityName
    this.storageKey = `erp_${entityName.toLowerCase()}`
  }

  /**
   * 生成统一的响应格式
   * @param {boolean} success 是否成功
   * @param {any} data 数据
   * @param {string} message 消息
   * @param {string} code 错误码
   * @returns {Object} 统一响应格式
   */
  createResponse(success, data = null, message = '', code = null) {
    const response = {
      success,
      message: message || (success ? '操作成功' : '操作失败')
    }
    
    if (success) {
      response.data = data
    } else {
      response.code = code
    }
    
    return response
  }

  /**
   * 模拟异步操作
   * @param {Function} operation 操作函数
   * @param {number} delay 延迟时间（毫秒）
   * @returns {Promise} Promise对象
   */
  async simulateAsync(operation, delay = 300) {
    return new Promise((resolve) => {
      setTimeout(() => {
        try {
          const result = operation()
          resolve(result)
        } catch (error) {
          resolve(this.createResponse(false, null, error.message, 'OPERATION_ERROR'))
        }
      }, delay)
    })
  }

  /**
   * 通用数据过滤
   * @param {Array} data 原始数据
   * @param {Object} filters 过滤条件
   * @returns {Array} 过滤后的数据
   */
  filterData(data, filters = {}) {
    let result = [...data]

    // 关键词搜索
    if (filters.keyword) {
      const keyword = filters.keyword.toLowerCase()
      result = result.filter(item => this.matchKeyword(item, keyword))
    }

    // 状态过滤
    if (filters.status && filters.status !== 'all') {
      result = result.filter(item => item.status === filters.status)
    }

    // 日期范围过滤
    if (filters.startDate || filters.endDate) {
      result = result.filter(item => this.matchDateRange(item, filters.startDate, filters.endDate))
    }

    // 自定义过滤器
    if (filters.customFilter && typeof filters.customFilter === 'function') {
      result = result.filter(filters.customFilter)
    }

    return result
  }

  /**
   * 关键词匹配（子类可重写）
   * @param {Object} item 数据项
   * @param {string} keyword 关键词
   * @returns {boolean} 是否匹配
   */
  matchKeyword(item, keyword) {
    // 默认匹配 name, title, description 等常见字段
    const searchFields = ['name', 'title', 'description', 'code', 'no']
    return searchFields.some(field => {
      const value = item[field]
      return value && value.toString().toLowerCase().includes(keyword)
    })
  }

  /**
   * 日期范围匹配
   * @param {Object} item 数据项
   * @param {string} startDate 开始日期
   * @param {string} endDate 结束日期
   * @returns {boolean} 是否在范围内
   */
  matchDateRange(item, startDate, endDate) {
    const itemDate = new Date(item.createdAt || item.updatedAt)
    const start = startDate ? new Date(startDate) : null
    const end = endDate ? new Date(endDate) : null

    if (start && itemDate < start) return false
    if (end && itemDate > end) return false
    return true
  }

  /**
   * 数据排序
   * @param {Array} data 数据数组
   * @param {string} sortBy 排序字段
   * @param {string} sortOrder 排序方向 (asc/desc)
   * @returns {Array} 排序后的数据
   */
  sortData(data, sortBy = 'createdAt', sortOrder = 'desc') {
    return data.sort((a, b) => {
      const aValue = a[sortBy]
      const bValue = b[sortBy]

      if (sortOrder === 'desc') {
        return bValue > aValue ? 1 : -1
      } else {
        return aValue > bValue ? 1 : -1
      }
    })
  }

  /**
   * 数据分页
   * @param {Array} data 数据数组
   * @param {number} page 页码
   * @param {number} pageSize 每页大小
   * @returns {Object} 分页结果
   */
  paginateData(data, page = 1, pageSize = 10) {
    const total = data.length
    const totalPages = Math.ceil(total / pageSize)
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const items = data.slice(startIndex, endIndex)

    return {
      items,
      pagination: {
        page,
        pageSize,
        total,
        totalPages,
        hasMore: page < totalPages
      }
    }
  }

  /**
   * 生成唯一ID
   * @param {string} prefix 前缀
   * @returns {string} 唯一ID
   */
  generateId(prefix = this.entityName.toLowerCase()) {
    const timestamp = Date.now().toString(36)
    const random = Math.random().toString(36).substr(2, 5)
    return `${prefix}_${timestamp}_${random}`
  }

  /**
   * 数据验证
   * @param {Object} data 要验证的数据
   * @param {Object} rules 验证规则
   * @returns {Object} 验证结果
   */
  validateData(data, rules) {
    const errors = []

    for (const field in rules) {
      const rule = rules[field]
      const value = data[field]

      // 必填验证
      if (rule.required && (!value || value.toString().trim() === '')) {
        errors.push(`${rule.label || field}不能为空`)
        continue
      }

      // 如果字段为空且非必填，跳过其他验证
      if (!value) continue

      // 类型验证
      if (rule.type && typeof value !== rule.type) {
        errors.push(`${rule.label || field}类型不正确`)
      }

      // 长度验证
      if (rule.minLength && value.toString().length < rule.minLength) {
        errors.push(`${rule.label || field}长度不能少于${rule.minLength}个字符`)
      }
      if (rule.maxLength && value.toString().length > rule.maxLength) {
        errors.push(`${rule.label || field}长度不能超过${rule.maxLength}个字符`)
      }

      // 正则验证
      if (rule.pattern && !rule.pattern.test(value)) {
        errors.push(`${rule.label || field}格式不正确`)
      }

      // 自定义验证
      if (rule.validator && typeof rule.validator === 'function') {
        const customResult = rule.validator(value, data)
        if (customResult !== true) {
          errors.push(customResult || `${rule.label || field}验证失败`)
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * 从本地存储获取数据
   * @returns {Array} 数据数组
   */
  getDataFromStorage() {
    try {
      const data = wx.getStorageSync(this.storageKey)
      return Array.isArray(data) ? data : []
    } catch (error) {
      console.error(`获取${this.entityName}数据失败:`, error)
      return []
    }
  }

  /**
   * 保存数据到本地存储
   * @param {Array} data 数据数组
   * @returns {boolean} 是否成功
   */
  saveDataToStorage(data) {
    try {
      wx.setStorageSync(this.storageKey, data)
      return true
    } catch (error) {
      console.error(`保存${this.entityName}数据失败:`, error)
      return false
    }
  }

  /**
   * 通用列表查询方法
   * @param {Object} params 查询参数
   * @returns {Promise} 查询结果
   */
  async getList(params = {}) {
    return this.simulateAsync(() => {
      const data = this.getDataFromStorage()
      
      // 过滤数据
      const filteredData = this.filterData(data, params)
      
      // 排序
      const sortedData = this.sortData(filteredData, params.sortBy, params.sortOrder)
      
      // 分页
      const result = this.paginateData(sortedData, params.page, params.pageSize)
      
      return this.createResponse(true, result.items, '查询成功', null)
    })
  }

  /**
   * 通用详情查询方法
   * @param {string} id ID
   * @returns {Promise} 查询结果
   */
  async getById(id) {
    return this.simulateAsync(() => {
      const data = this.getDataFromStorage()
      const item = data.find(item => item.id === id)
      
      if (item) {
        return this.createResponse(true, item, '查询成功')
      } else {
        return this.createResponse(false, null, `${this.entityName}不存在`, 'NOT_FOUND')
      }
    })
  }

  /**
   * 通用创建方法
   * @param {Object} itemData 创建数据
   * @param {Object} validationRules 验证规则
   * @returns {Promise} 创建结果
   */
  async create(itemData, validationRules = {}) {
    return this.simulateAsync(() => {
      // 数据验证
      if (Object.keys(validationRules).length > 0) {
        const validation = this.validateData(itemData, validationRules)
        if (!validation.valid) {
          return this.createResponse(false, null, validation.errors.join('; '), 'VALIDATION_ERROR')
        }
      }

      const data = this.getDataFromStorage()
      const newItem = {
        id: this.generateId(),
        ...itemData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      data.push(newItem)
      
      if (this.saveDataToStorage(data)) {
        return this.createResponse(true, newItem, '创建成功')
      } else {
        return this.createResponse(false, null, '保存失败', 'SAVE_ERROR')
      }
    })
  }

  /**
   * 通用更新方法
   * @param {string} id ID
   * @param {Object} updateData 更新数据
   * @param {Object} validationRules 验证规则
   * @returns {Promise} 更新结果
   */
  async update(id, updateData, validationRules = {}) {
    return this.simulateAsync(() => {
      // 数据验证
      if (Object.keys(validationRules).length > 0) {
        const validation = this.validateData(updateData, validationRules)
        if (!validation.valid) {
          return this.createResponse(false, null, validation.errors.join('; '), 'VALIDATION_ERROR')
        }
      }

      const data = this.getDataFromStorage()
      const index = data.findIndex(item => item.id === id)
      
      if (index === -1) {
        return this.createResponse(false, null, `${this.entityName}不存在`, 'NOT_FOUND')
      }

      const updatedItem = {
        ...data[index],
        ...updateData,
        updatedAt: new Date().toISOString()
      }
      
      data[index] = updatedItem
      
      if (this.saveDataToStorage(data)) {
        return this.createResponse(true, updatedItem, '更新成功')
      } else {
        return this.createResponse(false, null, '保存失败', 'SAVE_ERROR')
      }
    })
  }

  /**
   * 通用删除方法
   * @param {string} id ID
   * @returns {Promise} 删除结果
   */
  async delete(id) {
    return this.simulateAsync(() => {
      const data = this.getDataFromStorage()
      const index = data.findIndex(item => item.id === id)
      
      if (index === -1) {
        return this.createResponse(false, null, `${this.entityName}不存在`, 'NOT_FOUND')
      }

      data.splice(index, 1)
      
      if (this.saveDataToStorage(data)) {
        return this.createResponse(true, null, '删除成功')
      } else {
        return this.createResponse(false, null, '保存失败', 'SAVE_ERROR')
      }
    })
  }
}

module.exports = BaseService
