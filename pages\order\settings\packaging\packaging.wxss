/* 生产管理系统 - 包装管理页面样式 */
@import '../../../../styles/design-tokens.wxss';

.page-container {
  min-height: 100vh;
  background-color: var(--bg-page);
}

/* 页面头部 */
.page-header {
  background-color: var(--bg-primary);
  padding: var(--spacing-base);
  border-bottom: var(--border);
}

.btn {
  width: 100%;
  height: var(--btn-height-base);
  border-radius: var(--radius-base);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-base);
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:active {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: var(--border);
}

.btn-icon {
  font-size: var(--font-size-lg);
}

/* 包装列表 */
.packaging-list {
  padding: var(--spacing-base);
}

.packaging-card {
  background-color: var(--bg-primary);
  border-radius: var(--card-border-radius);
  padding: var(--card-padding);
  margin-bottom: var(--spacing-base);
  box-shadow: var(--card-shadow);
  border: var(--border);
}

.packaging-info {
  margin-bottom: var(--spacing-base);
}

.packaging-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-base);
}

.packaging-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  flex: 1;
}

.packaging-type {
  font-size: var(--font-size-sm);
  color: var(--warning-color);
  background-color: var(--warning-light);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-weight: var(--font-weight-medium);
}

.packaging-description {
  margin-bottom: var(--spacing-base);
  padding: var(--spacing-sm);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-sm);
  border-left: 4rpx solid var(--primary-color);
}

.description-text {
  font-size: var(--font-size-base);
  color: var(--text-primary);
  line-height: var(--line-height-relaxed);
}

.packaging-specifications {
  margin-bottom: var(--spacing-base);
}

.spec-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.spec-text {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  line-height: var(--line-height-relaxed);
}

.packaging-notes {
  padding: var(--spacing-sm);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-sm);
  border-left: 4rpx solid var(--success-color);
}

.notes-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
}

.packaging-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.action-btn {
  flex: 1;
  height: var(--btn-height-sm);
  border-radius: var(--radius-base);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  border: none;
  transition: all var(--transition-base);
}

.action-btn.secondary {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: var(--border);
}

.action-btn.danger {
  background-color: var(--error-light);
  color: var(--error-color);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4xl);
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: var(--spacing-base);
}

.empty-text {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.empty-hint {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
}

/* 表单弹窗 */
.form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: var(--z-index-modal);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-base);
}

.form-container {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xl);
  border-bottom: var(--border);
}

.form-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: var(--radius-full);
  background-color: var(--bg-secondary);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
  color: var(--text-tertiary);
}

.form-content {
  flex: 1;
  padding: var(--spacing-xl);
  overflow-y: auto;
}

.form-group {
  margin-bottom: var(--spacing-xl);
}

.form-label {
  display: block;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.form-input {
  width: 100%;
  height: var(--input-height);
  padding: 0 var(--input-padding);
  border: var(--border);
  border-radius: var(--input-border-radius);
  font-size: var(--font-size-base);
  color: var(--text-primary);
  background-color: var(--bg-primary);
}

.form-input:focus {
  border-color: var(--primary-color);
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: var(--input-padding);
  border: var(--border);
  border-radius: var(--input-border-radius);
  font-size: var(--font-size-base);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  resize: vertical;
}

.form-textarea:focus {
  border-color: var(--primary-color);
}

.form-picker {
  width: 100%;
}

.picker-display {
  height: var(--input-height);
  padding: 0 var(--input-padding);
  border: var(--border);
  border-radius: var(--input-border-radius);
  font-size: var(--font-size-base);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  display: flex;
  align-items: center;
}

.form-actions {
  display: flex;
  gap: var(--spacing-base);
  padding: var(--spacing-xl);
  border-top: var(--border);
}

.form-actions .btn {
  flex: 1;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: var(--z-index-modal);
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  background-color: var(--bg-primary);
  padding: var(--spacing-2xl);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
}

.loading-text {
  font-size: var(--font-size-base);
  color: var(--text-primary);
}
