<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生产管理系统 - 设计风格预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
            min-height: 100vh;
            padding: 40px 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }
        
        .header h1 {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 16px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .header p {
            font-size: 18px;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .styles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
            margin-bottom: 60px;
        }
        
        .style-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        
        .style-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: var(--accent-color, #007AFF);
        }
        
        .style-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
        }
        
        .style-header {
            margin-bottom: 20px;
        }
        
        .style-title {
            font-size: 24px;
            font-weight: 700;
            color: #1a1a1a;
            margin-bottom: 8px;
        }
        
        .style-subtitle {
            font-size: 14px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .style-description {
            margin-bottom: 24px;
            color: #555;
            line-height: 1.6;
        }
        
        .style-features {
            margin-bottom: 24px;
        }
        
        .style-features h4 {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }
        
        .features-list {
            list-style: none;
        }
        
        .features-list li {
            padding: 4px 0;
            color: #666;
            font-size: 14px;
        }
        
        .features-list li::before {
            content: '✓';
            color: var(--accent-color, #007AFF);
            font-weight: bold;
            margin-right: 8px;
        }
        
        .style-colors {
            display: flex;
            gap: 8px;
            margin-bottom: 24px;
        }
        
        .color-dot {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .style-actions {
            display: flex;
            gap: 12px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            text-align: center;
            flex: 1;
        }
        
        .btn-primary {
            background: var(--accent-color, #007AFF);
            color: white;
        }
        
        .btn-secondary {
            background: #f5f5f5;
            color: #333;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        /* 各风格的主色调 */
        .style-modern {
            --accent-color: #007AFF;
        }
        
        .style-business {
            --accent-color: #1B365D;
        }
        
        .style-fresh {
            --accent-color: #00C896;
        }
        
        .style-industrial {
            --accent-color: #FF6B35;
        }
        
        .style-minimalist {
            --accent-color: #000000;
        }
        
        .footer {
            text-align: center;
            color: white;
            opacity: 0.8;
            margin-top: 40px;
        }
        
        .comparison-note {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 40px;
            text-align: center;
        }
        
        .comparison-note h3 {
            color: #333;
            margin-bottom: 12px;
        }
        
        .comparison-note p {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>生产管理系统</h1>
            <p>5种不同设计风格预览 - 为您的企业选择最合适的界面风格</p>
        </div>
        
        <div class="comparison-note">
            <h3>💡 设计说明</h3>
            <p>每种风格都针对不同的企业类型和用户群体设计，点击"预览"可查看完整效果，点击"选择"可应用到您的项目中</p>
        </div>
        
        <div class="styles-grid">
            <!-- 风格1：简约现代 -->
            <div class="style-card style-modern">
                <div class="style-header">
                    <h3 class="style-title">简约现代风格</h3>
                    <div class="style-subtitle">Modern & Clean</div>
                </div>
                
                <div class="style-description">
                    以白色为主，蓝色为辅，大量留白设计。适合追求简洁高效的现代企业，具有强烈的现代感和科技感。
                </div>
                
                <div class="style-features">
                    <h4>设计特点</h4>
                    <ul class="features-list">
                        <li>大量留白，视觉清爽</li>
                        <li>现代化圆角设计</li>
                        <li>清晰的信息层级</li>
                        <li>优雅的交互动效</li>
                    </ul>
                </div>
                
                <div class="style-colors">
                    <div class="color-dot" style="background-color: #007AFF;"></div>
                    <div class="color-dot" style="background-color: #FFFFFF; border-color: #ddd;"></div>
                    <div class="color-dot" style="background-color: #F2F2F7;"></div>
                    <div class="color-dot" style="background-color: #34C759;"></div>
                </div>
                
                <div class="style-actions">
                    <a href="style1-modern.html" class="btn btn-primary" target="_blank">预览</a>
                    <button class="btn btn-secondary" onclick="selectStyle('modern')">选择</button>
                </div>
            </div>
            
            <!-- 风格2：商务专业 -->
            <div class="style-card style-business">
                <div class="style-header">
                    <h3 class="style-title">商务专业风格</h3>
                    <div class="style-subtitle">Business & Professional</div>
                </div>
                
                <div class="style-description">
                    深蓝色主色调，金色点缀，传统商务配色。适合大型企业、传统制造业，体现正式、稳重、权威感。
                </div>
                
                <div class="style-features">
                    <h4>设计特点</h4>
                    <ul class="features-list">
                        <li>正式商务配色方案</li>
                        <li>权威感强的视觉层次</li>
                        <li>传统企业级界面</li>
                        <li>稳重可靠的设计语言</li>
                    </ul>
                </div>
                
                <div class="style-colors">
                    <div class="color-dot" style="background-color: #1B365D;"></div>
                    <div class="color-dot" style="background-color: #D4AF37;"></div>
                    <div class="color-dot" style="background-color: #2E5984;"></div>
                    <div class="color-dot" style="background-color: #F8F9FA;"></div>
                </div>
                
                <div class="style-actions">
                    <a href="style2-business.html" class="btn btn-primary" target="_blank">预览</a>
                    <button class="btn btn-secondary" onclick="selectStyle('business')">选择</button>
                </div>
            </div>
            
            <!-- 风格3：活泼清新 -->
            <div class="style-card style-fresh">
                <div class="style-header">
                    <h3 class="style-title">活泼清新风格</h3>
                    <div class="style-subtitle">Fresh & Vibrant</div>
                </div>
                
                <div class="style-description">
                    绿色主色调，彩虹色点缀，清新自然。适合创新型企业、年轻团队，充满活力和亲和力。
                </div>
                
                <div class="style-features">
                    <h4>设计特点</h4>
                    <ul class="features-list">
                        <li>活泼的色彩搭配</li>
                        <li>大圆角亲和设计</li>
                        <li>丰富的视觉元素</li>
                        <li>年轻化的交互体验</li>
                    </ul>
                </div>
                
                <div class="style-colors">
                    <div class="color-dot" style="background-color: #00C896;"></div>
                    <div class="color-dot" style="background-color: #00B4D8;"></div>
                    <div class="color-dot" style="background-color: #FF6B6B;"></div>
                    <div class="color-dot" style="background-color: #FFD43B;"></div>
                </div>
                
                <div class="style-actions">
                    <a href="style3-fresh.html" class="btn btn-primary" target="_blank">预览</a>
                    <button class="btn btn-secondary" onclick="selectStyle('fresh')">选择</button>
                </div>
            </div>
            
            <!-- 风格4：工业风格 -->
            <div class="style-card style-industrial">
                <div class="style-header">
                    <h3 class="style-title">工业风格</h3>
                    <div class="style-subtitle">Industrial & Robust</div>
                </div>
                
                <div class="style-description">
                    深灰色主色调，橙色警示色，金属质感。适合制造业、工厂、重工业企业，体现硬朗实用的工业美学。
                </div>
                
                <div class="style-features">
                    <h4>设计特点</h4>
                    <ul class="features-list">
                        <li>硬朗的工业设计语言</li>
                        <li>金属质感的视觉效果</li>
                        <li>实用主义的界面布局</li>
                        <li>强烈的技术感和专业感</li>
                    </ul>
                </div>
                
                <div class="style-colors">
                    <div class="color-dot" style="background-color: #FF6B35;"></div>
                    <div class="color-dot" style="background-color: #2C3E50;"></div>
                    <div class="color-dot" style="background-color: #95A5A6;"></div>
                    <div class="color-dot" style="background-color: #F39C12;"></div>
                </div>
                
                <div class="style-actions">
                    <a href="style4-industrial.html" class="btn btn-primary" target="_blank">预览</a>
                    <button class="btn btn-secondary" onclick="selectStyle('industrial')">选择</button>
                </div>
            </div>
            
            <!-- 风格5：极简主义 -->
            <div class="style-card style-minimalist">
                <div class="style-header">
                    <h3 class="style-title">极简主义风格</h3>
                    <div class="style-subtitle">Minimalist & Pure</div>
                </div>
                
                <div class="style-description">
                    黑白灰为主，单一强调色，极度简洁。适合追求极简美学的现代企业、设计公司，专注内容本身。
                </div>
                
                <div class="style-features">
                    <h4>设计特点</h4>
                    <ul class="features-list">
                        <li>极简的黑白灰配色</li>
                        <li>丰富的留白空间</li>
                        <li>纯粹的几何形态</li>
                        <li>专注内容的设计理念</li>
                    </ul>
                </div>
                
                <div class="style-colors">
                    <div class="color-dot" style="background-color: #000000;"></div>
                    <div class="color-dot" style="background-color: #FFFFFF; border-color: #ddd;"></div>
                    <div class="color-dot" style="background-color: #666666;"></div>
                    <div class="color-dot" style="background-color: #F0F0F0;"></div>
                </div>
                
                <div class="style-actions">
                    <a href="style5-minimalist.html" class="btn btn-primary" target="_blank">预览</a>
                    <button class="btn btn-secondary" onclick="selectStyle('minimalist')">选择</button>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>© 2024 生产管理系统设计预览 | 选择最适合您企业的设计风格</p>
        </div>
    </div>
    
    <script>
        function selectStyle(styleName) {
            alert(`您选择了"${getStyleDisplayName(styleName)}"风格！\n\n接下来我们将为您的小程序应用这种设计风格。`);
        }
        
        function getStyleDisplayName(styleName) {
            const styleNames = {
                'modern': '简约现代风格',
                'business': '商务专业风格', 
                'fresh': '活泼清新风格',
                'industrial': '工业风格',
                'minimalist': '极简主义风格'
            };
            return styleNames[styleName] || styleName;
        }
    </script>
</body>
</html>
