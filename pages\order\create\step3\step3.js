// 订单创建第三步：配置详情
const PackagingService = require('../../../../services/packagingService.js')

Page({
  data: {
    // 已选产品列表
    selectedProducts: [],
    
    // 包装选项
    packagingOptions: [],
    
    // 优先级选项
    priorityOptions: [
      { label: '普通', value: 'normal' },
      { label: '紧急', value: 'urgent' },
      { label: '特急', value: 'emergency' },
      { label: '试制', value: 'trial' }
    ],
    
    // 表单验证
    isFormValid: false
  },

  onLoad() {
    console.log('订单创建第三步页面加载')
    this.loadStepData()
    this.loadPackagingOptions()
    this.validateForm()
  },

  // 加载步骤数据
  loadStepData() {
    try {
      const stepData = wx.getStorageSync('orderStepData')
      if (stepData && stepData.selectedProducts) {
        const selectedProducts = stepData.selectedProducts.map(item => ({
          ...item,
          estimatedDays: item.estimatedDays || 1,
          priorityIndex: item.priorityIndex || 0,
          packagingIndex: item.packagingIndex || 0,
          specialRequirements: item.specialRequirements || '',
          requirementsLength: (item.specialRequirements || '').length,
          quantityError: ''
        }))
        
        this.setData({ selectedProducts })
      } else {
        wx.showToast({
          title: '数据加载失败，请重新选择产品',
          icon: 'none'
        })
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      }
    } catch (error) {
      console.error('加载步骤数据失败:', error)
    }
  },

  // 加载包装选项
  async loadPackagingOptions() {
    try {
      const packagingList = await PackagingService.getPackagingList()
      const packagingOptions = packagingList.map(packaging => ({
        id: packaging.id,
        name: packaging.name,
        description: packaging.description || packaging.type
      }))

      this.setData({ packagingOptions })
      console.log('包装选项加载成功:', packagingOptions)
      
      // 为没有包装的产品设置默认包装
      const selectedProducts = this.data.selectedProducts.map(item => {
        if (!item.packaging && packagingOptions.length > 0) {
          return {
            ...item,
            packaging: packagingOptions[0],
            packagingIndex: 0,
            requirementsLength: (item.specialRequirements || '').length
          }
        }
        return {
          ...item,
          requirementsLength: (item.specialRequirements || '').length
        }
      })
      
      this.setData({ selectedProducts })
    } catch (error) {
      console.error('加载包装选项失败:', error)
      // 使用默认包装选项
      const defaultOptions = [
        { id: 'pkg_standard', name: '标准包装', description: '常规纸箱包装' },
        { id: 'pkg_moisture', name: '防潮包装', description: '防潮膜+纸箱' },
        { id: 'pkg_wooden', name: '木箱包装', description: '出口木箱包装' },
        { id: 'pkg_custom', name: '定制包装', description: '按客户要求定制' }
      ]
      this.setData({ packagingOptions: defaultOptions })
    }
  },

  // 包装类型选择
  onPackagingChange(e) {
    const productId = e.currentTarget.dataset.id
    const index = parseInt(e.detail.value)
    const packaging = this.data.packagingOptions[index]
    
    const selectedProducts = this.data.selectedProducts.map(item => {
      if (item.id === productId) {
        return {
          ...item,
          packaging,
          packagingIndex: index
        }
      }
      return item
    })
    
    this.setData({ selectedProducts })
    this.validateForm()
  },

  // 生产天数调整
  increaseDays(e) {
    const productId = e.currentTarget.dataset.id
    this.updateDays(productId, 1)
  },

  decreaseDays(e) {
    const productId = e.currentTarget.dataset.id
    this.updateDays(productId, -1)
  },

  onDaysInput(e) {
    const productId = e.currentTarget.dataset.id
    const days = parseInt(e.detail.value) || 1
    this.setDays(productId, days)
  },

  updateDays(productId, delta) {
    const selectedProducts = this.data.selectedProducts.map(item => {
      if (item.id === productId) {
        const newDays = Math.max(1, Math.min(365, item.estimatedDays + delta))
        return { ...item, estimatedDays: newDays }
      }
      return item
    })
    
    this.setData({ selectedProducts })
  },

  setDays(productId, days) {
    const selectedProducts = this.data.selectedProducts.map(item => {
      if (item.id === productId) {
        return { ...item, estimatedDays: Math.max(1, Math.min(365, days)) }
      }
      return item
    })
    
    this.setData({ selectedProducts })
  },

  // 优先级选择
  onPriorityChange(e) {
    const productId = e.currentTarget.dataset.id
    const index = parseInt(e.detail.value)
    
    const selectedProducts = this.data.selectedProducts.map(item => {
      if (item.id === productId) {
        return {
          ...item,
          priorityIndex: index,
          priority: this.data.priorityOptions[index].value
        }
      }
      return item
    })
    
    this.setData({ selectedProducts })
  },

  // 特殊要求输入
  onRequirementsInput(e) {
    const productId = e.currentTarget.dataset.id
    const value = e.detail.value

    const selectedProducts = this.data.selectedProducts.map(item => {
      if (item.id === productId) {
        return {
          ...item,
          specialRequirements: value,
          requirementsLength: value.length
        }
      }
      return item
    })

    this.setData({ selectedProducts })
  },

  // 批量设置包装
  batchSetPackaging() {
    wx.showActionSheet({
      itemList: this.data.packagingOptions.map(p => p.name),
      success: (res) => {
        const packaging = this.data.packagingOptions[res.tapIndex]
        const selectedProducts = this.data.selectedProducts.map(item => ({
          ...item,
          packaging,
          packagingIndex: res.tapIndex
        }))
        
        this.setData({ selectedProducts })
        this.validateForm()
        
        wx.showToast({
          title: '批量设置成功',
          icon: 'success'
        })
      }
    })
  },

  // 批量设置天数
  batchSetDays() {
    wx.showModal({
      title: '批量设置生产天数',
      content: '请输入统一的生产天数（1-365天）',
      editable: true,
      placeholderText: '请输入天数',
      success: (res) => {
        if (res.confirm && res.content) {
          const days = parseInt(res.content)
          if (days >= 1 && days <= 365) {
            const selectedProducts = this.data.selectedProducts.map(item => ({
              ...item,
              estimatedDays: days
            }))
            
            this.setData({ selectedProducts })
            
            wx.showToast({
              title: '批量设置成功',
              icon: 'success'
            })
          } else {
            wx.showToast({
              title: '请输入1-365之间的数字',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  // 批量设置优先级
  batchSetPriority() {
    wx.showActionSheet({
      itemList: this.data.priorityOptions.map(p => p.label),
      success: (res) => {
        const selectedProducts = this.data.selectedProducts.map(item => ({
          ...item,
          priorityIndex: res.tapIndex,
          priority: this.data.priorityOptions[res.tapIndex].value
        }))
        
        this.setData({ selectedProducts })
        
        wx.showToast({
          title: '批量设置成功',
          icon: 'success'
        })
      }
    })
  },

  // 表单验证
  validateForm() {
    const isValid = this.data.selectedProducts.every(item => {
      return item.packaging && item.estimatedDays >= 1
    })
    
    this.setData({ isFormValid: isValid })
  },

  // 返回上一步
  goBack() {
    wx.navigateBack()
  },

  // 继续添加产品
  addMoreProducts() {
    wx.navigateBack({
      delta: 1
    })
  },

  // 下一步
  nextStep() {
    if (!this.data.isFormValid) {
      wx.showToast({
        title: '请完善产品配置信息',
        icon: 'none'
      })
      return
    }

    // 保存数据
    const previousData = wx.getStorageSync('orderStepData') || {}
    const stepData = {
      ...previousData,
      step: 3,
      selectedProducts: this.data.selectedProducts
    }
    wx.setStorageSync('orderStepData', stepData)

    // 跳转到第4步
    wx.navigateTo({
      url: '/pages/order/create/step4/step4'
    })
  }
})
