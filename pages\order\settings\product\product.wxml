<!--生产管理系统 - 产品管理页面-->
<view class="page-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <button class="btn btn-primary" bindtap="showAddProduct">
      <text class="btn-icon">➕</text>
      <text class="btn-text">新建产品</text>
    </button>
  </view>

  <!-- 产品列表 -->
  <view class="product-list" wx:if="{{ productList.length > 0 }}">
    <view 
      class="product-card"
      wx:for="{{ productList }}"
      wx:key="id"
    >
      <!-- 产品信息 -->
      <view class="product-info">
        <view class="product-header">
          <view class="product-name">{{ item.name }}</view>
          <view class="product-code">{{ item.code }}</view>
        </view>
        
        <view class="product-details">
          <view class="detail-item" wx:if="{{ item.specification }}">
            <text class="detail-label">规格：</text>
            <text class="detail-value">{{ item.specification }}</text>
          </view>
          
          <view class="detail-item">
            <text class="detail-label">单位：</text>
            <text class="detail-value">{{ item.unit }}</text>
          </view>
          
          <view class="detail-item" wx:if="{{ item.price > 0 }}">
            <text class="detail-label">参考价格：</text>
            <text class="detail-value price">¥{{ item.price }}</text>
          </view>
          
          <view class="detail-item" wx:if="{{ item.category }}">
            <text class="detail-label">分类：</text>
            <text class="detail-value">{{ item.category }}</text>
          </view>
        </view>
        
        <view class="product-notes" wx:if="{{ item.notes }}">
          <text class="notes-text">{{ item.notes }}</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="product-actions">
        <button 
          class="action-btn secondary"
          bindtap="editProduct"
          data-product="{{ item }}"
        >
          编辑
        </button>
        <button 
          class="action-btn danger"
          bindtap="deleteProduct"
          data-product="{{ item }}"
        >
          删除
        </button>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:else>
    <text class="empty-icon">📦</text>
    <text class="empty-text">暂无产品信息</text>
    <text class="empty-hint">点击上方按钮添加产品</text>
  </view>

  <!-- 添加/编辑表单弹窗 -->
  <view class="form-overlay" wx:if="{{ showAddForm }}" bindtap="hideForm">
    <view class="form-container" catchtap="true">
      <view class="form-header">
        <text class="form-title">{{ editingId ? '编辑产品' : '新建产品' }}</text>
        <button class="close-btn" bindtap="hideForm">✕</button>
      </view>

      <view class="form-content">
        <view class="form-group">
          <text class="form-label">产品名称 *</text>
          <input 
            class="form-input"
            value="{{ formData.name }}"
            placeholder="请输入产品名称"
            data-field="name"
            bindinput="onFormInput"
          />
        </view>

        <view class="form-group">
          <text class="form-label">产品编码 *</text>
          <input 
            class="form-input"
            value="{{ formData.code }}"
            placeholder="请输入产品编码"
            data-field="code"
            bindinput="onFormInput"
          />
        </view>

        <view class="form-group">
          <text class="form-label">产品规格</text>
          <textarea 
            class="form-textarea"
            value="{{ formData.specification }}"
            placeholder="请输入产品规格说明"
            data-field="specification"
            bindinput="onFormInput"
          />
        </view>

        <view class="form-group">
          <text class="form-label">计量单位 *</text>
          <picker 
            class="form-picker"
            mode="selector"
            range="{{ unitOptions }}"
            range-key="label"
            value="{{ formData.unit }}"
            data-field="unit"
            bindchange="onPickerChange"
          >
            <view class="picker-display">
              {{ formData.unit || '请选择计量单位' }}
            </view>
          </picker>
        </view>



        <view class="form-group">
          <text class="form-label">产品分类</text>
          <picker 
            class="form-picker"
            mode="selector"
            range="{{ categoryOptions }}"
            range-key="label"
            value="{{ formData.category }}"
            data-field="category"
            bindchange="onPickerChange"
          >
            <view class="picker-display">
              {{ formData.category || '请选择产品分类' }}
            </view>
          </picker>
        </view>

        <view class="form-group">
          <text class="form-label">备注信息</text>
          <textarea 
            class="form-textarea"
            value="{{ formData.notes }}"
            placeholder="请输入备注信息"
            data-field="notes"
            bindinput="onFormInput"
          />
        </view>
      </view>

      <view class="form-actions">
        <button class="btn btn-secondary" bindtap="hideForm">取消</button>
        <button class="btn btn-primary" bindtap="saveProduct" disabled="{{ loading }}">
          {{ loading ? '保存中...' : '保存' }}
        </button>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{ loading }}">
    <view class="loading-content">
      <text class="loading-text">处理中...</text>
    </view>
  </view>
</view>
