/**
 * 错误处理工具
 * 提供统一的错误处理和日志记录功能
 */

const { ERROR_CODE, ERROR_MESSAGE } = require('./constants')

/**
 * 错误处理器类
 */
class ErrorHandler {
  constructor() {
    this.errorLog = []
    this.maxLogSize = 100
  }

  /**
   * 记录错误日志
   * @param {Error|string} error 错误对象或错误消息
   * @param {string} context 错误上下文
   * @param {Object} extra 额外信息
   */
  log(error, context = '', extra = {}) {
    const errorInfo = {
      timestamp: new Date().toISOString(),
      context,
      message: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : null,
      extra
    }

    this.errorLog.push(errorInfo)
    
    // 限制日志大小
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog.shift()
    }

    // 输出到控制台
    console.error(`[${context}] ${errorInfo.message}`, errorInfo)
  }

  /**
   * 获取错误日志
   * @param {number} limit 限制数量
   * @returns {Array} 错误日志数组
   */
  getErrorLog(limit = 10) {
    return this.errorLog.slice(-limit)
  }

  /**
   * 清空错误日志
   */
  clearErrorLog() {
    this.errorLog = []
  }

  /**
   * 处理API错误
   * @param {Error} error 错误对象
   * @param {string} apiName API名称
   * @returns {Object} 标准化的错误响应
   */
  handleApiError(error, apiName = '') {
    this.log(error, `API_ERROR:${apiName}`)
    
    let errorCode = ERROR_CODE.UNKNOWN_ERROR
    let errorMessage = ERROR_MESSAGE[ERROR_CODE.UNKNOWN_ERROR]

    if (error.code && ERROR_MESSAGE[error.code]) {
      errorCode = error.code
      errorMessage = ERROR_MESSAGE[error.code]
    } else if (error.message) {
      errorMessage = error.message
    }

    return {
      success: false,
      code: errorCode,
      message: errorMessage,
      timestamp: new Date().toISOString()
    }
  }

  /**
   * 处理网络错误
   * @param {Error} error 错误对象
   * @returns {Object} 标准化的错误响应
   */
  handleNetworkError(error) {
    this.log(error, 'NETWORK_ERROR')
    
    return {
      success: false,
      code: ERROR_CODE.NETWORK_ERROR,
      message: ERROR_MESSAGE[ERROR_CODE.NETWORK_ERROR],
      timestamp: new Date().toISOString()
    }
  }

  /**
   * 处理验证错误
   * @param {Array|string} errors 验证错误
   * @returns {Object} 标准化的错误响应
   */
  handleValidationError(errors) {
    const errorMessage = Array.isArray(errors) ? errors.join('; ') : errors
    
    this.log(errorMessage, 'VALIDATION_ERROR')
    
    return {
      success: false,
      code: ERROR_CODE.VALIDATION_ERROR,
      message: errorMessage,
      timestamp: new Date().toISOString()
    }
  }

  /**
   * 处理业务逻辑错误
   * @param {string} code 错误码
   * @param {string} message 自定义错误消息
   * @returns {Object} 标准化的错误响应
   */
  handleBusinessError(code, message = '') {
    const errorMessage = message || ERROR_MESSAGE[code] || ERROR_MESSAGE[ERROR_CODE.UNKNOWN_ERROR]
    
    this.log(errorMessage, 'BUSINESS_ERROR', { code })
    
    return {
      success: false,
      code,
      message: errorMessage,
      timestamp: new Date().toISOString()
    }
  }

  /**
   * 显示错误提示
   * @param {string|Object} error 错误信息或错误对象
   * @param {number} duration 显示时长
   */
  showError(error, duration = 2000) {
    let message = ''
    
    if (typeof error === 'string') {
      message = error
    } else if (error && error.message) {
      message = error.message
    } else {
      message = '操作失败'
    }

    wx.showToast({
      title: message,
      icon: 'none',
      duration
    })
  }

  /**
   * 显示网络错误提示
   */
  showNetworkError() {
    this.showError('网络连接失败，请检查网络设置')
  }

  /**
   * 显示加载失败提示
   */
  showLoadError() {
    this.showError('数据加载失败，请重试')
  }

  /**
   * 显示保存失败提示
   */
  showSaveError() {
    this.showError('保存失败，请重试')
  }

  /**
   * 显示删除失败提示
   */
  showDeleteError() {
    this.showError('删除失败，请重试')
  }
}

// 创建全局错误处理器实例
const errorHandler = new ErrorHandler()

/**
 * 全局错误处理函数
 * @param {Error} error 错误对象
 * @param {string} context 错误上下文
 * @param {Object} options 选项
 */
function handleError(error, context = '', options = {}) {
  const { showToast = true, logError = true } = options
  
  if (logError) {
    errorHandler.log(error, context)
  }
  
  if (showToast) {
    errorHandler.showError(error)
  }
}

/**
 * 异步函数错误包装器
 * @param {Function} asyncFn 异步函数
 * @param {string} context 错误上下文
 * @returns {Function} 包装后的函数
 */
function wrapAsync(asyncFn, context = '') {
  return async function(...args) {
    try {
      return await asyncFn.apply(this, args)
    } catch (error) {
      handleError(error, context)
      throw error
    }
  }
}

/**
 * Promise错误处理
 * @param {Promise} promise Promise对象
 * @param {string} context 错误上下文
 * @returns {Promise} 处理后的Promise
 */
function handlePromise(promise, context = '') {
  return promise.catch(error => {
    handleError(error, context)
    throw error
  })
}

/**
 * 重试机制
 * @param {Function} fn 要重试的函数
 * @param {number} maxRetries 最大重试次数
 * @param {number} delay 重试延迟（毫秒）
 * @returns {Promise} 重试结果
 */
async function retry(fn, maxRetries = 3, delay = 1000) {
  let lastError
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error
      
      if (i === maxRetries) {
        break
      }
      
      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
  
  throw lastError
}

/**
 * 超时处理
 * @param {Promise} promise Promise对象
 * @param {number} timeout 超时时间（毫秒）
 * @returns {Promise} 带超时的Promise
 */
function withTimeout(promise, timeout = 10000) {
  return Promise.race([
    promise,
    new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error('请求超时'))
      }, timeout)
    })
  ])
}

/**
 * 安全执行函数
 * @param {Function} fn 要执行的函数
 * @param {any} defaultValue 默认返回值
 * @param {string} context 错误上下文
 * @returns {any} 执行结果或默认值
 */
function safeExecute(fn, defaultValue = null, context = '') {
  try {
    return fn()
  } catch (error) {
    handleError(error, context, { showToast: false })
    return defaultValue
  }
}

/**
 * 创建错误对象
 * @param {string} code 错误码
 * @param {string} message 错误消息
 * @param {Object} extra 额外信息
 * @returns {Error} 错误对象
 */
function createError(code, message = '', extra = {}) {
  const error = new Error(message || ERROR_MESSAGE[code] || ERROR_MESSAGE[ERROR_CODE.UNKNOWN_ERROR])
  error.code = code
  error.extra = extra
  return error
}

module.exports = {
  // 错误处理器实例
  errorHandler,
  
  // 错误处理函数
  handleError,
  wrapAsync,
  handlePromise,
  
  // 工具函数
  retry,
  withTimeout,
  safeExecute,
  createError,
  
  // 错误码和消息
  ERROR_CODE,
  ERROR_MESSAGE
}
