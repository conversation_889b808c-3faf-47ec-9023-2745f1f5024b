/**
 * 数据验证工具
 * 提供统一的数据验证功能
 */

const { REGEX } = require('./constants')

/**
 * 验证手机号
 * @param {string} phone 手机号
 * @returns {boolean} 是否有效
 */
function validatePhone(phone) {
  if (!phone || typeof phone !== 'string') return false
  return REGEX.PHONE.test(phone)
}

/**
 * 验证邮箱
 * @param {string} email 邮箱
 * @returns {boolean} 是否有效
 */
function validateEmail(email) {
  if (!email || typeof email !== 'string') return false
  return REGEX.EMAIL.test(email)
}

/**
 * 验证身份证号
 * @param {string} idCard 身份证号
 * @returns {boolean} 是否有效
 */
function validateIdCard(idCard) {
  if (!idCard || typeof idCard !== 'string') return false
  return REGEX.ID_CARD.test(idCard)
}

/**
 * 验证中文字符
 * @param {string} text 文本
 * @returns {boolean} 是否为中文
 */
function validateChinese(text) {
  if (!text || typeof text !== 'string') return false
  return REGEX.CHINESE.test(text)
}

/**
 * 验证数字
 * @param {string} value 值
 * @returns {boolean} 是否为数字
 */
function validateNumber(value) {
  if (value === null || value === undefined) return false
  return REGEX.NUMBER.test(value.toString())
}

/**
 * 验证小数
 * @param {string} value 值
 * @returns {boolean} 是否为小数
 */
function validateDecimal(value) {
  if (value === null || value === undefined) return false
  return REGEX.DECIMAL.test(value.toString())
}

/**
 * 验证URL
 * @param {string} url URL
 * @returns {boolean} 是否有效
 */
function validateUrl(url) {
  if (!url || typeof url !== 'string') return false
  return REGEX.URL.test(url)
}

/**
 * 验证必填字段
 * @param {any} value 值
 * @returns {boolean} 是否有效
 */
function validateRequired(value) {
  if (value === null || value === undefined) return false
  if (typeof value === 'string') return value.trim() !== ''
  if (Array.isArray(value)) return value.length > 0
  if (typeof value === 'object') return Object.keys(value).length > 0
  return true
}

/**
 * 验证字符串长度
 * @param {string} value 值
 * @param {number} min 最小长度
 * @param {number} max 最大长度
 * @returns {boolean} 是否有效
 */
function validateLength(value, min = 0, max = Infinity) {
  if (!value || typeof value !== 'string') return false
  const length = value.trim().length
  return length >= min && length <= max
}

/**
 * 验证数值范围
 * @param {number} value 值
 * @param {number} min 最小值
 * @param {number} max 最大值
 * @returns {boolean} 是否有效
 */
function validateRange(value, min = -Infinity, max = Infinity) {
  if (typeof value !== 'number' || isNaN(value)) return false
  return value >= min && value <= max
}

/**
 * 验证日期
 * @param {string|Date} date 日期
 * @returns {boolean} 是否有效
 */
function validateDate(date) {
  if (!date) return false
  const dateObj = new Date(date)
  return !isNaN(dateObj.getTime())
}

/**
   * 验证日期范围
   * @param {string|Date} date 日期
   * @param {string|Date} minDate 最小日期
   * @param {string|Date} maxDate 最大日期
   * @returns {boolean} 是否有效
   */
function validateDateRange(date, minDate = null, maxDate = null) {
  if (!validateDate(date)) return false
  
  const dateObj = new Date(date)
  
  if (minDate) {
    const minDateObj = new Date(minDate)
    if (dateObj < minDateObj) return false
  }
  
  if (maxDate) {
    const maxDateObj = new Date(maxDate)
    if (dateObj > maxDateObj) return false
  }
  
  return true
}

/**
 * 验证数组
 * @param {any} value 值
 * @param {number} minLength 最小长度
 * @param {number} maxLength 最大长度
 * @returns {boolean} 是否有效
 */
function validateArray(value, minLength = 0, maxLength = Infinity) {
  if (!Array.isArray(value)) return false
  return value.length >= minLength && value.length <= maxLength
}

/**
 * 验证对象
 * @param {any} value 值
 * @param {Array} requiredKeys 必需的键
 * @returns {boolean} 是否有效
 */
function validateObject(value, requiredKeys = []) {
  if (!value || typeof value !== 'object' || Array.isArray(value)) return false
  
  for (const key of requiredKeys) {
    if (!(key in value)) return false
  }
  
  return true
}

/**
 * 验证枚举值
 * @param {any} value 值
 * @param {Array} enumValues 枚举值数组
 * @returns {boolean} 是否有效
 */
function validateEnum(value, enumValues) {
  if (!Array.isArray(enumValues)) return false
  return enumValues.includes(value)
}

/**
 * 复合验证器
 * @param {any} value 要验证的值
 * @param {Array} validators 验证器数组
 * @returns {Object} 验证结果
 */
function validate(value, validators) {
  const errors = []
  
  for (const validator of validators) {
    const { type, options = {}, message } = validator
    let isValid = true
    
    switch (type) {
      case 'required':
        isValid = validateRequired(value)
        break
      case 'phone':
        isValid = validatePhone(value)
        break
      case 'email':
        isValid = validateEmail(value)
        break
      case 'idCard':
        isValid = validateIdCard(value)
        break
      case 'chinese':
        isValid = validateChinese(value)
        break
      case 'number':
        isValid = validateNumber(value)
        break
      case 'decimal':
        isValid = validateDecimal(value)
        break
      case 'url':
        isValid = validateUrl(value)
        break
      case 'length':
        isValid = validateLength(value, options.min, options.max)
        break
      case 'range':
        isValid = validateRange(value, options.min, options.max)
        break
      case 'date':
        isValid = validateDate(value)
        break
      case 'dateRange':
        isValid = validateDateRange(value, options.min, options.max)
        break
      case 'array':
        isValid = validateArray(value, options.minLength, options.maxLength)
        break
      case 'object':
        isValid = validateObject(value, options.requiredKeys)
        break
      case 'enum':
        isValid = validateEnum(value, options.values)
        break
      case 'custom':
        if (typeof options.validator === 'function') {
          isValid = options.validator(value)
        }
        break
      default:
        console.warn(`未知的验证器类型: ${type}`)
    }
    
    if (!isValid) {
      errors.push(message || `${type}验证失败`)
    }
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 批量验证对象的多个字段
 * @param {Object} data 要验证的数据对象
 * @param {Object} rules 验证规则对象
 * @returns {Object} 验证结果
 */
function validateObject(data, rules) {
  const errors = {}
  let hasError = false
  
  for (const field in rules) {
    const fieldRules = rules[field]
    const fieldValue = data[field]
    
    const result = validate(fieldValue, fieldRules)
    if (!result.valid) {
      errors[field] = result.errors
      hasError = true
    }
  }
  
  return {
    valid: !hasError,
    errors
  }
}

/**
 * 创建验证规则
 * @param {string} type 验证类型
 * @param {Object} options 选项
 * @param {string} message 错误消息
 * @returns {Object} 验证规则
 */
function createRule(type, options = {}, message = '') {
  return { type, options, message }
}

/**
 * 常用验证规则快捷方法
 */
const rules = {
  required: (message = '此字段为必填项') => createRule('required', {}, message),
  phone: (message = '请输入正确的手机号') => createRule('phone', {}, message),
  email: (message = '请输入正确的邮箱地址') => createRule('email', {}, message),
  idCard: (message = '请输入正确的身份证号') => createRule('idCard', {}, message),
  chinese: (message = '请输入中文字符') => createRule('chinese', {}, message),
  number: (message = '请输入数字') => createRule('number', {}, message),
  decimal: (message = '请输入数字') => createRule('decimal', {}, message),
  url: (message = '请输入正确的URL') => createRule('url', {}, message),
  length: (min, max, message = `长度应在${min}-${max}个字符之间`) => 
    createRule('length', { min, max }, message),
  range: (min, max, message = `数值应在${min}-${max}之间`) => 
    createRule('range', { min, max }, message),
  date: (message = '请输入正确的日期') => createRule('date', {}, message),
  dateRange: (min, max, message = '日期超出允许范围') => 
    createRule('dateRange', { min, max }, message),
  array: (minLength, maxLength, message = '数组长度不符合要求') => 
    createRule('array', { minLength, maxLength }, message),
  enum: (values, message = '请选择有效的选项') => 
    createRule('enum', { values }, message),
  custom: (validator, message = '验证失败') => 
    createRule('custom', { validator }, message)
}

module.exports = {
  // 单个验证函数
  validatePhone,
  validateEmail,
  validateIdCard,
  validateChinese,
  validateNumber,
  validateDecimal,
  validateUrl,
  validateRequired,
  validateLength,
  validateRange,
  validateDate,
  validateDateRange,
  validateArray,
  validateObject,
  validateEnum,
  
  // 复合验证
  validate,
  validateObject,
  
  // 规则创建
  createRule,
  rules
}
