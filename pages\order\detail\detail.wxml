<!--订单详情页面 - 简约现代风格-->
<view class="page-container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{ loading }}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 测试数据显示 -->
  <view class="test-info" wx:if="{{ !loading }}">
    <text>Loading: {{ loading }}</text>
    <text>OrderInfo: {{ orderInfo ? 'exists' : 'null' }}</text>
    <text wx:if="{{ orderInfo }}">Order No: {{ orderInfo.orderNo }}</text>
  </view>

  <!-- 订单详情内容 -->
  <view class="detail-content" wx:if="{{ !loading && orderInfo }}">
    <!-- 订单头部信息 -->
    <view class="detail-card">
      <view class="card-header">
        <view class="order-title">
          <text class="order-no" bindlongpress="copyOrderNo">{{ orderInfo.orderNo }}</text>
          <view class="order-status {{ orderInfo.status }}">
            {{ orderInfo.statusText }}
          </view>
        </view>
        <view class="order-meta">
          <text class="create-time">创建时间：{{ orderInfo.createdAtText }}</text>
          <text class="update-time">更新时间：{{ orderInfo.updatedAtText }}</text>
        </view>
      </view>
    </view>

    <!-- 客户信息 -->
    <view class="detail-card">
      <view class="card-title">
        <text class="title-text">客户信息</text>
      </view>
      <view class="card-content">
        <view class="info-row">
          <text class="info-label">客户名称</text>
          <text class="info-value">{{ orderInfo.customerName }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">联系人</text>
          <text class="info-value">{{ orderInfo.customerContact }}</text>
        </view>
        <view class="info-row" wx:if="{{ orderInfo.customerPhone }}">
          <text class="info-label">联系电话</text>
          <text class="info-value phone-number" bindtap="makePhoneCall">{{ orderInfo.customerPhone }}</text>
        </view>
      </view>
    </view>

    <!-- 产品信息 -->
    <view class="detail-card">
      <view class="card-title">
        <text class="title-text">产品信息</text>
      </view>
      <view class="card-content">
        <view class="info-row">
          <text class="info-label">产品名称</text>
          <text class="info-value">{{ orderInfo.productName }}</text>
        </view>
        <view class="info-row" wx:if="{{ orderInfo.productCode }}">
          <text class="info-label">产品编码</text>
          <text class="info-value">{{ orderInfo.productCode }}</text>
        </view>
        <view class="info-row" wx:if="{{ orderInfo.specification }}">
          <text class="info-label">产品规格</text>
          <text class="info-value">{{ orderInfo.specification }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">订单数量</text>
          <text class="info-value">{{ orderInfo.quantity }} {{ orderInfo.unit }}</text>
        </view>
      </view>
    </view>

    <!-- 包装信息 -->
    <view class="detail-card" wx:if="{{ orderInfo.packagingType }}">
      <view class="card-title">
        <text class="title-text">包装信息</text>
      </view>
      <view class="card-content">
        <view class="info-row">
          <text class="info-label">包装类型</text>
          <text class="info-value">{{ orderInfo.packagingType }}</text>
        </view>
        <view class="info-row" wx:if="{{ orderInfo.packagingNotes }}">
          <text class="info-label">包装说明</text>
          <text class="info-value">{{ orderInfo.packagingNotes }}</text>
        </view>
      </view>
    </view>

    <!-- 交付信息 -->
    <view class="detail-card">
      <view class="card-title">
        <text class="title-text">交付信息</text>
      </view>
      <view class="card-content">
        <view class="info-row">
          <text class="info-label">交付日期</text>
          <text class="info-value">{{ orderInfo.deliveryDateText }}</text>
        </view>
        <view class="info-row" wx:if="{{ orderInfo.priority }}">
          <text class="info-label">优先级</text>
          <text class="info-value priority-{{ orderInfo.priority }}">{{ orderInfo.priorityText }}</text>
        </view>
        <view class="info-row" wx:if="{{ orderInfo.notes }}">
          <text class="info-label">订单备注</text>
          <text class="info-value">{{ orderInfo.notes }}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="action-btn secondary" bindtap="editOrder">
        编辑订单
      </button>
      <button class="action-btn secondary" bindtap="showStatusModal">
        修改状态
      </button>
      <button
        class="action-btn primary"
        bindtap="createWorkOrder"
        wx:if="{{ orderInfo.status === 'confirmed' }}"
      >
        创建工单
      </button>
      <button class="action-btn danger" bindtap="showDeleteModal">
        删除订单
      </button>
    </view>
  </view>

  <!-- 状态修改弹窗 -->
  <view class="modal-overlay" wx:if="{{ showStatusModal }}" bindtap="hideStatusModal">
    <view class="modal-content" catchtap="true">
      <view class="modal-header">
        <text class="modal-title">修改订单状态</text>
        <text class="modal-close" bindtap="hideStatusModal">×</text>
      </view>
      <view class="modal-body">
        <view class="status-options">
          <view
            class="status-option {{ item.value === orderInfo.status ? 'active' : '' }}"
            wx:for="{{ statusOptions }}"
            wx:key="value"
            bindtap="changeOrderStatus"
            data-status="{{ item.value }}"
          >
            <text class="status-label">{{ item.label }}</text>
            <text class="status-check" wx:if="{{ item.value === orderInfo.status }}">✓</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 删除确认弹窗 -->
  <view class="modal-overlay" wx:if="{{ showDeleteModal }}" bindtap="hideDeleteModal">
    <view class="modal-content" catchtap="true">
      <view class="modal-header">
        <text class="modal-title">确认删除</text>
        <text class="modal-close" bindtap="hideDeleteModal">×</text>
      </view>
      <view class="modal-body">
        <text class="delete-warning">确定要删除订单 {{ orderInfo.orderNo }} 吗？</text>
        <text class="delete-note">删除后无法恢复，请谨慎操作。</text>
      </view>
      <view class="modal-footer">
        <button class="modal-btn secondary" bindtap="hideDeleteModal">取消</button>
        <button class="modal-btn danger" bindtap="deleteOrder" disabled="{{ saving }}">
          {{ saving ? '删除中...' : '确认删除' }}
        </button>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{ !loading && !orderInfo }}">
    <text class="empty-text">订单不存在或已被删除</text>
    <button class="btn btn-primary" bindtap="navigateBack">返回列表</button>
  </view>
</view>
