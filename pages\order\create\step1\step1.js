// 订单创建第一步：基本信息
const { formatDate } = require('../../../../utils/dateUtils')
const CustomerService = require('../../../../services/customerService.js')

Page({
  data: {
    // 订单数据
    orderData: {
      orderNo: '',
      deliveryDate: '',
      priority: 'normal',
      notes: ''
    },

    // 选择器数据
    customerOptions: [],
    priorityOptions: [
      { label: '普通', value: 'normal' },
      { label: '紧急', value: 'urgent' },
      { label: '特急', value: 'emergency' },
      { label: '试制', value: 'trial' }
    ],

    // 选择器索引
    customerIndex: 0,
    priorityIndex: 0,

    // 选中的客户
    selectedCustomer: null,

    // 表单验证
    isFormValid: false,

    // 最小日期（今天）
    minDate: ''
  },

  onLoad() {
    console.log('订单创建第一步页面加载')
    this.generateOrderNo()
    this.setMinDate()
    this.loadCustomers()
    this.loadDraft()
    this.validateForm()
  },

  // 生成订单号
  generateOrderNo() {
    const timestamp = Date.now()
    const orderNo = `PO${timestamp}`
    this.setData({
      'orderData.orderNo': orderNo
    })
  },

  // 设置最小日期
  setMinDate() {
    const today = new Date()
    const minDate = formatDate(today, 'YYYY-MM-DD')
    this.setData({ minDate })
  },

  // 加载客户数据
  async loadCustomers() {
    try {
      const customerList = await CustomerService.getCustomerList()
      const customerOptions = customerList.map(customer => ({
        label: customer.name,
        value: customer.id,
        ...customer
      }))

      this.setData({ customerOptions })

      // 设置默认选中第一个客户
      if (customerOptions.length > 0) {
        this.setData({
          selectedCustomer: customerOptions[0],
          customerIndex: 0
        })
        this.validateForm()
      }

      console.log('客户列表加载成功:', customerOptions)
    } catch (error) {
      console.error('加载客户数据失败:', error)
      wx.showToast({
        title: '加载客户列表失败',
        icon: 'none'
      })
    }
  },

  // 加载草稿数据
  loadDraft() {
    try {
      const draft = wx.getStorageSync('orderDraft')
      if (draft && draft.step >= 1) {
        this.setData({
          orderData: { ...this.data.orderData, ...draft.orderData },
          selectedCustomer: draft.selectedCustomer,
          customerIndex: this.data.customerOptions.findIndex(c => c.id === draft.selectedCustomer?.id) || 0,
          priorityIndex: this.data.priorityOptions.findIndex(p => p.value === draft.orderData?.priority) || 0
        })
        this.validateForm()
      }
    } catch (error) {
      console.error('加载草稿失败:', error)
    }
  },

  // 交货日期选择
  onDeliveryDateChange(e) {
    this.setData({
      'orderData.deliveryDate': e.detail.value
    })
    this.validateForm()
  },

  // 优先级选择
  onPriorityChange(e) {
    const index = parseInt(e.detail.value)
    this.setData({
      priorityIndex: index,
      'orderData.priority': this.data.priorityOptions[index].value
    })
  },

  // 客户选择
  onCustomerChange(e) {
    const index = parseInt(e.detail.value)
    const customer = this.data.customerOptions[index]
    this.setData({
      customerIndex: index,
      selectedCustomer: customer
    })
    this.validateForm()
  },

  // 备注输入
  onNotesInput(e) {
    this.setData({
      'orderData.notes': e.detail.value
    })
  },

  // 表单验证
  validateForm() {
    const { selectedCustomer, orderData } = this.data
    const isValid = selectedCustomer && orderData.deliveryDate
    this.setData({ isFormValid: isValid })
  },

  // 返回
  goBack() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  },

  // 保存草稿
  saveDraft() {
    const draftData = {
      step: 1,
      orderData: this.data.orderData,
      selectedCustomer: this.data.selectedCustomer,
      timestamp: new Date().toISOString()
    }
    
    wx.setStorageSync('orderDraft', draftData)
    
    wx.showToast({
      title: '草稿已保存',
      icon: 'success'
    })
  },

  // 下一步
  nextStep() {
    if (!this.data.isFormValid) {
      wx.showToast({
        title: '请填写必填项',
        icon: 'none'
      })
      return
    }
    
    // 保存当前步骤数据
    const stepData = {
      step: 1,
      orderData: this.data.orderData,
      selectedCustomer: this.data.selectedCustomer
    }
    
    wx.setStorageSync('orderStepData', stepData)
    
    // 跳转到第2步
    wx.navigateTo({
      url: '/pages/order/create/step2/step2'
    })
  }
})
