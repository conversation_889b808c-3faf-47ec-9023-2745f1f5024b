/* 库存列表页面样式 */
.page-container {
  background-color: var(--bg-page);
  min-height: 100vh;
}

.page-header {
  background-color: #FFFFFF;
  padding: 40rpx 20rpx 20rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.page-title {
  font-size: 36rpx;
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  display: block;
  margin-bottom: 8rpx;
}

.page-subtitle {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.inventory-list {
  padding: 20rpx;
}

.inventory-card {
  background-color: #FFFFFF;
  border-radius: var(--radius-lg);
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: var(--shadow-base);
}

.inventory-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 32rpx;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  display: block;
  margin-bottom: 4rpx;
}

.product-code {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.inventory-status {
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: var(--radius-xs);
  font-weight: var(--font-weight-medium);
}

.inventory-status.sufficient {
  background-color: var(--success-light);
  color: var(--success-color);
}

.inventory-status.warning {
  background-color: var(--warning-light);
  color: var(--warning-color);
}

.inventory-status.shortage {
  background-color: var(--error-light);
  color: var(--error-color);
}

.inventory-details {
  border-top: 1rpx solid var(--border-color);
  padding-top: 16rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.detail-value {
  font-size: 24rpx;
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  color: var(--text-tertiary);
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  line-height: 1.5;
}
