<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>风格5 - 极简主义风格</title>
    <style>
        /*
        设计理念：极简主义风格
        - 色彩：黑白灰为主，单一强调色
        - 特点：极度简洁、留白丰富、专注内容
        - 适用：追求极简美学的现代企业、设计公司
        - 字体：简洁无衬线字体
        - 圆角：无圆角或极小圆角，几何感
        */
        
        :root {
            --primary-color: #000000;
            --accent-color: #FF0000;
            --success-color: #00FF00;
            --warning-color: #FFFF00;
            --error-color: #FF0000;
            
            --text-primary: #000000;
            --text-secondary: #666666;
            --text-tertiary: #999999;
            --text-inverse: #FFFFFF;
            
            --bg-primary: #FFFFFF;
            --bg-secondary: #FAFAFA;
            --bg-tertiary: #F0F0F0;
            --bg-dark: #000000;
            
            --border-color: #E0E0E0;
            --border-dark: #000000;
            --shadow-subtle: 0 1px 2px rgba(0, 0, 0, 0.05);
            --shadow-light: 0 2px 4px rgba(0, 0, 0, 0.1);
            
            --radius-none: 0px;
            --radius-sm: 1px;
            --radius-md: 2px;
            
            --spacing-xs: 4px;
            --spacing-sm: 8px;
            --spacing-md: 16px;
            --spacing-lg: 32px;
            --spacing-xl: 64px;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.5;
            width: 375px;
            margin: 0 auto;
            min-height: 100vh;
            font-weight: 300;
        }
        
        .container {
            background-color: var(--bg-primary);
            min-height: 100vh;
        }
        
        /* 导航栏 */
        .navbar {
            background-color: var(--bg-primary);
            padding: var(--spacing-xl) var(--spacing-lg) var(--spacing-lg);
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: baseline;
        }
        
        .nav-title {
            font-size: 24px;
            font-weight: 100;
            color: var(--text-primary);
            letter-spacing: -0.5px;
        }
        
        .nav-actions {
            display: flex;
            gap: var(--spacing-lg);
        }
        
        .btn {
            background: none;
            border: none;
            font-size: 14px;
            font-weight: 300;
            cursor: pointer;
            transition: all 0.2s ease;
            color: var(--text-secondary);
            padding: var(--spacing-sm) 0;
            position: relative;
        }
        
        .btn::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 1px;
            background-color: var(--text-primary);
            transition: width 0.2s ease;
        }
        
        .btn:hover {
            color: var(--text-primary);
        }
        
        .btn:hover::after {
            width: 100%;
        }
        
        .btn-primary {
            color: var(--text-primary);
            font-weight: 400;
        }
        
        /* 订单列表 */
        .order-list {
            padding: 0 var(--spacing-lg);
        }
        
        .order-card {
            background-color: var(--bg-primary);
            padding: var(--spacing-lg) 0;
            margin-bottom: var(--spacing-lg);
            border-bottom: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }
        
        .order-card:last-child {
            border-bottom: none;
        }
        
        .order-card:hover {
            padding-left: var(--spacing-md);
            border-left: 2px solid var(--text-primary);
        }
        
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: baseline;
            margin-bottom: var(--spacing-lg);
        }
        
        .order-number {
            font-size: 18px;
            font-weight: 400;
            color: var(--text-primary);
            letter-spacing: -0.3px;
        }
        
        .order-status {
            font-size: 12px;
            font-weight: 300;
            color: var(--text-tertiary);
            text-transform: lowercase;
        }
        
        .status-pending {
            color: var(--text-secondary);
        }
        
        .status-confirmed {
            color: var(--text-primary);
        }
        
        .order-section {
            margin-bottom: var(--spacing-md);
        }
        
        .section-title {
            font-size: 10px;
            font-weight: 400;
            color: var(--text-tertiary);
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: var(--spacing-xs);
        }
        
        .section-content {
            font-size: 14px;
            color: var(--text-primary);
            line-height: 1.4;
            font-weight: 300;
        }
        
        .highlight {
            font-weight: 400;
        }
        
        .order-meta {
            display: flex;
            justify-content: space-between;
            align-items: baseline;
            margin-top: var(--spacing-lg);
            padding-top: var(--spacing-md);
            border-top: 1px solid var(--bg-tertiary);
            font-size: 12px;
            color: var(--text-tertiary);
            font-weight: 300;
        }
        
        .order-date {
            font-weight: 300;
        }
        
        .order-amount {
            font-size: 16px;
            font-weight: 300;
            color: var(--text-primary);
            letter-spacing: -0.3px;
        }
        
        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 375px;
            background-color: var(--bg-primary);
            border-top: 1px solid var(--border-color);
            padding: var(--spacing-md) 0;
            display: flex;
            justify-content: space-around;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-sm);
            color: var(--text-tertiary);
            text-decoration: none;
            font-size: 10px;
            font-weight: 300;
            transition: all 0.2s ease;
            text-transform: lowercase;
        }
        
        .nav-item.active {
            color: var(--text-primary);
        }
        
        .nav-icon {
            width: 16px;
            height: 16px;
            border: 1px solid currentColor;
            transition: all 0.2s ease;
        }
        
        .nav-item:hover {
            color: var(--text-primary);
        }
        
        .nav-item.active .nav-icon {
            background-color: var(--text-primary);
        }
        
        /* 分隔线 */
        .divider {
            height: 1px;
            background-color: var(--border-color);
            margin: var(--spacing-xl) 0;
        }
        
        /* 数字强调 */
        .number {
            font-variant-numeric: tabular-nums;
            font-feature-settings: "tnum";
        }
        
        /* 响应式调整 */
        @media (max-width: 375px) {
            .navbar {
                padding: var(--spacing-lg) var(--spacing-md) var(--spacing-md);
            }
            
            .order-list {
                padding: 0 var(--spacing-md);
            }
            
            .nav-title {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 导航栏 -->
        <div class="navbar">
            <div class="nav-header">
                <h1 class="nav-title">orders</h1>
                <div class="nav-actions">
                    <button class="btn btn-primary">new</button>
                    <button class="btn">settings</button>
                </div>
            </div>
        </div>
        
        <!-- 订单列表 -->
        <div class="order-list">
            <div class="order-card">
                <div class="order-header">
                    <div class="order-number number">po20240101001</div>
                    <div class="order-status status-pending">pending</div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">client</div>
                    <div class="section-content">
                        <div class="highlight">华东机械制造有限公司</div>
                        张工 · 13800138000
                    </div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">product</div>
                    <div class="section-content">
                        <div class="highlight">精密齿轮 gear001</div>
                        100件 · 高精度传动齿轮
                    </div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">packaging</div>
                    <div class="section-content">
                        防潮包装 · 3-5个工作日
                    </div>
                </div>
                
                <div class="order-meta">
                    <span class="order-date">2024.01.15 09:30</span>
                    <span class="order-amount number">¥15,000.00</span>
                </div>
            </div>
            
            <div class="order-card">
                <div class="order-header">
                    <div class="order-number number">po20240101002</div>
                    <div class="order-status status-confirmed">confirmed</div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">client</div>
                    <div class="section-content">
                        <div class="highlight">江南精工科技股份有限公司</div>
                        李经理 · 13900139000
                    </div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">product</div>
                    <div class="section-content">
                        <div class="highlight">不锈钢板材 steel002</div>
                        50平方米 · 304不锈钢板材，厚度3mm
                    </div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">packaging</div>
                    <div class="section-content">
                        标准包装 · 2-3个工作日
                    </div>
                </div>
                
                <div class="order-meta">
                    <span class="order-date">2024.01.14 14:20</span>
                    <span class="order-amount number">¥4,275.00</span>
                </div>
            </div>
            
            <div class="divider"></div>
            
            <div class="order-card">
                <div class="order-header">
                    <div class="order-number number">po20240101003</div>
                    <div class="order-status status-confirmed">confirmed</div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">client</div>
                    <div class="section-content">
                        <div class="highlight">北方重工集团有限公司</div>
                        王总工 · 13700137000
                    </div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">product</div>
                    <div class="section-content">
                        <div class="highlight">液压缸 hydr003</div>
                        20台 · 重型液压缸，最大压力16mpa
                    </div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">packaging</div>
                    <div class="section-content">
                        木箱包装 · 5-7个工作日
                    </div>
                </div>
                
                <div class="order-meta">
                    <span class="order-date">2024.01.13 16:45</span>
                    <span class="order-amount number">¥24,000.00</span>
                </div>
            </div>
        </div>
        
        <!-- 底部导航 -->
        <div class="bottom-nav">
            <a href="#" class="nav-item">
                <div class="nav-icon"></div>
                <span>home</span>
            </a>
            <a href="#" class="nav-item active">
                <div class="nav-icon"></div>
                <span>orders</span>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon"></div>
                <span>work</span>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon"></div>
                <span>package</span>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon"></div>
                <span>stock</span>
            </a>
        </div>
    </div>
</body>
</html>
