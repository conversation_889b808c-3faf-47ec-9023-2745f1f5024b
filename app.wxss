/* ERP管理系统 - 全局样式 - 简约现代风格 */
@import "styles/design-tokens.wxss";
@import "styles/modern-components.wxss";

/* 全局样式重置 - 简约现代风格 */
page {
  /* 简约现代风格色彩变量 */
  --primary-color: #007AFF;
  --primary-dark: #0056CC;
  --primary-light: #E3F2FD;
  --primary-ultra-light: #F0F8FF;

  --success-color: #34C759;
  --success-light: #E8F5E9;

  --warning-color: #FF9500;
  --warning-light: #FFF3E0;

  --error-color: #FF3B30;
  --error-light: #FFEBEE;

  --text-primary: #1D1D1F;
  --text-secondary: #48484A;
  --text-tertiary: #8E8E93;
  --text-quaternary: #C7C7CC;
  --text-disabled: #C7C7CC;
  --text-inverse: #FFFFFF;

  --bg-primary: #FFFFFF;
  --bg-secondary: #F2F2F7;
  --bg-tertiary: #E5E5EA;
  --bg-page: #F2F2F7;
  --border-color: #E5E5EA;

  /* 字体系统 */
  --font-size-xs: 20rpx;
  --font-size-sm: 24rpx;
  --font-size-base: 28rpx;
  --font-size-lg: 32rpx;
  --font-size-xl: 36rpx;
  --font-size-2xl: 40rpx;
  --font-size-3xl: 48rpx;
  --font-size-4xl: 56rpx;

  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  --line-height-tight: 1.2;
  --line-height-normal: 1.4;
  --line-height-relaxed: 1.6;
  --line-height-base: 1.4;

  /* 间距系统 */
  --spacing-xs: 8rpx;
  --spacing-sm: 12rpx;
  --spacing-base: 16rpx;
  --spacing-lg: 20rpx;
  --spacing-xl: 24rpx;
  --spacing-2xl: 32rpx;
  --spacing-3xl: 40rpx;
  --spacing-4xl: 48rpx;

  /* 圆角系统 */
  --radius-xs: 6rpx;
  --radius-sm: 8rpx;
  --radius-base: 12rpx;
  --radius-lg: 16rpx;
  --radius-xl: 20rpx;
  --radius-2xl: 24rpx;
  --radius-3xl: 32rpx;
  --radius-full: 50%;

  /* 阴影系统 */
  --shadow-xs: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 2rpx 4rpx rgba(0, 0, 0, 0.06);
  --shadow-base: 0 4rpx 8rpx rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 16rpx 32rpx rgba(0, 0, 0, 0.12);
  --shadow-card: 0 2rpx 12rpx rgba(0, 122, 255, 0.08);

  /* 动画系统 */
  --transition-fast: 0.15s;
  --transition-base: 0.2s;
  --transition-slow: 0.3s;
  --transition-smooth: 0.4s;
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-out-back: cubic-bezier(0.34, 1.56, 0.64, 1);
  --ease-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);

  /* 组件变量 */
  --btn-height-sm: 64rpx;
  --btn-height-base: 88rpx;
  --btn-height-lg: 104rpx;
  --btn-padding-x: 32rpx;
  --btn-border-radius: 16rpx;

  --input-height: 88rpx;
  --input-padding: 24rpx;
  --input-border-radius: 16rpx;

  --card-padding: 32rpx;
  --card-border-radius: 20rpx;
  --card-shadow: 0 2rpx 12rpx rgba(0, 122, 255, 0.08);
  --card-hover-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);

  --list-item-height: 120rpx;
  --list-item-padding: 32rpx;

  --navbar-height: 88rpx;
  --navbar-padding: 24rpx;

  --section-spacing: 24rpx;
  --content-max-width: 750rpx;
  --page-padding: 32rpx;

  /* 页面基础样式 */
  background-color: var(--bg-page);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  font-size: var(--font-size-base);
  color: var(--text-primary);
  line-height: var(--line-height-base);
  height: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 通用布局类 - 简约现代风格 */
.container {
  padding: var(--page-padding);
  min-height: 100vh;
  box-sizing: border-box;
}

.page-container {
  min-height: 100vh;
  background-color: var(--bg-page);
}

.content-container {
  padding: var(--page-padding);
  padding-bottom: 120rpx; /* 为底部按钮留出空间 */
  max-width: var(--content-max-width);
  margin: 0 auto;
}

/* Flex布局 */
.flex {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-1 {
  flex: 1;
}

/* 间距类 */
.mt-10 { margin-top: 10rpx; }
.mt-20 { margin-top: 20rpx; }
.mt-30 { margin-top: 30rpx; }
.mb-10 { margin-bottom: 10rpx; }
.mb-20 { margin-bottom: 20rpx; }
.mb-30 { margin-bottom: 30rpx; }
.ml-10 { margin-left: 10rpx; }
.ml-20 { margin-left: 20rpx; }
.mr-10 { margin-right: 10rpx; }
.mr-20 { margin-right: 20rpx; }

.pt-10 { padding-top: 10rpx; }
.pt-20 { padding-top: 20rpx; }
.pb-10 { padding-bottom: 10rpx; }
.pb-20 { padding-bottom: 20rpx; }
.pl-10 { padding-left: 10rpx; }
.pl-20 { padding-left: 20rpx; }
.pr-10 { padding-right: 10rpx; }
.pr-20 { padding-right: 20rpx; }

/* 文本类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }

.font-bold { font-weight: 600; }
.font-normal { font-weight: 400; }

.font-xs { font-size: 20rpx; }
.font-sm { font-size: 24rpx; }
.font-base { font-size: 28rpx; }
.font-lg { font-size: 32rpx; }
.font-xl { font-size: 36rpx; }

/* 按钮样式 */
.btn {
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  text-align: center;
  border: none;
  display: inline-block;
  box-sizing: border-box;
  line-height: 1.4;
}

.btn-primary {
  background-color: var(--primary-color);
  color: #FFFFFF;
}

.btn-primary:active {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1rpx solid var(--border-color);
}

.btn-success {
  background-color: var(--success-color);
  color: #FFFFFF;
}

.btn-warning {
  background-color: var(--warning-color);
  color: #FFFFFF;
}

.btn-error {
  background-color: var(--error-color);
  color: #FFFFFF;
}

.btn-small {
  padding: 8rpx 16rpx;
  font-size: 24rpx;
}

.btn-large {
  padding: 20rpx 40rpx;
  font-size: 32rpx;
}

.btn-block {
  width: 100%;
  display: block;
}

.btn-disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* 卡片样式 */
.card {
  background-color: #FFFFFF;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid var(--border-color);
  margin-bottom: 16rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.card-content {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* 列表样式 */
.list-item {
  background-color: #FFFFFF;
  padding: 24rpx;
  border-bottom: 1rpx solid var(--border-color);
  display: flex;
  align-items: center;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:active {
  background-color: var(--bg-secondary);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  color: var(--text-tertiary);
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  line-height: 1.5;
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  color: var(--text-tertiary);
}

.loading-text {
  margin-left: 16rpx;
  font-size: 28rpx;
}

/* 底部固定按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  padding: 20rpx;
  border-top: 1rpx solid var(--border-color);
  display: flex;
  gap: 16rpx;
  z-index: 100;
}

.bottom-actions .btn {
  flex: 1;
}

/* 状态标签 */
.status-tag {
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
  line-height: 1.2;
}

.status-pending {
  background-color: var(--warning-light);
  color: var(--warning-color);
}

.status-processing {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.status-completed {
  background-color: var(--success-light);
  color: var(--success-color);
}

.status-cancelled {
  background-color: var(--error-light);
  color: var(--error-color);
}
