// 库存列表页面逻辑
Page({
  data: {
    inventoryList: [
      {
        id: 'inv_001',
        productName: '测试产品A',
        productCode: 'PROD-001',
        currentStock: 150,
        safetyStock: 100,
        unit: '件',
        status: 'sufficient',
        statusText: '充足',
        lastUpdateTime: '2024-01-15 14:30'
      },
      {
        id: 'inv_002',
        productName: '测试产品B',
        productCode: 'PROD-002',
        currentStock: 80,
        safetyStock: 100,
        unit: '件',
        status: 'warning',
        statusText: '预警',
        lastUpdateTime: '2024-01-15 10:20'
      },
      {
        id: 'inv_003',
        productName: '测试产品C',
        productCode: 'PROD-003',
        currentStock: 20,
        safetyStock: 50,
        unit: '件',
        status: 'shortage',
        statusText: '不足',
        lastUpdateTime: '2024-01-14 16:45'
      }
    ]
  },

  onLoad() {
    console.log('库存列表页面加载')
  },

  onShow() {
    console.log('库存列表页面显示')
  }
})
