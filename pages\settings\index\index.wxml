<!--设置页面 - 简约现代风格-->
<view class="modern-page">
  <!-- 页面头部 -->
  <view class="modern-navbar">
    <view class="header-content">
      <text class="modern-navbar-title">系统设置</text>
      <text class="page-subtitle">管理系统基础数据</text>
    </view>
  </view>

  <view class="modern-container">
    <!-- 基础数据管理 -->
    <view class="modern-section">
      <view class="modern-section-title">
        <text class="modern-section-text">基础数据管理</text>
      </view>

      <view class="modern-list">
        <view
          class="modern-list-item"
          wx:for="{{ basicDataOptions }}"
          wx:key="id"
          bindtap="navigateToPage"
          data-url="{{ item.url }}"
        >
          <view class="list-item-content">
            <text class="list-item-title">{{ item.title }}</text>
            <text class="list-item-desc">{{ item.description }}</text>
          </view>
          <view class="list-item-arrow">
            <text class="arrow-icon">→</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 生产管理 -->
    <view class="modern-section">
      <view class="modern-section-title">
        <text class="modern-section-text">生产管理</text>
      </view>

      <view class="modern-list">
        <view
          class="modern-list-item"
          wx:for="{{ productionOptions }}"
          wx:key="id"
          bindtap="navigateToPage"
          data-url="{{ item.url }}"
        >
          <view class="list-item-content">
            <text class="list-item-title">{{ item.title }}</text>
            <text class="list-item-desc">{{ item.description }}</text>
          </view>
          <view class="list-item-arrow">
            <text class="arrow-icon">→</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 系统信息 -->
    <view class="modern-section">
      <view class="modern-section-title">
        <text class="modern-section-text">系统信息</text>
      </view>

      <view class="modern-list">
        <view class="modern-list-item">
          <view class="list-item-content">
            <text class="list-item-title">应用版本</text>
            <text class="list-item-desc">ERP管理系统 v1.0.0</text>
          </view>
        </view>

        <view class="modern-list-item">
          <view class="list-item-content">
            <text class="list-item-title">最后更新</text>
            <text class="list-item-desc">{{ lastUpdateTime }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <button class="modern-btn modern-btn-secondary" bindtap="clearCache">
        清除缓存
      </button>
      <button class="modern-btn modern-btn-secondary" bindtap="exportData">
        导出数据
      </button>
    </view>
  </view>
</view>
