// 设置页面
const { formatDate } = require('../../../utils/dateUtils')

Page({
  data: {
    // 基础数据管理选项
    basicDataOptions: [
      {
        id: 'customer',
        title: '客户信息管理',
        description: '管理客户公司信息',
        url: '/pages/order/settings/customer/customer'
      },
      {
        id: 'product',
        title: '产品信息管理',
        description: '管理产品基础信息',
        url: '/pages/order/settings/product/product'
      },
      {
        id: 'category',
        title: '产品分类管理',
        description: '管理产品分类和标签',
        url: '/pages/order/settings/category/category'
      },
      {
        id: 'packaging',
        title: '包装信息管理',
        description: '管理包装类型和规格',
        url: '/pages/order/settings/packaging/packaging'
      }
    ],

    // 生产管理选项
    productionOptions: [
      {
        id: 'process',
        title: '工序管理',
        description: '管理生产工序模板',
        url: '/pages/settings/process/process'
      },
      {
        id: 'route',
        title: '工艺路线管理',
        description: '管理工艺路线配置',
        url: '/pages/settings/route/route'
      }
    ],

    lastUpdateTime: ''
  },

  onLoad() {
    wx.setNavigationBarTitle({
      title: '系统设置'
    })
    this.initPageData()
  },

  onShow() {
    // 每次显示时更新时间
    this.updateLastTime()
  },

  /**
   * 初始化页面数据
   */
  initPageData() {
    this.updateLastTime()
  },

  /**
   * 更新最后更新时间
   */
  updateLastTime() {
    const now = new Date()
    this.setData({
      lastUpdateTime: formatDate(now, 'YYYY-MM-DD HH:mm')
    })
  },

  /**
   * 导航到指定页面
   */
  navigateToPage(e) {
    const url = e.currentTarget.dataset.url
    if (url) {
      wx.navigateTo({
        url: url,
        fail: (error) => {
          console.error('页面跳转失败:', error)
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          })
        }
      })
    }
  },

  /**
   * 清除缓存
   */
  clearCache() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除所有缓存数据吗？此操作不可撤销。',
      confirmText: '确认清除',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          try {
            wx.clearStorageSync()
            wx.showToast({
              title: '缓存清除成功',
              icon: 'success'
            })
            
            // 延迟重启应用
            setTimeout(() => {
              wx.reLaunch({
                url: '/pages/index/index'
              })
            }, 1500)
          } catch (error) {
            console.error('清除缓存失败:', error)
            wx.showToast({
              title: '清除缓存失败',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  /**
   * 导出数据
   */
  exportData() {
    wx.showLoading({
      title: '准备导出...'
    })

    try {
      // 获取所有存储的数据
      const storageInfo = wx.getStorageInfoSync()
      const exportData = {}
      
      storageInfo.keys.forEach(key => {
        if (key.startsWith('erp_')) {
          try {
            exportData[key] = wx.getStorageSync(key)
          } catch (error) {
            console.error(`读取${key}失败:`, error)
          }
        }
      })

      // 生成导出文件内容
      const exportContent = JSON.stringify(exportData, null, 2)
      const fileName = `ERP数据导出_${formatDate(new Date(), 'YYYYMMDD_HHmmss')}.json`

      wx.hideLoading()

      // 显示导出信息
      wx.showModal({
        title: '数据导出',
        content: `数据已准备完成\n文件名: ${fileName}\n数据大小: ${(exportContent.length / 1024).toFixed(2)}KB`,
        showCancel: false,
        confirmText: '知道了'
      })

      console.log('导出数据:', exportContent)
      
    } catch (error) {
      wx.hideLoading()
      console.error('导出数据失败:', error)
      wx.showToast({
        title: '导出失败',
        icon: 'none'
      })
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.updateLastTime()
    wx.stopPullDownRefresh()
  }
})
