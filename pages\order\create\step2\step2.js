// 订单创建第二步：选择产品
const ProductService = require('../../../../services/productService.js')

Page({
  data: {
    // 搜索关键词
    searchKeyword: '',
    
    // 产品数据
    products: [],
    categories: [],
    selectedCategoryId: '',
    
    // 已选产品
    selectedProducts: [],
    
    // 表单验证
    isFormValid: false,
    
    // 计算属性
    filteredProducts: [],
    totalAmount: 0
  },

  onLoad() {
    console.log('订单创建第二步页面加载')
    this.loadCategories()
    this.loadProducts()
    this.loadDraft()
    this.updateFilteredProducts()
    this.validateForm()
  },

  // 加载产品类别
  async loadCategories() {
    try {
      const categories = await ProductService.getProductCategories()
      const categoryOptions = categories.map(category => ({
        id: category,
        name: category
      }))

      // 添加"全部"选项
      categoryOptions.unshift({ id: '', name: '全部' })

      this.setData({
        categories: categoryOptions,
        selectedCategoryId: categoryOptions[0]?.id || ''
      })
    } catch (error) {
      console.error('加载类别数据失败:', error)
      // 使用默认分类
      const defaultCategories = [
        { id: '', name: '全部' },
        { id: '机械零件', name: '机械零件' },
        { id: '电子元件', name: '电子元件' },
        { id: '原材料', name: '原材料' },
        { id: '工具设备', name: '工具设备' }
      ]
      this.setData({
        categories: defaultCategories,
        selectedCategoryId: ''
      })
    }
  },

  // 加载产品数据
  async loadProducts() {
    try {
      const productList = await ProductService.getProductList()
      const products = productList.map(product => ({
        ...product,
        selected: false,
        quantity: 1
      }))

      this.setData({ products })
      console.log('产品列表加载成功:', products)
    } catch (error) {
      console.error('加载产品数据失败:', error)
      wx.showToast({
        title: '加载产品列表失败',
        icon: 'none'
      })
    }
  },

  // 加载草稿数据
  loadDraft() {
    try {
      const draft = wx.getStorageSync('orderDraft')
      if (draft && draft.step >= 2 && draft.selectedProducts) {
        // 恢复已选产品状态
        const products = this.data.products.map(product => {
          const selected = draft.selectedProducts.find(p => p.id === product.id)
          if (selected) {
            return {
              ...product,
              selected: true,
              quantity: selected.quantity
            }
          }
          return product
        })
        
        this.setData({ 
          products,
          selectedProducts: draft.selectedProducts
        })
      }
    } catch (error) {
      console.error('加载草稿失败:', error)
    }
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({ searchKeyword: e.detail.value })
    this.updateFilteredProducts()
  },

  // 选择类别
  selectCategory(e) {
    const categoryId = e.currentTarget.dataset.id
    this.setData({ selectedCategoryId: categoryId })
    this.updateFilteredProducts()
  },

  // 更新过滤后的产品列表
  updateFilteredProducts() {
    const { products, searchKeyword, selectedCategoryId } = this.data

    let filtered = products.filter(product => {
      // 类别过滤
      if (selectedCategoryId && product.category !== selectedCategoryId) {
        return false
      }

      // 搜索过滤
      if (searchKeyword) {
        const keyword = searchKeyword.toLowerCase()
        return product.name.toLowerCase().includes(keyword) ||
               product.code.toLowerCase().includes(keyword) ||
               (product.specification && product.specification.toLowerCase().includes(keyword))
      }

      return true
    })

    this.setData({ filteredProducts: filtered })
  },

  // 切换产品选择状态
  toggleProduct(e) {
    const productId = e.currentTarget.dataset.id
    const products = this.data.products.map(product => {
      if (product.id === productId) {
        return {
          ...product,
          selected: !product.selected,
          quantity: product.selected ? 1 : product.quantity
        }
      }
      return product
    })
    
    this.setData({ products })
    this.updateSelectedProducts()
    this.updateFilteredProducts()
    this.validateForm()
  },

  // 增加数量
  increaseQuantity(e) {
    const productId = e.currentTarget.dataset.id
    this.updateProductQuantity(productId, 1)
  },

  // 减少数量
  decreaseQuantity(e) {
    const productId = e.currentTarget.dataset.id
    this.updateProductQuantity(productId, -1)
  },

  // 数量输入
  onQuantityInput(e) {
    const productId = e.currentTarget.dataset.id
    const quantity = parseInt(e.detail.value) || 1
    this.setProductQuantity(productId, quantity)
  },

  // 更新产品数量
  updateProductQuantity(productId, delta) {
    const products = this.data.products.map(product => {
      if (product.id === productId) {
        const newQuantity = Math.max(1, product.quantity + delta)
        return { ...product, quantity: newQuantity }
      }
      return product
    })
    
    this.setData({ products })
    this.updateSelectedProducts()
    this.updateFilteredProducts()
  },

  // 设置产品数量
  setProductQuantity(productId, quantity) {
    const products = this.data.products.map(product => {
      if (product.id === productId) {
        return { ...product, quantity: Math.max(1, quantity) }
      }
      return product
    })
    
    this.setData({ products })
    this.updateSelectedProducts()
    this.updateFilteredProducts()
  },

  // 更新已选产品列表
  updateSelectedProducts() {
    const selectedProducts = this.data.products.filter(p => p.selected)

    this.setData({
      selectedProducts
    })
  },

  // 表单验证
  validateForm() {
    const isValid = this.data.selectedProducts.length > 0
    this.setData({ isFormValid: isValid })
  },

  // 返回上一步
  goBack() {
    wx.navigateBack()
  },

  // 保存草稿
  saveDraft() {
    // 获取第1步数据
    const step1Data = wx.getStorageSync('orderStepData') || {}

    // 保存草稿到本地存储
    const draftData = {
      step: 2,
      ...step1Data,
      selectedProducts: this.data.selectedProducts,
      timestamp: new Date().toISOString()
    }

    wx.setStorageSync('orderDraft', draftData)

    wx.showToast({
      title: '草稿已保存',
      icon: 'success'
    })
  },

  // 下一步
  nextStep() {
    if (!this.data.isFormValid) {
      wx.showToast({
        title: '请至少选择一个产品',
        icon: 'none'
      })
      return
    }

    // 获取第1步数据
    const step1Data = wx.getStorageSync('orderStepData') || {}

    // 保存当前步骤数据
    const stepData = {
      ...step1Data,
      step: 2,
      selectedProducts: this.data.selectedProducts
    }

    wx.setStorageSync('orderStepData', stepData)

    // 跳转到第3步
    wx.navigateTo({
      url: '/pages/order/create/step3/step3'
    })
  }
})
