/* 订单创建第一步样式 */

/* 重置按钮样式 */
button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  font-size: inherit;
  color: inherit;
  line-height: inherit;
}

button::after {
  border: none;
}

.step-container {
  min-height: 100vh;
  background: #FAFAFA;
  display: flex;
  flex-direction: column;
  padding-bottom: 160rpx;
}

/* 自定义导航栏 */
.custom-navbar {
  background: #007AFF;
  padding: 20rpx 24rpx 16rpx;
  position: sticky;
  top: 0;
  z-index: 100;
}

.navbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar-left {
  display: flex;
  align-items: center;
  color: #FFFFFF;
}

.back-icon {
  font-size: 40rpx;
  margin-right: 8rpx;
}

.back-text {
  font-size: 28rpx;
}

.navbar-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #FFFFFF;
}

.navbar-right {
  width: 120rpx;
}

/* 步骤指示器 */
.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 20rpx;
  background: #FFFFFF;
  margin-bottom: 20rpx;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #F0F0F0;
  color: #8E8E93;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.step-item.active .step-number {
  background: #007AFF;
  color: #FFFFFF;
}

.step-text {
  font-size: 24rpx;
  color: #8E8E93;
}

.step-item.active .step-text {
  color: #007AFF;
  font-weight: 600;
}

.step-line {
  flex: 1;
  height: 2rpx;
  background: #F0F0F0;
  margin: 0 20rpx;
  margin-top: -30rpx;
}

.step-line.completed {
  background: #007AFF;
}

.step-number.completed {
  background: #007AFF;
  color: #FFFFFF;
}

/* 表单容器 */
.form-container {
  flex: 1;
  padding: 0 20rpx;
}

.form-section {
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1D1D1F;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #E3F2FD;
}

/* 表单项 */
.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #1D1D1F;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.required {
  color: #FF3B30;
}

/* 输入框 */
.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #F0F0F0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #1D1D1F;
  background: #FFFFFF;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #007AFF;
}

.form-input[disabled] {
  background: #F8F9FA;
  color: #8E8E93;
}

/* 文本域 */
.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 16rpx 20rpx;
  border: 2rpx solid #F0F0F0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #1D1D1F;
  background: #FFFFFF;
  box-sizing: border-box;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #8E8E93;
  margin-top: 8rpx;
}

/* 选择器 */
.picker-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #F0F0F0;
  border-radius: 8rpx;
  background: #FFFFFF;
}

.picker-text {
  font-size: 28rpx;
  color: #1D1D1F;
}

.picker-text.placeholder {
  color: #C7C7CC;
}

.picker-arrow {
  color: #8E8E93;
  font-size: 24rpx;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #FFFFFF;
  padding: 20rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #F0F0F0;
  z-index: 1000;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 16rpx;
}

.btn-secondary {
  flex: 1;
  height: 88rpx;
  background: #F8F9FA !important;
  color: #1D1D1F !important;
  border: 2rpx solid #F0F0F0 !important;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  flex: 2;
  height: 88rpx;
  background: #007AFF !important;
  color: #FFFFFF !important;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-primary[disabled] {
  background: #C7C7CC !important;
  color: #8E8E93 !important;
}
