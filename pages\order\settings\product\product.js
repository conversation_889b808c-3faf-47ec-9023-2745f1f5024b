// 生产管理系统 - 产品管理页面
const ProductService = require('../../../../services/productService.js')
const CategoryService = require('../../../../services/categoryService.js')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    productList: [],
    showAddForm: false,
    formData: {
      name: '',
      code: '',
      specification: '',
      unit: '',
      price: '',
      category: '',
      notes: ''
    },
    editingId: null,
    loading: false,
    unitOptions: [
      { value: '件', label: '件' },
      { value: '套', label: '套' },
      { value: '个', label: '个' },
      { value: 'kg', label: '千克' },
      { value: 'm', label: '米' },
      { value: 'm²', label: '平方米' },
      { value: 'm³', label: '立方米' }
    ],
    categoryOptions: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    wx.setNavigationBarTitle({
      title: '产品管理'
    })
    this.loadProductList()
    this.loadCategoryOptions()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.loadProductList()
    this.loadCategoryOptions()
  },

  /**
   * 加载产品列表
   */
  async loadProductList() {
    try {
      this.setData({ loading: true })
      const productList = await ProductService.getProductList()
      this.setData({ productList })
    } catch (error) {
      console.error('加载产品列表失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 加载分类选项
   */
  async loadCategoryOptions() {
    try {
      const result = await CategoryService.getCategoryList()
      if (result.success) {
        const categoryOptions = result.data.map(category => ({
          value: category.name,
          label: category.name
        }))
        // 添加"其他"选项
        categoryOptions.push({ value: '其他', label: '其他' })
        this.setData({ categoryOptions })
      }
    } catch (error) {
      console.error('加载分类选项失败:', error)
      // 使用默认分类选项
      this.setData({
        categoryOptions: [
          { value: '机械零件', label: '机械零件' },
          { value: '电子元件', label: '电子元件' },
          { value: '原材料', label: '原材料' },
          { value: '工具设备', label: '工具设备' },
          { value: '其他', label: '其他' }
        ]
      })
    }
  },

  /**
   * 显示添加表单
   */
  showAddProduct() {
    this.setData({
      showAddForm: true,
      editingId: null,
      formData: {
        name: '',
        code: '',
        specification: '',
        unit: '',
        price: '',
        category: '',
        notes: ''
      }
    })
  },

  /**
   * 编辑产品
   */
  editProduct(e) {
    const product = e.currentTarget.dataset.product
    this.setData({
      showAddForm: true,
      editingId: product.id,
      formData: {
        name: product.name,
        code: product.code,
        specification: product.specification || '',
        unit: product.unit,
        price: product.price ? product.price.toString() : '',
        category: product.category || '',
        notes: product.notes || ''
      }
    })
  },

  /**
   * 隐藏表单
   */
  hideForm() {
    this.setData({ showAddForm: false })
  },

  /**
   * 表单输入处理
   */
  onFormInput(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    this.setData({
      [`formData.${field}`]: value
    })
  },

  /**
   * 选择器变化处理
   */
  onPickerChange(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    const options = field === 'unit' ? this.data.unitOptions : this.data.categoryOptions
    this.setData({
      [`formData.${field}`]: options[value].value
    })
  },

  /**
   * 保存产品
   */
  async saveProduct() {
    const { formData, editingId } = this.data
    
    // 表单验证
    if (!formData.name.trim()) {
      wx.showToast({
        title: '请输入产品名称',
        icon: 'none'
      })
      return
    }
    
    if (!formData.code.trim()) {
      wx.showToast({
        title: '请输入产品编码',
        icon: 'none'
      })
      return
    }

    if (!formData.unit.trim()) {
      wx.showToast({
        title: '请选择计量单位',
        icon: 'none'
      })
      return
    }

    try {
      this.setData({ loading: true })

      const productData = {
        ...formData
      }
      
      if (editingId) {
        // 更新产品
        await ProductService.updateProduct(editingId, productData)
        wx.showToast({
          title: '更新成功',
          icon: 'success'
        })
      } else {
        // 新增产品
        await ProductService.createProduct(productData)
        wx.showToast({
          title: '添加成功',
          icon: 'success'
        })
      }
      
      this.hideForm()
      this.loadProductList()
    } catch (error) {
      console.error('保存产品失败:', error)
      wx.showToast({
        title: error.message || '保存失败',
        icon: 'error'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 删除产品
   */
  async deleteProduct(e) {
    const { product } = e.currentTarget.dataset
    
    const result = await new Promise((resolve) => {
      wx.showModal({
        title: '确认删除',
        content: `确定要删除产品"${product.name}"吗？`,
        success: resolve
      })
    })
    
    if (!result.confirm) return
    
    try {
      this.setData({ loading: true })
      await ProductService.deleteProduct(product.id)
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      })
      this.loadProductList()
    } catch (error) {
      console.error('删除产品失败:', error)
      wx.showToast({
        title: '删除失败',
        icon: 'error'
      })
    } finally {
      this.setData({ loading: false })
    }
  }
})
