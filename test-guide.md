# 🧪 项目测试指南

## 快速测试步骤

### 1. 导入项目
1. 打开微信开发者工具
2. 选择"导入项目"
3. 项目目录选择：`miniprogram-erp`
4. AppID使用：`wx5face363b14694ae` (已配置)

### 2. 验证项目结构
确认以下文件存在且无错误：
- ✅ `app.js` - 应用入口
- ✅ `app.json` - 应用配置 (已移除图标引用)
- ✅ `app.wxss` - 全局样式
- ✅ `project.config.json` - 项目配置

### 3. 测试核心功能

#### 首页测试
- 点击"首页"Tab
- 验证数据统计显示正常
- 测试快捷操作按钮
- 检查待处理事项列表
- 验证最近订单显示

#### 订单列表测试
- 点击"订单"Tab
- 验证订单列表显示
- 测试搜索功能（输入关键词）
- 测试状态筛选（全部、待确认、已确认等）
- 点击订单卡片跳转详情页
- 测试设置菜单（右上角三点）

#### 工单列表测试
- 点击"工单"Tab
- 验证工单列表显示
- 检查工单进度条显示
- 测试"新建工单"按钮

#### 库存列表测试
- 点击"库存"Tab
- 验证库存列表显示
- 检查库存状态标识（充足/预警/不足）

### 4. 预期结果

#### ✅ 正常情况
- 所有页面正常加载，无白屏
- TabBar导航正常切换
- Mock数据正常显示
- 页面样式正确渲染
- 控制台无严重错误

#### ❌ 异常情况处理
如果遇到问题：

1. **编译错误**
   - 检查文件路径是否正确
   - 确认所有必需文件都存在
   - 重新编译项目

2. **页面白屏**
   - 查看控制台错误信息
   - 检查页面路径配置
   - 验证页面文件完整性

3. **样式问题**
   - 确认 `styles/design-tokens.wxss` 正确导入
   - 检查CSS变量是否生效
   - 验证rpx单位转换

4. **数据不显示**
   - 检查Mock服务是否正常
   - 验证数据格式是否正确
   - 查看网络请求状态

### 5. 性能验证

#### 加载性能
- 首页加载时间 < 2秒
- 页面切换流畅无卡顿
- 列表滚动性能良好

#### 内存使用
- 长时间使用无内存泄漏
- 页面切换后正确释放资源

### 6. 兼容性测试

#### 设备测试
- iPhone (iOS 12+)
- Android (Android 7+)
- 不同屏幕尺寸适配

#### 微信版本
- 微信 8.0+
- 基础库 2.19.4+

## 🐛 常见问题解决

### Q1: TabBar图标不显示
**A**: 已移除图标配置，使用纯文字导航，这是正常的。

### Q2: 页面跳转失败
**A**: 检查 `app.json` 中的页面路径配置是否正确。

### Q3: 样式不生效
**A**: 确认CSS变量正确导入，检查 `app.wxss` 中的 `@import` 语句。

### Q4: Mock数据不显示
**A**: 检查 `services/orderService.js` 文件是否存在，数据格式是否正确。

## 📝 测试报告模板

```
测试时间：____年__月__日
测试环境：微信开发者工具 v______
基础库版本：______

功能测试结果：
□ 首页功能正常
□ 订单列表正常
□ 工单列表正常  
□ 库存列表正常
□ 页面导航正常
□ 样式渲染正常

性能测试结果：
□ 加载速度满足要求
□ 内存使用正常
□ 无明显卡顿

问题记录：
1. ________________
2. ________________
3. ________________

总体评价：□ 通过 □ 需要修复
```

## 🚀 下一步开发建议

测试通过后，可以继续开发：

1. **完善订单详情页面**
2. **实现订单创建功能**
3. **添加工单详情和创建**
4. **集成真实后端API**
5. **添加用户认证功能**

---

**测试完成后，请反馈测试结果，以便进行下一步开发！** 🎯
