/* 生产管理系统 - 订单列表页面样式 - 简约现代风格 */
@import '../../../styles/modern-simple.wxss';

.page-container {
  min-height: 100vh;
  background-color: var(--bg-page);
  padding-bottom: 40rpx;
}

/* 页面头部操作区 - 简约现代风格 */
.header-actions {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 20rpx;
  background-color: #FFFFFF;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.btn {
  height: 80rpx;
  border-radius: 12rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  font-size: 28rpx;
  padding: 0 24rpx;
  min-width: 160rpx;
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-base) var(--ease-in-out);
  box-shadow: var(--shadow-xs);
}

.btn-primary {
  background-color: #007AFF;
  color: #FFFFFF;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.btn-primary:active {
  background-color: #0056CC;
  transform: scale(0.98);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  background-color: #F2F2F7;
  color: #1D1D1F;
  border: 2rpx solid #E5E5EA;
}

.btn-secondary:active {
  background-color: #E5E5EA;
  transform: scale(0.98);
}

.btn-secondary {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  border: 2rpx solid var(--border-color);
}

.btn-secondary:active {
  background-color: var(--bg-secondary);
  transform: scale(0.98);
}

/* 移除图标样式，使用纯文字按钮 */

/* 搜索栏 - 简约现代风格 */
.search-container {
  background-color: var(--bg-primary);
  padding: var(--spacing-lg) var(--page-padding);
  margin-bottom: var(--spacing-sm);
}

.search-box {
  display: flex;
  align-items: center;
  background-color: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: 0 var(--spacing-lg);
  height: 80rpx;
  box-shadow: var(--shadow-xs);
}

/* 移除搜索图标样式 */

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: var(--text-primary);
  height: 100%;
}

.clear-btn {
  font-size: 24rpx;
  color: #007AFF;
  padding: 8rpx 12rpx;
  margin-left: 8rpx;
  background-color: transparent;
  border: none;
}

/* 筛选栏 - 简约现代风格 */
.filter-container {
  background-color: var(--bg-primary);
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  border-radius: var(--radius-lg);
  margin: 0 var(--page-padding) var(--spacing-lg);
  box-shadow: var(--shadow-xs);
}

.filter-scroll {
  flex: 1;
  white-space: nowrap;
}

.filter-tabs {
  display: flex;
  padding: 0 20rpx;
}

.filter-tab {
  padding: 24rpx 32rpx;
  font-size: 28rpx;
  color: var(--text-secondary);
  white-space: nowrap;
  position: relative;
  transition: all 0.2s ease;
}

.filter-tab.active {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

.filter-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: var(--primary-color);
  border-radius: 2rpx;
}

.filter-actions {
  padding: 0 20rpx;
  border-left: 1rpx solid var(--border-color);
}

.action-btn {
  width: 64rpx;
  height: 64rpx;
  border-radius: var(--radius-base);
  background-color: var(--bg-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  padding: 0;
}

.action-btn:active {
  background-color: var(--primary-light);
}

.action-icon {
  font-size: 28rpx;
}

/* 订单列表 - 简约现代风格 */
.order-list {
  padding: 0 var(--page-padding);
}

.order-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #E5E5EA;
  transition: all 0.3s ease;
}

.order-card:active {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.12);
}

/* 订单头部 - 简约现代风格 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

/* order-info 容器已移除 */

.order-no {
  font-size: 32rpx;
  font-weight: 600;
  color: #1D1D1F;
}

.order-status {
  font-size: 24rpx;
  padding: 8rpx 24rpx;
  border-radius: 40rpx;
  font-weight: 500;
}

.order-status.pending {
  background-color: #FFF3CD;
  color: #856404;
}

.order-status.confirmed {
  background-color: #D1ECF1;
  color: #0C5460;
}

.order-status.processing {
  background-color: #D4EDDA;
  color: #155724;
}

.order-status.completed {
  background-color: #F2F2F7;
  color: #8E8E93;
}

.order-status.cancelled {
  background-color: #F8D7DA;
  color: #721C24;
}

.order-priority {
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: var(--radius-xs);
  font-weight: var(--font-weight-medium);
}

.order-priority.low {
  background-color: var(--bg-secondary);
  color: var(--text-tertiary);
}

.order-priority.normal {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.order-priority.high {
  background-color: var(--warning-light);
  color: var(--warning-color);
}

.order-priority.urgent {
  background-color: var(--error-light);
  color: var(--error-color);
}

/* 客户信息 */
.customer-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 12rpx;
}

.customer-name {
  font-size: 28rpx;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.customer-contact {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 产品信息 */
.product-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.product-main {
  flex: 1;
}

.product-name {
  font-size: 28rpx;
  color: var(--text-primary);
  display: block;
  margin-bottom: 4rpx;
}

.product-spec {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.product-quantity {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
}

.quantity {
  font-size: 32rpx;
  font-weight: var(--font-weight-semibold);
  color: var(--primary-color);
}

.unit {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 重复样式已移除，使用下方统一的订单底部样式 */

/* 操作按钮 */
.order-actions {
  display: flex;
  gap: 16rpx;
}

.order-actions .action-btn {
  flex: 1;
  height: 64rpx;
  border-radius: var(--radius-base);
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.order-actions .action-btn.primary {
  background-color: var(--primary-color);
  color: #FFFFFF;
}

.order-actions .action-btn.secondary {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1rpx solid var(--border-color);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  color: var(--text-tertiary);
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

/* 加载更多 */
.load-more {
  padding: 20rpx;
  text-align: center;
}

.load-more-btn {
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-base);
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  color: var(--text-tertiary);
}

.loading-text {
  font-size: 28rpx;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  padding: 20rpx;
  border-top: 1rpx solid var(--border-color);
  z-index: 100;
}

.bottom-actions .action-btn {
  width: 100%;
  height: 80rpx;
  background-color: var(--primary-color);
  color: #FFFFFF;
  border: none;
  border-radius: var(--radius-base);
  font-size: 32rpx;
  font-weight: var(--font-weight-medium);
}

/* 设置菜单 */
.settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 120rpx 20rpx 0 0;
}

.settings-menu {
  background-color: #FFFFFF;
  border-radius: var(--radius-lg);
  padding: 16rpx 0;
  min-width: 240rpx;
  box-shadow: var(--shadow-lg);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  gap: 16rpx;
}

.menu-item:active {
  background-color: var(--bg-secondary);
}

.menu-icon {
  font-size: 28rpx;
}

.menu-text {
  font-size: 28rpx;
  color: var(--text-primary);
}

/* 信息区块 - 简约现代风格 */
.section-title {
  margin-bottom: 16rpx;
}

.title-text {
  font-size: 24rpx;
  font-weight: 500;
  color: #6E6E73;
  text-transform: uppercase;
  letter-spacing: 1rpx;
}

.customer-section,
.product-section,
.packaging-section {
  margin-bottom: 24rpx;
}

.packaging-section:last-of-type {
  margin-bottom: var(--spacing-lg);
}

/* 客户信息 */
.customer-info {
  font-size: 28rpx;
  color: #1D1D1F;
  line-height: 1.4;
}

.customer-name {
  font-size: 28rpx;
  color: #1D1D1F;
}

.customer-contact {
  font-size: 28rpx;
  color: #1D1D1F;
}

.customer-phone {
  font-size: 28rpx;
  color: #1D1D1F;
}

/* 产品信息 */
.product-info {
  font-size: 28rpx;
  color: #1D1D1F;
  line-height: 1.4;
}

.product-main {
  flex: 1;
}

.product-name {
  font-size: 28rpx;
  color: #1D1D1F;
}

.product-spec {
  font-size: 28rpx;
  color: #1D1D1F;
}

.product-quantity {
  display: flex;
  align-items: baseline;
  gap: var(--spacing-xs);
}

.quantity {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--primary-color);
}

.unit {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
}

/* 包装信息 */
.packaging-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.packaging-type {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.packaging-notes {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* 订单底部 */
.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #E5E5EA;
  font-size: 24rpx;
  color: #8E8E93;
}

.order-date {
  font-size: 24rpx;
  color: #8E8E93;
}

/* 操作按钮 */
.order-actions {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-base);
}

.order-actions .action-btn {
  flex: 1;
  height: var(--btn-height-sm);
  border-radius: var(--radius-base);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  border: none;
  transition: all var(--transition-base);
}

.order-actions .action-btn.primary {
  background-color: var(--primary-color);
  color: white;
}

.order-actions .action-btn.secondary {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: var(--border);
}
