// 工艺路线管理页面
const RouteService = require('../../../services/routeService')
const ProcessService = require('../../../services/processService')
const { formatDate } = require('../../../utils/dateUtils')

Page({
  data: {
    loading: false,
    routes: [],
    filteredRoutes: [],
    searchKeyword: '',
    
    // 弹窗相关
    showModal: false,
    isEditing: false,
    editingId: null,
    saving: false,
    isFormValid: false,
    formData: {
      name: '',
      category: '',
      description: '',
      processes: []
    },
    totalHours: 0,
    
    // 工序选择弹窗
    showProcessModal: false,
    availableProcesses: [],
    processSearchKeyword: '',
    
    // 删除确认弹窗
    showDeleteModal: false,
    deleteTarget: {},
    deleting: false
  },

  onLoad() {
    wx.setNavigationBarTitle({
      title: '工艺路线管理'
    })
    this.loadPageData()
  },

  onShow() {
    // 每次显示时刷新数据
    this.loadPageData()
  },

  onPullDownRefresh() {
    this.loadPageData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 加载页面数据
   */
  async loadPageData() {
    this.setData({ loading: true })
    
    try {
      await Promise.all([
        this.loadRoutes(),
        this.loadAvailableProcesses()
      ])
      this.filterRoutes()
    } catch (error) {
      console.error('加载页面数据失败:', error)
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 加载工艺路线列表
   */
  async loadRoutes() {
    try {
      const result = await RouteService.getRouteList()
      if (result.success) {
        // 格式化时间显示并获取工序名称
        const routes = await Promise.all(result.data.map(async route => {
          const processesWithNames = await Promise.all(route.processes.map(async process => {
            const processResult = await ProcessService.getProcessById(process.processId)
            return {
              ...process,
              processName: processResult.success ? processResult.data.name : process.processId
            }
          }))
          
          return {
            ...route,
            createdAt: formatDate(route.createdAt, 'MM-DD HH:mm'),
            processes: processesWithNames
          }
        }))
        
        this.setData({ routes })
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('加载工艺路线列表失败:', error)
      wx.showToast({
        title: '加载路线失败',
        icon: 'none'
      })
    }
  },

  /**
   * 加载可用工序列表
   */
  async loadAvailableProcesses() {
    try {
      const result = await ProcessService.getProcessList()
      if (result.success) {
        this.setData({ availableProcesses: result.data })
      }
    } catch (error) {
      console.error('加载工序列表失败:', error)
    }
  },

  /**
   * 筛选工艺路线
   */
  filterRoutes() {
    const { routes, searchKeyword } = this.data
    let filtered = [...routes]

    // 关键词搜索
    if (searchKeyword.trim()) {
      const keyword = searchKeyword.toLowerCase()
      filtered = filtered.filter(route =>
        route.name.toLowerCase().includes(keyword) ||
        route.description.toLowerCase().includes(keyword) ||
        route.category.toLowerCase().includes(keyword)
      )
    }

    this.setData({ filteredRoutes: filtered })
  },

  /**
   * 搜索输入处理
   */
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    })
  },

  /**
   * 搜索确认
   */
  onSearchConfirm() {
    this.filterRoutes()
  },

  /**
   * 显示创建工艺路线弹窗
   */
  showCreateModal() {
    this.setData({
      showModal: true,
      isEditing: false,
      editingId: null,
      formData: {
        name: '',
        category: '',
        description: '',
        processes: []
      },
      totalHours: 0
    })
    this.validateForm()
  },

  /**
   * 编辑工艺路线
   */
  async editRoute(e) {
    const route = e.currentTarget.dataset.route
    
    // 获取工序详细信息
    const processesWithDetails = await Promise.all(route.processes.map(async process => {
      const processResult = await ProcessService.getProcessById(process.processId)
      return {
        processId: process.processId,
        processName: processResult.success ? processResult.data.name : process.processId,
        estimatedHours: process.estimatedHours,
        order: process.order
      }
    }))
    
    this.setData({
      showModal: true,
      isEditing: true,
      editingId: route.id,
      formData: {
        name: route.name,
        category: route.category || '',
        description: route.description || '',
        processes: processesWithDetails
      }
    })
    this.calculateTotalHours()
    this.validateForm()
  },

  /**
   * 隐藏弹窗
   */
  hideModal() {
    this.setData({
      showModal: false,
      isEditing: false,
      editingId: null,
      saving: false,
      formData: {
        name: '',
        category: '',
        description: '',
        processes: []
      },
      totalHours: 0
    })
  },

  /**
   * 表单输入处理
   */
  onNameInput(e) {
    this.setData({
      'formData.name': e.detail.value
    })
    this.validateForm()
  },

  onCategoryInput(e) {
    this.setData({
      'formData.category': e.detail.value
    })
  },

  onDescriptionInput(e) {
    this.setData({
      'formData.description': e.detail.value
    })
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { formData } = this.data
    const isValid = formData.name.trim() && formData.processes.length > 0
    
    this.setData({ isFormValid: isValid })
  },

  /**
   * 计算总工时
   */
  calculateTotalHours() {
    const { formData } = this.data
    const totalHours = formData.processes.reduce((sum, process) => {
      return sum + (process.estimatedHours || 0)
    }, 0)
    
    this.setData({ totalHours })
  },

  /**
   * 显示工序选择弹窗
   */
  showProcessSelector() {
    this.setData({
      showProcessModal: true,
      processSearchKeyword: ''
    })
    this.filterAvailableProcesses()
  },

  /**
   * 隐藏工序选择弹窗
   */
  hideProcessModal() {
    this.setData({
      showProcessModal: false,
      processSearchKeyword: ''
    })
  },

  /**
   * 工序搜索输入处理
   */
  onProcessSearchInput(e) {
    this.setData({
      processSearchKeyword: e.detail.value
    })
    this.filterAvailableProcesses()
  },

  /**
   * 筛选可用工序
   */
  filterAvailableProcesses() {
    const { availableProcesses, processSearchKeyword, formData } = this.data
    
    // 过滤已选择的工序
    const selectedProcessIds = formData.processes.map(p => p.processId)
    let filtered = availableProcesses.filter(process => 
      !selectedProcessIds.includes(process.id)
    )

    // 关键词搜索
    if (processSearchKeyword.trim()) {
      const keyword = processSearchKeyword.toLowerCase()
      filtered = filtered.filter(process =>
        process.name.toLowerCase().includes(keyword) ||
        process.category.toLowerCase().includes(keyword)
      )
    }

    this.setData({ availableProcesses: filtered })
  },

  /**
   * 选择工序
   */
  selectProcess(e) {
    const process = e.currentTarget.dataset.process
    const { formData } = this.data
    
    const newProcess = {
      processId: process.id,
      processName: process.name,
      estimatedHours: process.estimatedHours,
      order: formData.processes.length + 1
    }
    
    formData.processes.push(newProcess)
    
    this.setData({
      'formData.processes': formData.processes,
      showProcessModal: false
    })
    
    this.calculateTotalHours()
    this.validateForm()
    this.loadAvailableProcesses() // 重新加载可用工序
  },

  /**
   * 移除工序
   */
  removeProcess(e) {
    const index = e.currentTarget.dataset.index
    const { formData } = this.data
    
    formData.processes.splice(index, 1)
    
    // 重新排序
    formData.processes.forEach((process, i) => {
      process.order = i + 1
    })
    
    this.setData({
      'formData.processes': formData.processes
    })
    
    this.calculateTotalHours()
    this.validateForm()
    this.loadAvailableProcesses() // 重新加载可用工序
  },

  /**
   * 上移工序
   */
  moveProcessUp(e) {
    const index = e.currentTarget.dataset.index
    if (index === 0) return
    
    const { formData } = this.data
    const processes = [...formData.processes]
    
    // 交换位置
    const temp = processes[index]
    processes[index] = processes[index - 1]
    processes[index - 1] = temp
    
    // 重新排序
    processes.forEach((process, i) => {
      process.order = i + 1
    })
    
    this.setData({
      'formData.processes': processes
    })
  },

  /**
   * 下移工序
   */
  moveProcessDown(e) {
    const index = e.currentTarget.dataset.index
    const { formData } = this.data
    
    if (index === formData.processes.length - 1) return
    
    const processes = [...formData.processes]
    
    // 交换位置
    const temp = processes[index]
    processes[index] = processes[index + 1]
    processes[index + 1] = temp
    
    // 重新排序
    processes.forEach((process, i) => {
      process.order = i + 1
    })
    
    this.setData({
      'formData.processes': processes
    })
  },

  /**
   * 保存工艺路线
   */
  async saveRoute() {
    const { isEditing, editingId, formData } = this.data

    if (!formData.name.trim()) {
      wx.showToast({
        title: '请输入工艺路线名称',
        icon: 'none'
      })
      return
    }

    if (formData.processes.length === 0) {
      wx.showToast({
        title: '请至少添加一个工序',
        icon: 'none'
      })
      return
    }

    this.setData({ saving: true })

    try {
      let result
      if (isEditing) {
        result = await RouteService.updateRoute(editingId, formData)
      } else {
        result = await RouteService.createRoute(formData)
      }

      if (result.success) {
        wx.showToast({
          title: result.message,
          icon: 'success'
        })

        this.hideModal()
        this.loadPageData()
      } else {
        wx.showToast({
          title: result.message,
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('保存工艺路线失败:', error)
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      })
    } finally {
      this.setData({ saving: false })
    }
  },

  /**
   * 删除工艺路线
   */
  deleteRoute(e) {
    const { id, name } = e.currentTarget.dataset
    this.setData({
      showDeleteModal: true,
      deleteTarget: { id, name }
    })
  },

  /**
   * 隐藏删除确认弹窗
   */
  hideDeleteModal() {
    this.setData({
      showDeleteModal: false,
      deleteTarget: {},
      deleting: false
    })
  },

  /**
   * 确认删除
   */
  async confirmDelete() {
    const { deleteTarget } = this.data

    this.setData({ deleting: true })

    try {
      const result = await RouteService.deleteRoute(deleteTarget.id)

      if (result.success) {
        wx.showToast({
          title: result.message,
          icon: 'success'
        })

        this.hideDeleteModal()
        this.loadPageData()
      } else {
        wx.showToast({
          title: result.message,
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('删除工艺路线失败:', error)
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      })
    } finally {
      this.setData({ deleting: false })
    }
  },

  /**
   * 查看工艺路线详情
   */
  viewRouteDetail(e) {
    const route = e.currentTarget.dataset.route
    const processNames = route.processes.map(p => p.processName).join(' → ')

    wx.showModal({
      title: '工艺路线详情',
      content: `名称：${route.name}\n分类：${route.category}\n工序：${processNames}\n总工时：${route.totalHours}小时\n描述：${route.description || '暂无描述'}\n创建时间：${route.createdAt}`,
      showCancel: false,
      confirmText: '知道了'
    })
  }
})
