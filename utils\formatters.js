/**
 * 格式化工具函数
 */

/**
 * 格式化日期
 * @param {Date|string} date 日期对象或日期字符串
 * @param {string} format 格式化模板，默认 'YYYY-MM-DD'
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(date, format = 'YYYY-MM-DD') {
  if (!date) return ''
  
  try {
    const d = new Date(date)
    
    // 检查日期是否有效
    if (isNaN(d.getTime())) {
      return ''
    }
    
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    const hour = String(d.getHours()).padStart(2, '0')
    const minute = String(d.getMinutes()).padStart(2, '0')
    const second = String(d.getSeconds()).padStart(2, '0')
    
    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hour)
      .replace('mm', minute)
      .replace('ss', second)
  } catch (error) {
    console.error('日期格式化失败:', error)
    return ''
  }
}

/**
 * 格式化日期时间
 * @param {Date|string} date 日期对象或日期字符串
 * @returns {string} 格式化后的日期时间字符串
 */
function formatDateTime(date) {
  if (!date) return ''
  
  try {
    const d = new Date(date)
    
    // 检查日期是否有效
    if (isNaN(d.getTime())) {
      return ''
    }
    
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    const hour = String(d.getHours()).padStart(2, '0')
    const minute = String(d.getMinutes()).padStart(2, '0')
    
    return `${year}-${month}-${day} ${hour}:${minute}`
  } catch (error) {
    console.error('日期时间格式化失败:', error)
    return ''
  }
}

/**
 * 获取状态文本
 * @param {string} status 状态值
 * @param {string} type 状态类型
 * @returns {string} 状态文本
 */
function getStatusText(status, type = 'order') {
  const statusMaps = {
    order: {
      'pending': '待确认',
      'confirmed': '已确认',
      'processing': '生产中',
      'completed': '已完成',
      'cancelled': '已取消'
    },
    workorder: {
      'draft': '草稿',
      'pending': '待开始',
      'in_progress': '进行中',
      'completed': '已完成',
      'cancelled': '已取消'
    },
    inventory: {
      'sufficient': '充足',
      'warning': '预警',
      'shortage': '不足'
    }
  }
  
  return statusMaps[type]?.[status] || status
}

/**
 * 获取优先级文本
 * @param {string} priority 优先级值
 * @returns {string} 优先级文本
 */
function getPriorityText(priority) {
  const priorityMap = {
    'low': '低',
    'normal': '普通',
    'high': '高',
    'urgent': '紧急'
  }
  
  return priorityMap[priority] || priority
}

module.exports = {
  formatDate,
  formatDateTime,
  getStatusText,
  getPriorityText
}
