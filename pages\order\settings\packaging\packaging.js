// 生产管理系统 - 包装管理页面
const PackagingService = require('../../../../services/packagingService.js')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    packagingList: [],
    showAddForm: false,
    formData: {
      name: '',
      type: '',
      description: '',
      specifications: '',
      notes: ''
    },
    editingId: null,
    loading: false,
    typeOptions: [
      { value: '标准包装', label: '标准包装' },
      { value: '防潮包装', label: '防潮包装' },
      { value: '木箱包装', label: '木箱包装' },
      { value: '托盘包装', label: '托盘包装' },
      { value: '定制包装', label: '定制包装' },
      { value: '其他', label: '其他' }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    wx.setNavigationBarTitle({
      title: '包装管理'
    })
    this.loadPackagingList()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.loadPackagingList()
  },

  /**
   * 加载包装列表
   */
  async loadPackagingList() {
    try {
      this.setData({ loading: true })
      const packagingList = await PackagingService.getPackagingList()
      this.setData({ packagingList })
    } catch (error) {
      console.error('加载包装列表失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 显示添加表单
   */
  showAddPackaging() {
    this.setData({
      showAddForm: true,
      editingId: null,
      formData: {
        name: '',
        type: '',
        description: '',
        specifications: '',
        notes: ''
      }
    })
  },

  /**
   * 编辑包装
   */
  editPackaging(e) {
    const packaging = e.currentTarget.dataset.packaging
    this.setData({
      showAddForm: true,
      editingId: packaging.id,
      formData: {
        name: packaging.name,
        type: packaging.type,
        description: packaging.description || '',
        specifications: packaging.specifications || '',
        notes: packaging.notes || ''
      }
    })
  },

  /**
   * 隐藏表单
   */
  hideForm() {
    this.setData({ showAddForm: false })
  },

  /**
   * 表单输入处理
   */
  onFormInput(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    this.setData({
      [`formData.${field}`]: value
    })
  },

  /**
   * 选择器变化处理
   */
  onPickerChange(e) {
    const { value } = e.detail
    const selectedType = this.data.typeOptions[value].value
    this.setData({
      'formData.type': selectedType
    })
  },

  /**
   * 保存包装
   */
  async savePackaging() {
    const { formData, editingId } = this.data
    
    // 表单验证
    if (!formData.name.trim()) {
      wx.showToast({
        title: '请输入包装名称',
        icon: 'none'
      })
      return
    }
    
    if (!formData.type.trim()) {
      wx.showToast({
        title: '请选择包装类型',
        icon: 'none'
      })
      return
    }

    try {
      this.setData({ loading: true })
      
      if (editingId) {
        // 更新包装
        await PackagingService.updatePackaging(editingId, formData)
        wx.showToast({
          title: '更新成功',
          icon: 'success'
        })
      } else {
        // 新增包装
        await PackagingService.createPackaging(formData)
        wx.showToast({
          title: '添加成功',
          icon: 'success'
        })
      }
      
      this.hideForm()
      this.loadPackagingList()
    } catch (error) {
      console.error('保存包装失败:', error)
      wx.showToast({
        title: error.message || '保存失败',
        icon: 'error'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 删除包装
   */
  async deletePackaging(e) {
    const { packaging } = e.currentTarget.dataset
    
    const result = await new Promise((resolve) => {
      wx.showModal({
        title: '确认删除',
        content: `确定要删除包装"${packaging.name}"吗？`,
        success: resolve
      })
    })
    
    if (!result.confirm) return
    
    try {
      this.setData({ loading: true })
      await PackagingService.deletePackaging(packaging.id)
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      })
      this.loadPackagingList()
    } catch (error) {
      console.error('删除包装失败:', error)
      wx.showToast({
        title: '删除失败',
        icon: 'error'
      })
    } finally {
      this.setData({ loading: false })
    }
  }
})
