<!--任务详情页面-->
<view class="page-container">
  <!-- 加载状态 -->
  <view wx:if="{{ loading }}" class="loading-container">
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 任务详情内容 -->
  <view wx:else class="detail-content">
    <!-- 任务基本信息 -->
    <view class="info-section">
      <view class="section-header">
        <text class="section-title">任务信息</text>
        <view class="status-badge {{ task.status }}">{{ task.statusText }}</view>
      </view>
      
      <view class="info-grid">
        <view class="info-item">
          <text class="info-label">工序名称</text>
          <text class="info-value">{{ task.processName }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">工单号</text>
          <text class="info-value">{{ task.workOrderNo }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">产品名称</text>
          <text class="info-value">{{ task.productName }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">产品编码</text>
          <text class="info-value">{{ task.productCode }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">工位</text>
          <text class="info-value">{{ task.workstation }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">车间</text>
          <text class="info-value">{{ task.workshop }}</text>
        </view>
      </view>
    </view>

    <!-- 进度信息 -->
    <view class="progress-section">
      <view class="section-header">
        <text class="section-title">任务进度</text>
        <text class="progress-percentage">{{ task.progressText }}</text>
      </view>
      
      <view class="progress-bar">
        <view 
          class="progress-fill {{ task.status }}" 
          style="width: {{ task.progress }}%"
        ></view>
      </view>
      
      <view class="progress-details">
        <text class="progress-text">{{ task.processedRatio }}%</text>
      </view>
    </view>

    <!-- 生产数据 -->
    <view class="production-section">
      <view class="section-header">
        <text class="section-title">生产数据</text>
        <text class="unit-text">单位：kg</text>
      </view>
      
      <view class="production-grid">
        <view class="production-item planned">
          <text class="production-label">计划数</text>
          <text class="production-value">{{ task.plannedQuantity }}</text>
        </view>
        <view class="production-item processed">
          <text class="production-label">已加工数</text>
          <text class="production-value">{{ task.processedQuantity }}</text>
        </view>
        <view class="production-item qualified">
          <text class="production-label">良品数</text>
          <text class="production-value">{{ task.qualifiedQuantity }}</text>
        </view>
        <view class="production-item defective">
          <text class="production-label">不良品数</text>
          <text class="production-value">{{ task.defectiveQuantity }}</text>
        </view>
      </view>
      
      <view class="quality-info">
        <text class="quality-label">良品率：</text>
        <text class="quality-value">{{ task.qualifiedRatio }}%</text>
      </view>
    </view>

    <!-- 分配信息 -->
    <view class="assignment-section">
      <view class="section-header">
        <text class="section-title">分配信息</text>
      </view>
      
      <view class="assignment-info">
        <view class="assignment-item">
          <text class="assignment-label">分配对象：</text>
          <text class="assignment-value {{ task.assignedTo ? 'assigned' : 'unassigned' }}">
            {{ task.assignedText }}
          </text>
        </view>
        <view wx:if="{{ task.assignedEmployee }}" class="assignment-item">
          <text class="assignment-label">所属部门：</text>
          <text class="assignment-value">{{ task.assignedEmployee.department }}</text>
        </view>
      </view>
    </view>

    <!-- 时间信息 -->
    <view class="time-section">
      <view class="section-header">
        <text class="section-title">时间信息</text>
      </view>
      
      <view class="time-grid">
        <view class="time-item">
          <text class="time-label">计划开始</text>
          <text class="time-value">{{ task.plannedStartTimeText }}</text>
        </view>
        <view class="time-item">
          <text class="time-label">计划结束</text>
          <text class="time-value">{{ task.plannedEndTimeText }}</text>
        </view>
        <view class="time-item">
          <text class="time-label">实际开始</text>
          <text class="time-value">{{ task.actualStartTimeText }}</text>
        </view>
        <view class="time-item">
          <text class="time-label">实际结束</text>
          <text class="time-value">{{ task.actualEndTimeText }}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <button class="action-btn report-btn" bindtap="showReportModal">
        员工报工
      </button>
      <button class="action-btn status-btn" bindtap="showStatusModal">
        修改状态
      </button>
    </view>
  </view>

  <!-- 报工弹窗 -->
  <view wx:if="{{ showReportModal }}" class="modal-overlay" bindtap="hideReportModal">
    <view class="report-modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">员工报工</text>
        <button class="modal-close" bindtap="hideReportModal">×</button>
      </view>
      
      <view class="report-content">
        <view class="report-item">
          <text class="report-label">已加工数量 (kg)</text>
          <input
            class="report-input"
            type="digit"
            value="{{ workReport.processedQuantity }}"
            placeholder="请输入已加工数量"
            data-field="processedQuantity"
            bindinput="onReportInput"
          />
        </view>
        
        <view class="report-item">
          <text class="report-label">良品数量 (kg)</text>
          <input
            class="report-input"
            type="digit"
            value="{{ workReport.qualifiedQuantity }}"
            placeholder="请输入良品数量"
            data-field="qualifiedQuantity"
            bindinput="onReportInput"
          />
        </view>
        
        <view class="report-item">
          <text class="report-label">不良品数量 (kg)</text>
          <input
            class="report-input"
            type="digit"
            value="{{ workReport.defectiveQuantity }}"
            placeholder="请输入不良品数量"
            data-field="defectiveQuantity"
            bindinput="onReportInput"
          />
        </view>
        
        <view class="report-item">
          <text class="report-label">工作时长 (小时)</text>
          <input
            class="report-input"
            type="digit"
            value="{{ workReport.workHours }}"
            placeholder="请输入工作时长"
            data-field="workHours"
            bindinput="onReportInput"
          />
        </view>
        
        <view class="report-item">
          <text class="report-label">备注说明</text>
          <textarea
            class="report-textarea"
            value="{{ workReport.notes }}"
            placeholder="请输入备注说明"
            data-field="notes"
            bindinput="onReportInput"
          />
        </view>
        
        <view class="modal-actions">
          <button class="cancel-btn" bindtap="hideReportModal">取消</button>
          <button class="confirm-btn" bindtap="submitWorkReport">提交报工</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 状态修改弹窗 -->
  <view wx:if="{{ showStatusModal }}" class="modal-overlay" bindtap="hideStatusModal">
    <view class="status-modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">修改状态</text>
        <button class="modal-close" bindtap="hideStatusModal">×</button>
      </view>
      
      <view class="status-content">
        <view class="current-status">
          <text class="current-label">当前状态：</text>
          <text class="current-value">{{ task.statusText }}</text>
        </view>
        
        <view class="status-select">
          <text class="select-label">选择新状态</text>
          <picker
            range="{{ statusOptions }}"
            range-key="label"
            value="{{ selectedStatusIndex }}"
            bindchange="onStatusChange"
          >
            <view class="picker-display">
              {{ selectedStatusIndex >= 0 ? statusOptions[selectedStatusIndex].label : '请选择状态' }}
            </view>
          </picker>
        </view>
        
        <view class="modal-actions">
          <button class="cancel-btn" bindtap="hideStatusModal">取消</button>
          <button class="confirm-btn" bindtap="confirmStatusChange">确认修改</button>
        </view>
      </view>
    </view>
  </view>
</view>
