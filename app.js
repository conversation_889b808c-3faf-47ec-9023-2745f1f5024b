// ERP管理系统 - 小程序入口文件
App({
  globalData: {
    userInfo: null,
    systemInfo: null,
    version: '1.0.0'
  },
  
  onLaunch(options) {
    console.log('ERP小程序启动', options)
    
    // 获取系统信息
    wx.getSystemInfo({
      success: (res) => {
        this.globalData.systemInfo = res
        console.log('系统信息:', res)
      },
      fail: (err) => {
        console.error('获取系统信息失败:', err)
      }
    })
    
    // 检查更新
    this.checkForUpdate()
  },
  
  onShow(options) {
    console.log('ERP小程序显示', options)
  },
  
  onHide() {
    console.log('ERP小程序隐藏')
  },
  
  onError(msg) {
    console.error('ERP小程序错误:', msg)
    
    // 错误上报
    wx.showToast({
      title: '系统异常，请重试',
      icon: 'none',
      duration: 2000
    })
  },
  
  onUnhandledRejection(res) {
    console.error('未处理的Promise拒绝:', res)
  },
  
  // 检查小程序更新
  checkForUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager()
      
      updateManager.onCheckForUpdate((res) => {
        console.log('检查更新结果:', res.hasUpdate)
      })
      
      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate()
            }
          }
        })
      })
      
      updateManager.onUpdateFailed(() => {
        console.error('新版本下载失败')
      })
    }
  },
  
  // 全局工具方法
  utils: {
    // 格式化日期
    formatDate(date, format = 'YYYY-MM-DD') {
      if (!date) return ''
      
      const d = new Date(date)
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')
      const hour = String(d.getHours()).padStart(2, '0')
      const minute = String(d.getMinutes()).padStart(2, '0')
      const second = String(d.getSeconds()).padStart(2, '0')
      
      return format
        .replace('YYYY', year)
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hour)
        .replace('mm', minute)
        .replace('ss', second)
    },
    
    // 显示加载提示
    showLoading(title = '加载中...') {
      wx.showLoading({
        title,
        mask: true
      })
    },
    
    // 隐藏加载提示
    hideLoading() {
      wx.hideLoading()
    },
    
    // 显示成功提示
    showSuccess(title = '操作成功') {
      wx.showToast({
        title,
        icon: 'success',
        duration: 2000
      })
    },
    
    // 显示错误提示
    showError(title = '操作失败') {
      wx.showToast({
        title,
        icon: 'none',
        duration: 2000
      })
    }
  }
})
