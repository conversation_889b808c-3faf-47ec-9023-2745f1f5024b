// 任务详情页面逻辑
const TaskService = require('../../../services/taskService.js')
const { formatDate } = require('../../../utils/dateUtils.js')

Page({
  data: {
    // 任务详情数据
    task: null,
    loading: true,
    
    // 报工数据
    workReport: {
      processedQuantity: 0,
      qualifiedQuantity: 0,
      defectiveQuantity: 0,
      workHours: 0,
      notes: ''
    },
    
    // 状态选项
    statusOptions: [
      { label: '待开始', value: 'pending' },
      { label: '进行中', value: 'in_progress' },
      { label: '已完成', value: 'completed' },
      { label: '已暂停', value: 'paused' }
    ],
    
    // UI状态
    showReportModal: false,
    showStatusModal: false,
    selectedStatusIndex: -1,
    
    // 员工信息
    employeeList: []
  },

  onLoad(options) {
    console.log('任务详情页面加载，接收参数:', options)
    console.log('任务ID类型:', typeof options.id, '值:', options.id)

    if (options.id) {
      this.taskId = options.id
      console.log('保存任务ID到实例:', this.taskId)
      this.initPage()
    } else {
      console.error('任务ID缺失，接收到的参数:', options)
      wx.showToast({
        title: '任务ID缺失',
        icon: 'error'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  onShow() {
    // 从其他页面返回时刷新数据
    if (this.taskId) {
      this.loadTaskDetail()
    }
  },

  onPullDownRefresh() {
    this.loadTaskDetail()
  },

  /**
   * 初始化页面
   */
  async initPage() {
    await this.loadEmployeeList()
    await this.loadTaskDetail()
  },

  /**
   * 加载员工列表
   */
  async loadEmployeeList() {
    try {
      const result = await TaskService.getEmployeeList()
      if (result.success) {
        this.setData({ employeeList: result.data })
      }
    } catch (error) {
      console.error('加载员工列表失败:', error)
    }
  },

  /**
   * 加载任务详情
   */
  async loadTaskDetail() {
    this.setData({ loading: true })

    try {
      console.log('开始加载任务详情，任务ID:', this.taskId)
      console.log('任务ID类型:', typeof this.taskId)

      const result = await TaskService.getTaskById(this.taskId)

      console.log('TaskService.getTaskById 返回结果:', result)

      if (result.success && result.data) {
        const task = this.formatTaskForDisplay(result.data)
        this.setData({ task })

        // 设置导航栏标题
        wx.setNavigationBarTitle({
          title: task.processName
        })

        console.log('任务详情加载成功:', task)
      } else {
        console.error('任务详情加载失败:', result)
        wx.showToast({
          title: result.message || '任务不存在',
          icon: 'error'
        })
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      }
    } catch (error) {
      console.error('加载任务详情失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
      wx.stopPullDownRefresh()
    }
  },

  /**
   * 格式化任务数据用于显示
   */
  formatTaskForDisplay(task) {
    const statusMap = {
      'pending': '待开始',
      'in_progress': '进行中',
      'completed': '已完成',
      'paused': '已暂停'
    }

    return {
      ...task,
      statusText: statusMap[task.status] || task.status,
      progressText: `${task.progress}%`,
      assignedText: task.assignedEmployee ? task.assignedEmployee.name : '待分配',
      plannedStartTimeText: formatDate(task.plannedStartTime, 'YYYY-MM-DD HH:mm'),
      plannedEndTimeText: formatDate(task.plannedEndTime, 'YYYY-MM-DD HH:mm'),
      actualStartTimeText: task.actualStartTime ? formatDate(task.actualStartTime, 'YYYY-MM-DD HH:mm') : '未开始',
      actualEndTimeText: task.actualEndTime ? formatDate(task.actualEndTime, 'YYYY-MM-DD HH:mm') : '未完成',
      processedRatio: task.plannedQuantity > 0 ? (task.processedQuantity / task.plannedQuantity * 100).toFixed(1) : 0,
      qualifiedRatio: task.processedQuantity > 0 ? (task.qualifiedQuantity / task.processedQuantity * 100).toFixed(1) : 0
    }
  },

  /**
   * 显示报工弹窗
   */
  showReportModal() {
    const { task } = this.data
    this.setData({ 
      showReportModal: true,
      'workReport.processedQuantity': task.processedQuantity,
      'workReport.qualifiedQuantity': task.qualifiedQuantity,
      'workReport.defectiveQuantity': task.defectiveQuantity,
      'workReport.workHours': 0,
      'workReport.notes': ''
    })
  },

  /**
   * 隐藏报工弹窗
   */
  hideReportModal() {
    this.setData({ showReportModal: false })
  },

  /**
   * 报工数据输入
   */
  onReportInput(e) {
    const field = e.currentTarget.dataset.field
    const value = e.detail.value
    this.setData({
      [`workReport.${field}`]: field === 'notes' ? value : (parseFloat(value) || 0)
    })
  },

  /**
   * 提交报工
   */
  async submitWorkReport() {
    const { workReport, task } = this.data
    
    // 数据验证
    if (workReport.processedQuantity < 0 || workReport.qualifiedQuantity < 0 || workReport.defectiveQuantity < 0) {
      wx.showToast({
        title: '数量不能为负数',
        icon: 'none'
      })
      return
    }
    
    if (workReport.qualifiedQuantity + workReport.defectiveQuantity > workReport.processedQuantity) {
      wx.showToast({
        title: '良品数+不良品数不能大于已加工数',
        icon: 'none'
      })
      return
    }
    
    try {
      const updateData = {
        processedQuantity: workReport.processedQuantity,
        qualifiedQuantity: workReport.qualifiedQuantity,
        defectiveQuantity: workReport.defectiveQuantity
      }
      
      // 如果任务还未开始，设置开始时间
      if (task.status === 'pending' && workReport.processedQuantity > 0) {
        updateData.status = 'in_progress'
        updateData.actualStartTime = new Date().toISOString()
      }
      
      // 如果已完成全部数量，设置完成状态
      if (workReport.processedQuantity >= task.plannedQuantity) {
        updateData.status = 'completed'
        updateData.actualEndTime = new Date().toISOString()
      }
      
      const result = await TaskService.updateTask(task.id, updateData)
      
      if (result.success) {
        wx.showToast({
          title: '报工成功',
          icon: 'success'
        })
        
        this.hideReportModal()
        this.loadTaskDetail()
      } else {
        wx.showToast({
          title: result.message || '报工失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('提交报工失败:', error)
      wx.showToast({
        title: '报工失败，请重试',
        icon: 'none'
      })
    }
  },

  /**
   * 显示状态修改弹窗
   */
  showStatusModal() {
    const { task, statusOptions } = this.data
    const currentIndex = statusOptions.findIndex(option => option.value === task.status)
    this.setData({ 
      showStatusModal: true,
      selectedStatusIndex: currentIndex
    })
  },

  /**
   * 隐藏状态修改弹窗
   */
  hideStatusModal() {
    this.setData({ showStatusModal: false })
  },

  /**
   * 选择状态
   */
  onStatusChange(e) {
    this.setData({ selectedStatusIndex: e.detail.value })
  },

  /**
   * 确认修改状态
   */
  async confirmStatusChange() {
    const { selectedStatusIndex, statusOptions, task } = this.data
    
    if (selectedStatusIndex === -1) {
      wx.showToast({
        title: '请选择状态',
        icon: 'none'
      })
      return
    }
    
    const newStatus = statusOptions[selectedStatusIndex].value
    
    if (newStatus === task.status) {
      this.hideStatusModal()
      return
    }
    
    try {
      const updateData = { status: newStatus }
      
      // 根据状态变更设置时间
      if (newStatus === 'in_progress' && !task.actualStartTime) {
        updateData.actualStartTime = new Date().toISOString()
      } else if (newStatus === 'completed' && !task.actualEndTime) {
        updateData.actualEndTime = new Date().toISOString()
      }
      
      const result = await TaskService.updateTask(task.id, updateData)
      
      if (result.success) {
        wx.showToast({
          title: '状态更新成功',
          icon: 'success'
        })
        
        this.hideStatusModal()
        this.loadTaskDetail()
      } else {
        wx.showToast({
          title: result.message || '更新失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('更新状态失败:', error)
      wx.showToast({
        title: '更新失败，请重试',
        icon: 'none'
      })
    }
  },

  /**
   * 返回任务列表
   */
  goBack() {
    wx.navigateBack()
  }
})
