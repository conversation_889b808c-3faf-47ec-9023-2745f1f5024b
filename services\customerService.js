/**
 * 客户管理服务 - 重构版本
 * 基于BaseService实现，提供统一的客户管理功能
 */

const BaseService = require('./base/BaseService')
const mockDataManager = require('./base/MockDataManager')
const { rules } = require('../utils/validators')

/**
 * 获取客户列表
 */
function getCustomerList(params = {}) {
  return new Promise((resolve) => {
    setTimeout(() => {
      let result = [...mockCustomers]
      
      // 搜索过滤
      if (params.keyword) {
        const keyword = params.keyword.toLowerCase()
        result = result.filter(customer => 
          customer.name.toLowerCase().includes(keyword) ||
          customer.contact.toLowerCase().includes(keyword) ||
          (customer.phone && customer.phone.includes(keyword))
        )
      }
      
      // 排序
      result.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      
      resolve(result)
    }, 300)
  })
}

/**
 * 根据ID获取客户信息
 */
function getCustomerById(id) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const customer = mockCustomers.find(item => item.id === id)
      if (customer) {
        resolve(customer)
      } else {
        reject(new Error('客户不存在'))
      }
    }, 200)
  })
}

/**
 * 创建客户
 */
function createCustomer(customerData) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      // 验证必填字段
      if (!customerData.name || !customerData.contact) {
        reject(new Error('客户名称和联系人为必填项'))
        return
      }
      
      // 检查客户名称是否重复
      const exists = mockCustomers.some(customer => customer.name === customerData.name)
      if (exists) {
        reject(new Error('客户名称已存在'))
        return
      }
      
      const newCustomer = {
        id: generateId(),
        name: customerData.name,
        contact: customerData.contact,
        phone: customerData.phone || '',
        address: customerData.address || '',
        email: customerData.email || '',
        notes: customerData.notes || '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      mockCustomers.unshift(newCustomer)
      resolve(newCustomer)
    }, 500)
  })
}

/**
 * 更新客户信息
 */
function updateCustomer(id, customerData) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockCustomers.findIndex(customer => customer.id === id)
      if (index === -1) {
        reject(new Error('客户不存在'))
        return
      }
      
      // 验证必填字段
      if (!customerData.name || !customerData.contact) {
        reject(new Error('客户名称和联系人为必填项'))
        return
      }
      
      // 检查客户名称是否重复（排除自己）
      const exists = mockCustomers.some(customer => 
        customer.name === customerData.name && customer.id !== id
      )
      if (exists) {
        reject(new Error('客户名称已存在'))
        return
      }
      
      const updatedCustomer = {
        ...mockCustomers[index],
        name: customerData.name,
        contact: customerData.contact,
        phone: customerData.phone || '',
        address: customerData.address || '',
        email: customerData.email || '',
        notes: customerData.notes || '',
        updatedAt: new Date().toISOString()
      }
      
      mockCustomers[index] = updatedCustomer
      resolve(updatedCustomer)
    }, 500)
  })
}

/**
 * 删除客户
 */
function deleteCustomer(id) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockCustomers.findIndex(customer => customer.id === id)
      if (index === -1) {
        reject(new Error('客户不存在'))
        return
      }
      
      const deletedCustomer = mockCustomers.splice(index, 1)[0]
      resolve(deletedCustomer)
    }, 300)
  })
}

/**
 * 获取客户选项列表（用于下拉选择）
 */
function getCustomerOptions() {
  return new Promise((resolve) => {
    setTimeout(() => {
      const options = mockCustomers.map(customer => ({
        value: customer.id,
        label: customer.name,
        contact: customer.contact,
        phone: customer.phone
      }))
      resolve(options)
    }, 200)
  })
}

module.exports = {
  getCustomerList,
  getCustomerById,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  getCustomerOptions
}
