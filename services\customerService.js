/**
 * 客户管理服务
 * 提供客户信息的增删改查功能
 */

// 模拟客户数据
let mockCustomers = [
  {
    id: 'customer_001',
    name: '华东制造有限公司',
    contact: '张经理',
    phone: '13800138001',
    address: '上海市浦东新区张江高科技园区',
    email: '<EMAIL>',
    notes: '重要客户，长期合作伙伴',
    createdAt: '2024-01-01T08:00:00.000Z',
    updatedAt: '2024-01-01T08:00:00.000Z'
  },
  {
    id: 'customer_002',
    name: '江南机械集团',
    contact: '李总',
    phone: '13800138002',
    address: '江苏省苏州市工业园区',
    email: '<EMAIL>',
    notes: '大客户，订单量大',
    createdAt: '2024-01-02T09:15:00.000Z',
    updatedAt: '2024-01-02T09:15:00.000Z'
  },
  {
    id: 'customer_003',
    name: '北方重工股份',
    contact: '王主管',
    phone: '13800138003',
    address: '辽宁省沈阳市铁西区',
    email: '<EMAIL>',
    notes: '国企客户，付款及时',
    createdAt: '2024-01-03T11:30:00.000Z',
    updatedAt: '2024-01-03T11:30:00.000Z'
  },
  {
    id: 'customer_004',
    name: '南方电机制造',
    contact: '赵工程师',
    phone: '13800138004',
    address: '广东省深圳市南山区',
    email: '<EMAIL>',
    notes: '技术要求高，质量标准严格',
    createdAt: '2024-01-04T14:20:00.000Z',
    updatedAt: '2024-01-04T14:20:00.000Z'
  }
]

/**
 * 生成唯一ID
 */
function generateId() {
  return 'customer_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

/**
 * 获取客户列表
 */
function getCustomerList(params = {}) {
  return new Promise((resolve) => {
    setTimeout(() => {
      let result = [...mockCustomers]
      
      // 搜索过滤
      if (params.keyword) {
        const keyword = params.keyword.toLowerCase()
        result = result.filter(customer => 
          customer.name.toLowerCase().includes(keyword) ||
          customer.contact.toLowerCase().includes(keyword) ||
          (customer.phone && customer.phone.includes(keyword))
        )
      }
      
      // 排序
      result.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      
      resolve(result)
    }, 300)
  })
}

/**
 * 根据ID获取客户信息
 */
function getCustomerById(id) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const customer = mockCustomers.find(item => item.id === id)
      if (customer) {
        resolve(customer)
      } else {
        reject(new Error('客户不存在'))
      }
    }, 200)
  })
}

/**
 * 创建客户
 */
function createCustomer(customerData) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      // 验证必填字段
      if (!customerData.name || !customerData.contact) {
        reject(new Error('客户名称和联系人为必填项'))
        return
      }
      
      // 检查客户名称是否重复
      const exists = mockCustomers.some(customer => customer.name === customerData.name)
      if (exists) {
        reject(new Error('客户名称已存在'))
        return
      }
      
      const newCustomer = {
        id: generateId(),
        name: customerData.name,
        contact: customerData.contact,
        phone: customerData.phone || '',
        address: customerData.address || '',
        email: customerData.email || '',
        notes: customerData.notes || '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      mockCustomers.unshift(newCustomer)
      resolve(newCustomer)
    }, 500)
  })
}

/**
 * 更新客户信息
 */
function updateCustomer(id, customerData) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockCustomers.findIndex(customer => customer.id === id)
      if (index === -1) {
        reject(new Error('客户不存在'))
        return
      }
      
      // 验证必填字段
      if (!customerData.name || !customerData.contact) {
        reject(new Error('客户名称和联系人为必填项'))
        return
      }
      
      // 检查客户名称是否重复（排除自己）
      const exists = mockCustomers.some(customer => 
        customer.name === customerData.name && customer.id !== id
      )
      if (exists) {
        reject(new Error('客户名称已存在'))
        return
      }
      
      const updatedCustomer = {
        ...mockCustomers[index],
        name: customerData.name,
        contact: customerData.contact,
        phone: customerData.phone || '',
        address: customerData.address || '',
        email: customerData.email || '',
        notes: customerData.notes || '',
        updatedAt: new Date().toISOString()
      }
      
      mockCustomers[index] = updatedCustomer
      resolve(updatedCustomer)
    }, 500)
  })
}

/**
 * 删除客户
 */
function deleteCustomer(id) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockCustomers.findIndex(customer => customer.id === id)
      if (index === -1) {
        reject(new Error('客户不存在'))
        return
      }
      
      const deletedCustomer = mockCustomers.splice(index, 1)[0]
      resolve(deletedCustomer)
    }, 300)
  })
}

/**
 * 获取客户选项列表（用于下拉选择）
 */
function getCustomerOptions() {
  return new Promise((resolve) => {
    setTimeout(() => {
      const options = mockCustomers.map(customer => ({
        value: customer.id,
        label: customer.name,
        contact: customer.contact,
        phone: customer.phone
      }))
      resolve(options)
    }, 200)
  })
}

module.exports = {
  getCustomerList,
  getCustomerById,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  getCustomerOptions
}
