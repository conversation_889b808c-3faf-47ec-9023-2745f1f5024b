<!--订单创建第四步：确认提交-->
<view class="step-container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <view class="navbar-left" bindtap="goBack">
        <text class="back-icon">‹</text>
        <text class="back-text">上一步</text>
      </view>
      <text class="navbar-title">创建订单 - 确认提交</text>
      <view class="navbar-right"></view>
    </view>
  </view>

  <!-- 步骤指示器 -->
  <view class="step-indicator">
    <view class="step-item">
      <view class="step-number completed">✓</view>
      <text class="step-text">基本信息</text>
    </view>
    <view class="step-line completed"></view>
    <view class="step-item">
      <view class="step-number completed">✓</view>
      <text class="step-text">选择产品</text>
    </view>
    <view class="step-line completed"></view>
    <view class="step-item">
      <view class="step-number completed">✓</view>
      <text class="step-text">配置详情</text>
    </view>
    <view class="step-line completed"></view>
    <view class="step-item active">
      <view class="step-number">4</view>
      <text class="step-text">确认提交</text>
    </view>
  </view>

  <!-- 订单预览 -->
  <view class="preview-container">
    <scroll-view class="preview-content" scroll-y>
      <!-- 基本信息 -->
      <view class="preview-section">
        <view class="section-header">
          <text class="section-title">订单基本信息</text>
          <button class="edit-btn" bindtap="editBasicInfo">编辑</button>
        </view>
        <view class="info-grid">
          <view class="info-item">
            <text class="info-label">订单编号</text>
            <text class="info-value">{{ orderData.orderNo }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">客户名称</text>
            <text class="info-value">{{ selectedCustomer.name }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">交货日期</text>
            <text class="info-value">{{ orderData.deliveryDate }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">优先级</text>
            <text class="info-value priority-{{ orderData.priority }}">
              {{ priorityLabel }}
            </text>
          </view>
        </view>
        <view class="info-item full-width" wx:if="{{ orderData.notes }}">
          <text class="info-label">订单备注</text>
          <text class="info-value">{{ orderData.notes }}</text>
        </view>
      </view>

      <!-- 产品清单 -->
      <view class="preview-section">
        <view class="section-header">
          <text class="section-title">产品清单 ({{ selectedProducts.length }}项)</text>
          <button class="edit-btn" bindtap="editProducts">编辑</button>
        </view>
        <view class="product-list">
          <view 
            class="product-preview"
            wx:for="{{ selectedProducts }}"
            wx:key="id"
          >
            <view class="product-main">
              <view class="product-header">
                <text class="product-name">{{ item.name }}</text>
                <text class="product-code">{{ item.code }}</text>
              </view>
              <view class="product-details">
                <text class="product-spec">{{ item.specification }}</text>
                <view class="product-meta">
                  <text class="meta-item">数量：{{ item.quantity }} {{ item.unit }}</text>
                  <text class="meta-item total">小计：￥{{ item.subtotal }}</text>
                </view>
              </view>
            </view>
            <view class="product-config">
              <view class="config-item">
                <text class="config-label">包装</text>
                <text class="config-value">{{ item.packaging.name }}</text>
              </view>
              <view class="config-item">
                <text class="config-label">生产天数</text>
                <text class="config-value">{{ item.estimatedDays }}天</text>
              </view>
              <view class="config-item" wx:if="{{ item.specialRequirements }}">
                <text class="config-label">特殊要求</text>
                <text class="config-value">{{ item.specialRequirements }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 订单汇总 -->
      <view class="preview-section">
        <view class="section-title">订单汇总</view>
        <view class="summary-grid">
          <view class="summary-item">
            <text class="summary-label">产品总数</text>
            <text class="summary-value">{{ totalQuantity }} 项</text>
          </view>
          <view class="summary-item">
            <text class="summary-label">预计完成</text>
            <text class="summary-value">{{ maxEstimatedDays }} 天</text>
          </view>
        </view>
      </view>

      <!-- 工单创建选项 -->
      <view class="preview-section">
        <view class="section-title">工单创建</view>
        <view class="workorder-options">
          <label class="checkbox-item">
            <checkbox 
              checked="{{ createWorkOrder }}"
              bindchange="onCreateWorkOrderChange"
            />
            <text class="checkbox-text">订单提交后自动创建工单</text>
          </label>
          <view class="option-desc">
            <text class="desc-text">选择此项将在订单创建成功后自动为每个产品创建对应的生产工单</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <button class="btn-secondary" bindtap="saveDraft">保存草稿</button>
    <button 
      class="btn-primary" 
      bindtap="submitOrder"
      loading="{{ submitting }}"
    >
      {{ submitting ? '提交中...' : '确认提交订单' }}
    </button>
  </view>
</view>

<!-- 成功提示弹窗 -->
<view class="success-modal" wx:if="{{ showSuccessModal }}">
  <view class="modal-content">
    <view class="success-icon">✓</view>
    <text class="success-title">订单创建成功！</text>
    <text class="success-desc">订单编号：{{ orderData.orderNo }}</text>
    <view class="success-actions">
      <button class="success-btn" bindtap="viewOrder">查看订单</button>
      <button class="success-btn primary" bindtap="createNewOrder">继续创建</button>
    </view>
  </view>
</view>
