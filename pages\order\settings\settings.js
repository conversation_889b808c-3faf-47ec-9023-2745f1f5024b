// 生产管理系统 - 设置页面
Page({
  /**
   * 页面的初始数据
   */
  data: {
    settingsOptions: [
      {
        id: 'customer',
        title: '新建客户公司',
        icon: '🏢',
        description: '管理客户公司信息',
        url: '/pages/order/settings/customer/customer'
      },
      {
        id: 'product',
        title: '新建产品信息',
        icon: '📦',
        description: '管理产品基础信息',
        url: '/pages/order/settings/product/product'
      },
      {
        id: 'category',
        title: '产品分类管理',
        icon: '🏷️',
        description: '管理产品分类和标签',
        url: '/pages/order/settings/category/category'
      },
      {
        id: 'packaging',
        title: '新建包装信息',
        icon: '📋',
        description: '管理包装类型和规格',
        url: '/pages/order/settings/packaging/packaging'
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    wx.setNavigationBarTitle({
      title: '设置'
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉刷新
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 跳转到指定设置页面
   */
  navigateToSetting(e) {
    const { url } = e.currentTarget.dataset
    wx.navigateTo({
      url: url
    })
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack()
  }
})
