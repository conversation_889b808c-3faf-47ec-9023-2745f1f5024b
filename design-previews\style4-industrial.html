<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>风格4 - 工业风格</title>
    <style>
        /*
        设计理念：工业风格
        - 色彩：深灰色主色调，橙色警示色，金属质感
        - 特点：硬朗、实用、工业感强
        - 适用：制造业、工厂、重工业企业
        - 字体：等宽字体，技术感
        - 圆角：直角或小圆角，硬朗感
        */
        
        :root {
            --primary-color: #FF6B35;
            --secondary-color: #2C3E50;
            --accent-color: #F39C12;
            --success-color: #27AE60;
            --warning-color: #E67E22;
            --error-color: #E74C3C;
            
            --text-primary: #2C3E50;
            --text-secondary: #34495E;
            --text-tertiary: #7F8C8D;
            --text-inverse: #FFFFFF;
            --text-warning: #D35400;
            
            --bg-primary: #FFFFFF;
            --bg-secondary: #ECF0F1;
            --bg-tertiary: #BDC3C7;
            --bg-dark: #2C3E50;
            --bg-metal: linear-gradient(135deg, #BDC3C7 0%, #95A5A6 100%);
            
            --border-color: #95A5A6;
            --border-dark: #34495E;
            --shadow-light: 0 2px 4px rgba(44, 62, 80, 0.1);
            --shadow-medium: 0 4px 8px rgba(44, 62, 80, 0.15);
            --shadow-heavy: 0 8px 16px rgba(44, 62, 80, 0.2);
            
            --radius-sm: 2px;
            --radius-md: 4px;
            --radius-lg: 6px;
            
            --spacing-xs: 8px;
            --spacing-sm: 12px;
            --spacing-md: 16px;
            --spacing-lg: 24px;
            --spacing-xl: 32px;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.4;
            width: 375px;
            margin: 0 auto;
            min-height: 100vh;
        }
        
        .container {
            background-color: var(--bg-secondary);
            min-height: 100vh;
            position: relative;
        }
        
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                repeating-linear-gradient(
                    90deg,
                    transparent,
                    transparent 2px,
                    rgba(149, 165, 166, 0.1) 2px,
                    rgba(149, 165, 166, 0.1) 4px
                );
            pointer-events: none;
        }
        
        /* 导航栏 */
        .navbar {
            background: var(--bg-dark);
            padding: var(--spacing-lg) var(--spacing-md);
            color: var(--text-inverse);
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: var(--shadow-heavy);
            border-bottom: 3px solid var(--primary-color);
        }
        
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-sm);
        }
        
        .nav-title {
            font-size: 18px;
            font-weight: 700;
            color: var(--text-inverse);
            text-transform: uppercase;
            letter-spacing: 2px;
            font-family: 'Courier New', monospace;
        }
        
        .nav-subtitle {
            font-size: 10px;
            color: var(--primary-color);
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-top: 2px;
        }
        
        .nav-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn {
            padding: var(--spacing-sm) var(--spacing-md);
            border: 2px solid;
            border-radius: var(--radius-sm);
            font-size: 12px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.2s ease;
            font-family: 'Courier New', monospace;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--text-inverse);
        }
        
        .btn-secondary {
            background-color: transparent;
            border-color: var(--border-color);
            color: var(--text-inverse);
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-medium);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        /* 订单列表 */
        .order-list {
            padding: var(--spacing-md);
            position: relative;
            z-index: 1;
        }
        
        .order-card {
            background-color: var(--bg-primary);
            border: 2px solid var(--border-color);
            border-left: 6px solid var(--primary-color);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-md);
            box-shadow: var(--shadow-light);
            transition: all 0.2s ease;
            position: relative;
        }
        
        .order-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 0 20px 20px 0;
            border-color: transparent var(--bg-tertiary) transparent transparent;
        }
        
        .order-card:hover {
            border-left-color: var(--accent-color);
            box-shadow: var(--shadow-medium);
        }
        
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--spacing-md);
            padding-bottom: var(--spacing-sm);
            border-bottom: 1px dashed var(--border-color);
        }
        
        .order-number {
            font-size: 16px;
            font-weight: 700;
            color: var(--text-primary);
            font-family: 'Courier New', monospace;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .order-id {
            font-size: 10px;
            color: var(--text-tertiary);
            margin-top: 2px;
        }
        
        .order-status {
            padding: 6px 12px;
            border: 2px solid;
            font-size: 10px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-family: 'Courier New', monospace;
            position: relative;
        }
        
        .status-pending {
            background-color: #FFF3CD;
            color: var(--text-warning);
            border-color: var(--warning-color);
        }
        
        .status-confirmed {
            background-color: #D4EDDA;
            color: #155724;
            border-color: var(--success-color);
        }
        
        .order-section {
            margin-bottom: var(--spacing-md);
            border-left: 2px solid var(--bg-tertiary);
            padding-left: var(--spacing-md);
        }
        
        .section-title {
            font-size: 10px;
            font-weight: 700;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 2px;
            margin-bottom: var(--spacing-xs);
            font-family: 'Courier New', monospace;
            position: relative;
        }
        
        .section-title::before {
            content: '▶';
            color: var(--primary-color);
            margin-right: var(--spacing-xs);
        }
        
        .section-content {
            font-size: 13px;
            color: var(--text-primary);
            line-height: 1.4;
            font-family: 'Courier New', monospace;
        }
        
        .company-name {
            font-weight: 700;
            color: var(--text-primary);
            text-transform: uppercase;
        }
        
        .spec-code {
            color: var(--primary-color);
            font-weight: 700;
        }
        
        .order-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm);
            background: var(--bg-metal);
            border: 1px solid var(--border-color);
            font-size: 11px;
            color: var(--text-secondary);
            font-family: 'Courier New', monospace;
            text-transform: uppercase;
        }
        
        .order-date {
            font-weight: 700;
            letter-spacing: 1px;
        }
        
        .order-amount {
            font-size: 14px;
            font-weight: 700;
            color: var(--primary-color);
            font-family: 'Courier New', monospace;
        }
        
        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 375px;
            background: var(--bg-dark);
            border-top: 3px solid var(--primary-color);
            padding: var(--spacing-sm) 0;
            display: flex;
            justify-content: space-around;
            box-shadow: 0 -4px 8px rgba(44, 62, 80, 0.2);
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: var(--spacing-xs);
            color: var(--text-tertiary);
            text-decoration: none;
            font-size: 9px;
            font-weight: 700;
            transition: all 0.2s ease;
            font-family: 'Courier New', monospace;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .nav-item.active {
            color: var(--primary-color);
        }
        
        .nav-icon {
            width: 18px;
            height: 18px;
            background-color: currentColor;
            border: 1px solid currentColor;
        }
        
        .nav-item:hover {
            color: var(--accent-color);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 导航栏 -->
        <div class="navbar">
            <div class="nav-header">
                <div>
                    <h1 class="nav-title">ORDER MGMT SYS</h1>
                    <div class="nav-subtitle">INDUSTRIAL v2.1.0</div>
                </div>
                <div class="nav-actions">
                    <button class="btn btn-primary">NEW</button>
                    <button class="btn btn-secondary">CFG</button>
                </div>
            </div>
        </div>
        
        <!-- 订单列表 -->
        <div class="order-list">
            <div class="order-card">
                <div class="order-header">
                    <div>
                        <div class="order-number">PO20240101001</div>
                        <div class="order-id">ID: #ORD-001</div>
                    </div>
                    <div class="order-status status-pending">PENDING</div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">CLIENT INFO</div>
                    <div class="section-content">
                        <div class="company-name">HUADONG MACHINERY MFG CO.</div>
                        CONTACT: ZHANG ENG. | TEL: 13800138000
                    </div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">PRODUCT SPEC</div>
                    <div class="section-content">
                        PRECISION GEAR <span class="spec-code">[GEAR001]</span> × 100 PCS<br>
                        HIGH-PRECISION TRANSMISSION GEAR FOR MACHINERY
                    </div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">PACKAGING</div>
                    <div class="section-content">
                        MOISTURE-PROOF PKG | LEAD TIME: 3-5 DAYS
                    </div>
                </div>
                
                <div class="order-meta">
                    <span class="order-date">2024-01-15 09:30</span>
                    <span class="order-amount">¥15,000.00</span>
                </div>
            </div>
            
            <div class="order-card">
                <div class="order-header">
                    <div>
                        <div class="order-number">PO20240101002</div>
                        <div class="order-id">ID: #ORD-002</div>
                    </div>
                    <div class="order-status status-confirmed">CONFIRMED</div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">CLIENT INFO</div>
                    <div class="section-content">
                        <div class="company-name">JIANGNAN PRECISION TECH CO.</div>
                        CONTACT: LI MGR. | TEL: 13900139000
                    </div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">PRODUCT SPEC</div>
                    <div class="section-content">
                        STAINLESS STEEL PLATE <span class="spec-code">[STEEL002]</span> × 50 SQM<br>
                        304 STAINLESS STEEL PLATE, THICKNESS: 3MM
                    </div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">PACKAGING</div>
                    <div class="section-content">
                        STANDARD PKG | LEAD TIME: 2-3 DAYS
                    </div>
                </div>
                
                <div class="order-meta">
                    <span class="order-date">2024-01-14 14:20</span>
                    <span class="order-amount">¥4,275.00</span>
                </div>
            </div>
        </div>
        
        <!-- 底部导航 -->
        <div class="bottom-nav">
            <a href="#" class="nav-item">
                <div class="nav-icon"></div>
                <span>HOME</span>
            </a>
            <a href="#" class="nav-item active">
                <div class="nav-icon"></div>
                <span>ORDER</span>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon"></div>
                <span>WORK</span>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon"></div>
                <span>PKG</span>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon"></div>
                <span>STOCK</span>
            </a>
        </div>
    </div>
</body>
</html>
