<!--生产管理系统 - 订单列表页面-->
<view class="page-container">
  <!-- 页面头部操作区 - 简约现代风格 -->
  <view class="header-actions">
    <button class="btn btn-primary" bindtap="createOrder">
      新建订单
    </button>
    <button class="btn btn-secondary" bindtap="openSettings">
      设置
    </button>
  </view>

  <!-- 搜索栏 -->
  <view class="search-container">
    <view class="search-box">
      <input
        class="search-input"
        value="{{ searchKeyword }}"
        placeholder="搜索订单号、客户名称或产品"
        bindinput="onSearchInput"
        bindconfirm="onSearchConfirm"
      />
      <text class="clear-btn" wx:if="{{ searchKeyword }}" bindtap="clearSearch">清除</text>
    </view>
  </view>

  <!-- 状态筛选 -->
  <view class="filter-container">
    <scroll-view class="filter-scroll" scroll-x>
      <view class="filter-tabs">
        <view
          class="filter-tab {{ activeStatus === item.value ? 'active' : '' }}"
          wx:for="{{ statusOptions }}"
          wx:key="value"
          bindtap="onStatusChange"
          data-status="{{ item.value }}"
        >
          {{ item.label }}
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 订单列表 -->
  <view class="order-list">
    <view
      class="order-card"
      wx:for="{{ orderList }}"
      wx:key="id"
      bindtap="viewOrderDetail"
      data-order="{{ item }}"
    >
      <!-- 订单头部 -->
      <view class="order-header">
        <text class="order-no">{{ item.orderNo }}</text>
        <view class="order-status {{ item.status }}">
          {{ item.statusText }}
        </view>
      </view>

      <!-- 客户信息 - 简约现代风格 -->
      <view class="customer-section">
        <view class="section-title">
          <text class="title-text">客户信息</text>
        </view>
        <view class="customer-info">
          <text class="customer-name">{{ item.customerName }}</text>
          <text class="customer-contact">联系人：{{ item.customerContact }}</text>
          <text class="customer-phone" wx:if="{{ item.customerPhone }}">电话：{{ item.customerPhone }}</text>
        </view>
      </view>

      <!-- 产品信息 - 简约现代风格 -->
      <view class="product-section">
        <view class="section-title">
          <text class="title-text">产品信息</text>
        </view>
        <view class="product-info">
          <view class="product-main">
            <text class="product-name">{{ item.productName }}</text>
            <text class="product-spec" wx:if="{{ item.specification }}">规格：{{ item.specification }}</text>
          </view>
          <view class="product-quantity">
            <text class="quantity">{{ item.quantity }}</text>
            <text class="unit">{{ item.unit }}</text>
          </view>
        </view>
      </view>

      <!-- 包装信息 - 简约现代风格 -->
      <view class="packaging-section" wx:if="{{ item.packagingType }}">
        <view class="section-title">
          <text class="title-text">包装信息</text>
        </view>
        <view class="packaging-info">
          <text class="packaging-type">{{ item.packagingType }}</text>
          <text class="packaging-notes" wx:if="{{ item.packagingNotes }}">备注：{{ item.packagingNotes }}</text>
        </view>
      </view>

      <!-- 订单底部 -->
      <view class="order-footer">
        <text class="order-date">{{ item.createdAtText }}</text>
      </view>

      <!-- 操作按钮 -->
      <view class="order-actions">
        <button
          class="action-btn secondary"
          bindtap="editOrder"
          data-order="{{ item }}"
          catchtap="true"
        >
          编辑
        </button>
        <button
          class="action-btn primary"
          bindtap="createWorkOrder"
          data-order="{{ item }}"
          catchtap="true"
          wx:if="{{ item.status === 'confirmed' }}"
        >
          创建工单
        </button>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{ !loading && orderList.length === 0 }}">
    <view class="empty-icon">📋</view>
    <text class="empty-text">
      {{ searchKeyword ? '未找到匹配的订单' : '暂无订单数据' }}
    </text>
    <button class="btn btn-primary" bindtap="createOrder" wx:if="{{ !searchKeyword }}">
      创建第一个订单
    </button>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{ hasMore && !loading }}">
    <button class="load-more-btn" bindtap="loadMore">
      加载更多
    </button>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{ loading }}">
    <text class="loading-text">加载中...</text>
  </view>


</view>
