# 工单管理系统 - 工序管理功能文档

## 功能概述

本文档描述了工单管理系统中新增的工序管理功能，包括工序管理、工艺路线管理以及在工单创建中的应用。

## 核心功能

### 1. 工序管理 (Process Management)

#### 功能特性
- **工序模板管理**: 创建、编辑、删除工序模板
- **工序分类**: 支持工序分类管理（成型、加工、表面处理等）
- **工时估算**: 每个工序包含预计工时信息
- **搜索功能**: 支持按工序名称搜索

#### 数据结构
```javascript
{
  id: 'proc_001',
  name: '压铸',
  description: '压铸成型工序',
  estimatedHours: 2.0,
  category: '成型',
  createdAt: '2024-01-01T00:00:00.000Z',
  updatedAt: '2024-01-01T00:00:00.000Z'
}
```

#### 页面位置
- 设置 → 工序管理 (`pages/settings/process/process.*`)

### 2. 工艺路线管理 (Process Route Management)

#### 功能特性
- **路线模板管理**: 创建、编辑、删除工艺路线模板
- **工序组合**: 将多个工序按顺序组合成完整的生产路线
- **自动计算**: 自动计算路线总工时
- **工序排序**: 支持工序顺序调整

#### 数据结构
```javascript
{
  id: 'route_001',
  name: 'GYLX20230003加工件（氧化）',
  description: '包含工序：压铸,去毛刺,车,钻,铣,氧化',
  processes: [
    { processId: 'proc_001', order: 1, estimatedHours: 2.0 },
    { processId: 'proc_002', order: 2, estimatedHours: 0.5 }
  ],
  totalHours: 11.5,
  createdAt: '2024-01-01T00:00:00.000Z',
  updatedAt: '2024-01-01T00:00:00.000Z'
}
```

#### 页面位置
- 设置 → 工艺路线管理 (`pages/settings/route/route.*`)

### 3. 工单创建中的工序配置

#### 功能特性
- **应用工艺路线**: 一键应用预设的工艺路线到工单
- **手动添加工序**: 从工序库中选择单个工序添加
- **工序编辑**: 支持修改工序名称、描述、工时
- **工序排序**: 支持工序上移、下移调整顺序
- **工序删除**: 支持移除不需要的工序
- **总工时显示**: 实时显示所有工序的总预计工时

#### 使用流程
1. 选择工单创建模式（从订单创建 / 手动创建）
2. 填写基本信息
3. 在工序配置部分：
   - 点击"应用工艺路线"选择预设路线
   - 或点击"添加工序"手动添加单个工序
4. 调整工序顺序和参数
5. 查看总工时并创建工单

## 技术实现

### 服务层 (Services)

#### ProcessService (`services/processService.js`)
- `getProcessList()`: 获取工序列表
- `getProcessById(id)`: 获取单个工序详情
- `createProcess(data)`: 创建新工序
- `updateProcess(id, data)`: 更新工序信息
- `deleteProcess(id)`: 删除工序
- `searchProcesses(keyword)`: 搜索工序

#### RouteService (`services/routeService.js`)
- `getRouteList()`: 获取工艺路线列表
- `getRouteById(id)`: 获取单个工艺路线详情
- `createRoute(data)`: 创建新工艺路线
- `updateRoute(id, data)`: 更新工艺路线信息
- `deleteRoute(id)`: 删除工艺路线

### 页面组件

#### 工序管理页面 (`pages/settings/process/process.*`)
- 工序列表展示
- 工序搜索和分类筛选
- 工序创建/编辑弹窗
- 工序删除确认

#### 工艺路线管理页面 (`pages/settings/route/route.*`)
- 工艺路线列表展示
- 路线创建/编辑弹窗
- 工序选择和排序
- 总工时自动计算

#### 工单创建页面增强 (`pages/workorder/create/create.*`)
- 工序配置区域
- 工艺路线选择弹窗
- 工序选择弹窗
- 工序编辑弹窗
- 工序列表管理

### 数据存储

使用微信小程序本地存储：
- `erp_processes`: 工序数据
- `erp_routes`: 工艺路线数据
- 工单数据中包含 `processes` 字段存储关联的工序信息

## 设计特色

### 简约现代风格
- 采用蓝白配色方案 (#007AFF)
- 圆角卡片设计
- 微妙阴影效果
- 流畅的交互动画

### 用户体验优化
- 直观的工序排序控制
- 实时的总工时计算
- 友好的空状态提示
- 响应式布局适配

### 数据完整性
- 完整的CRUD操作
- 数据验证和错误处理
- 关联数据同步更新
- 默认数据初始化

## 使用说明

### 管理员操作
1. 进入"设置"页面
2. 选择"工序管理"或"工艺路线管理"
3. 创建和维护工序模板和工艺路线模板

### 生产人员操作
1. 创建工单时，在工序配置部分
2. 选择合适的工艺路线或手动添加工序
3. 根据实际情况调整工序参数
4. 确认总工时后创建工单

## 扩展性

该工序管理系统设计具有良好的扩展性：
- 支持工序状态跟踪
- 可扩展工序质量检查点
- 支持工序成本核算
- 可集成设备和人员分配

## 注意事项

1. 工序删除前会检查是否被工艺路线引用
2. 工艺路线删除前会检查是否被工单使用
3. 工序工时修改会影响相关工艺路线的总工时
4. 建议定期备份工序和工艺路线数据
