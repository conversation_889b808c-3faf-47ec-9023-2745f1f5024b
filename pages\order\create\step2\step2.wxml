<!--订单创建第二步：选择产品-->
<view class="step-container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <view class="navbar-left" bindtap="goBack">
        <text class="back-icon">‹</text>
        <text class="back-text">上一步</text>
      </view>
      <text class="navbar-title">创建订单 - 选择产品</text>
      <view class="navbar-right"></view>
    </view>
  </view>

  <!-- 步骤指示器 -->
  <view class="step-indicator">
    <view class="step-item">
      <view class="step-number completed">✓</view>
      <text class="step-text">基本信息</text>
    </view>
    <view class="step-line completed"></view>
    <view class="step-item active">
      <view class="step-number">2</view>
      <text class="step-text">选择产品</text>
    </view>
    <view class="step-line"></view>
    <view class="step-item">
      <view class="step-number">3</view>
      <text class="step-text">配置详情</text>
    </view>
    <view class="step-line"></view>
    <view class="step-item">
      <view class="step-number">4</view>
      <text class="step-text">确认提交</text>
    </view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-container">
    <view class="search-box">
      <text class="search-icon">🔍</text>
      <input 
        class="search-input"
        placeholder="搜索产品名称或编码"
        value="{{ searchKeyword }}"
        bindinput="onSearchInput"
      />
    </view>
  </view>

  <!-- 产品类别 -->
  <view class="category-container">
    <scroll-view class="category-scroll" scroll-x>
      <view class="category-list">
        <view 
          class="category-item {{ selectedCategoryId === item.id ? 'active' : '' }}"
          wx:for="{{ categories }}"
          wx:key="id"
          bindtap="selectCategory"
          data-id="{{ item.id }}"
        >
          <text class="category-name">{{ item.name }}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 产品列表 -->
  <view class="product-container">
    <scroll-view class="product-list" scroll-y>
      <view 
        class="product-item"
        wx:for="{{ filteredProducts }}"
        wx:key="id"
      >
        <view class="product-info">
          <view class="product-header">
            <text class="product-name">{{ item.name }}</text>
            <text class="product-code">{{ item.code }}</text>
          </view>
          <text class="product-spec">{{ item.specification }}</text>
          <view class="product-meta">
            <text class="product-unit">单位：{{ item.unit }}</text>
          </view>
        </view>
        
        <view class="product-actions">
          <view class="quantity-control" wx:if="{{ item.selected }}">
            <button 
              class="quantity-btn"
              bindtap="decreaseQuantity"
              data-id="{{ item.id }}"
              disabled="{{ item.quantity <= 1 }}"
            >-</button>
            <input 
              class="quantity-input"
              type="number"
              value="{{ item.quantity }}"
              bindinput="onQuantityInput"
              data-id="{{ item.id }}"
            />
            <button 
              class="quantity-btn"
              bindtap="increaseQuantity"
              data-id="{{ item.id }}"
            >+</button>
          </view>
          
          <button 
            class="select-btn {{ item.selected ? 'selected' : '' }}"
            bindtap="toggleProduct"
            data-id="{{ item.id }}"
          >
            {{ item.selected ? '已选择' : '选择' }}
          </button>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{ filteredProducts.length === 0 }}">
        <text class="empty-icon">📦</text>
        <text class="empty-text">暂无产品数据</text>
        <text class="empty-desc">请先添加产品信息</text>
      </view>
    </scroll-view>
  </view>

  <!-- 已选产品摘要 -->
  <view class="selected-summary" wx:if="{{ selectedProducts.length > 0 }}">
    <view class="summary-header">
      <text class="summary-title">已选择 {{ selectedProducts.length }} 个产品</text>
    </view>
    <scroll-view class="summary-list" scroll-x>
      <view class="summary-item" wx:for="{{ selectedProducts }}" wx:key="id">
        <text class="summary-name">{{ item.name }}</text>
        <text class="summary-quantity">×{{ item.quantity }}</text>
      </view>
    </scroll-view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <button class="btn-secondary" bindtap="saveDraft">保存草稿</button>
    <button 
      class="btn-primary" 
      bindtap="nextStep" 
      disabled="{{ !isFormValid }}"
    >
      下一步：配置详情
    </button>
  </view>
</view>
