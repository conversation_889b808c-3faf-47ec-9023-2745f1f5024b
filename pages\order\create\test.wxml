<!--简化测试版本-->
<view class="page">
  <view class="container">
    <view class="title">创建订单</view>
    
    <!-- 订单号 -->
    <view class="form-group">
      <text class="label">订单编号</text>
      <input class="input" value="{{ orderNo }}" disabled />
    </view>
    
    <!-- 客户信息 -->
    <view class="form-group">
      <text class="label">客户名称</text>
      <picker range="{{ customerOptions }}" range-key="name" bindchange="selectCustomer">
        <view class="picker">
          {{ formData.customerName || '请选择客户' }}
        </view>
      </picker>
    </view>
    
    <!-- 产品信息 -->
    <view class="form-group">
      <text class="label">产品名称</text>
      <picker range="{{ productOptions }}" range-key="name" bindchange="selectProduct">
        <view class="picker">
          {{ formData.productName || '请选择产品' }}
        </view>
      </picker>
    </view>
    
    <!-- 数量 -->
    <view class="form-group">
      <text class="label">数量</text>
      <view class="quantity-row">
        <button class="qty-btn" bindtap="decreaseQuantity">-</button>
        <input class="qty-input" type="number" value="{{ formData.quantity }}" bindinput="onQuantityInput" />
        <button class="qty-btn" bindtap="increaseQuantity">+</button>
      </view>
    </view>
    

  </view>
  
  <!-- 底部按钮 -->
  <view class="bottom-bar">
    <button class="btn btn-cancel" bindtap="cancel">取消</button>
    <button class="btn btn-submit" bindtap="submitOrder">提交订单</button>
  </view>
</view>
