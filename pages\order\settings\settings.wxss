/* 生产管理系统 - 设置页面样式 - 简约现代风格 */
@import '../../../styles/design-tokens.wxss';
@import '../../../styles/modern-components.wxss';

.page-container {
  min-height: 100vh;
  background-color: var(--bg-page);
}

/* 页面头部 */
.page-header {
  background-color: var(--bg-primary);
  padding: var(--spacing-2xl) var(--spacing-base) var(--spacing-xl);
  border-bottom: var(--border);
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.page-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.page-subtitle {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
}

/* 设置选项列表 */
.settings-list {
  padding: var(--spacing-base);
}

.setting-item {
  background-color: var(--bg-primary);
  border-radius: var(--card-border-radius);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-base);
  box-shadow: var(--card-shadow);
  border: var(--border);
  display: flex;
  align-items: center;
  gap: var(--spacing-base);
  transition: all var(--transition-base);
}

.setting-item:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-sm);
}

.setting-icon {
  width: 80rpx;
  height: 80rpx;
  background-color: var(--primary-light);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-text {
  font-size: var(--font-size-2xl);
}

.setting-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.setting-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.setting-description {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.setting-arrow {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-icon {
  font-size: var(--font-size-xl);
  color: var(--text-tertiary);
  font-weight: var(--font-weight-bold);
}

/* 底部说明 */
.footer-info {
  padding: var(--spacing-2xl) var(--spacing-base);
  text-align: center;
}

.info-text {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
  line-height: var(--line-height-relaxed);
}
