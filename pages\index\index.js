// 首页逻辑
const OrderService = require('../../services/orderService')
const { formatDate, getRelativeTime } = require('../../utils/dateUtils')
const { getStatusText } = require('../../utils/common')

Page({
  data: {
    loading: false,
    // 统计数据
    orderStats: {
      total: 0,
      todayNew: 0
    },
    workOrderStats: {
      total: 0,
      processing: 0
    },
    // 待处理事项
    pendingTasks: [],
    // 最近订单
    recentOrders: []
  },

  onLoad() {
    console.log('首页加载')
    this.loadPageData()
  },

  onShow() {
    console.log('首页显示')
    // 每次显示时刷新数据
    this.loadPageData()
  },

  onPullDownRefresh() {
    console.log('首页下拉刷新')
    this.loadPageData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 加载页面数据
   */
  async loadPageData() {
    this.setData({ loading: true })
    
    try {
      await Promise.all([
        this.loadOrderStats(),
        this.loadWorkOrderStats(),
        this.loadPendingTasks(),
        this.loadRecentOrders()
      ])
    } catch (error) {
      console.error('加载页面数据失败:', error)
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 加载订单统计
   */
  async loadOrderStats() {
    try {
      const result = await OrderService.getOrderList()
      if (result.success) {
        const orders = result.data
        const today = new Date().toDateString()
        const todayNew = orders.filter(order => 
          new Date(order.createdAt).toDateString() === today
        ).length

        this.setData({
          orderStats: {
            total: orders.length,
            todayNew
          }
        })
      }
    } catch (error) {
      console.error('加载订单统计失败:', error)
    }
  },

  /**
   * 加载工单统计
   */
  async loadWorkOrderStats() {
    try {
      // 模拟工单数据
      const mockWorkOrderStats = {
        total: 15,
        processing: 8
      }
      
      this.setData({
        workOrderStats: mockWorkOrderStats
      })
    } catch (error) {
      console.error('加载工单统计失败:', error)
    }
  },

  /**
   * 加载待处理事项
   */
  async loadPendingTasks() {
    try {
      // 模拟待处理事项
      const mockTasks = [
        {
          id: 'task_001',
          type: 'order',
          icon: '📋',
          title: '订单待确认',
          description: '3个订单等待确认',
          timeText: '2小时前',
          action: 'viewPendingOrders'
        },
        {
          id: 'task_002',
          type: 'workorder',
          icon: '🏭',
          title: '工单即将到期',
          description: 'PO20240101001 预计明天完成',
          timeText: '1天后',
          action: 'viewWorkOrder',
          params: { id: 'wo_001' }
        },
        {
          id: 'task_003',
          type: 'inventory',
          icon: '⚠️',
          title: '库存预警',
          description: '5个产品库存不足',
          timeText: '刚刚',
          action: 'viewInventoryWarning'
        }
      ]
      
      this.setData({
        pendingTasks: mockTasks
      })
    } catch (error) {
      console.error('加载待处理事项失败:', error)
    }
  },

  /**
   * 加载最近订单
   */
  async loadRecentOrders() {
    try {
      const result = await OrderService.getOrderList({ pageSize: 5 })
      if (result.success) {
        const orders = result.data.map(order => ({
          ...order,
          statusText: getStatusText(order.status, 'order'),
          timeText: getRelativeTime(order.createdAt)
        }))
        
        this.setData({
          recentOrders: orders
        })
      }
    } catch (error) {
      console.error('加载最近订单失败:', error)
    }
  },

  /**
   * 导航到订单页面
   */
  navigateToOrders() {
    wx.navigateTo({
      url: '/pages/order/list/list'
    })
  },

  /**
   * 导航到工单页面
   */
  navigateToWorkOrders() {
    wx.switchTab({
      url: '/pages/workorder/list/list'
    })
  },

  /**
   * 创建订单
   */
  createOrder() {
    wx.switchTab({
      url: '/pages/order/create/create'
    })
  },

  /**
   * 创建工单
   */
  createWorkOrder() {
    wx.navigateTo({
      url: '/pages/workorder/create/create'
    })
  },

  /**
   * 入库操作
   */
  inventoryIn() {
    wx.switchTab({
      url: '/pages/inventory/inout/inout'
    })
    // 设置入库模式
    setTimeout(() => {
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      if (currentPage.setOperationType) {
        currentPage.setOperationType('in')
      }
    }, 100)
  },

  /**
   * 出库操作
   */
  inventoryOut() {
    wx.switchTab({
      url: '/pages/inventory/inout/inout'
    })
    // 设置出库模式
    setTimeout(() => {
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      if (currentPage.setOperationType) {
        currentPage.setOperationType('out')
      }
    }, 100)
  },

  /**
   * 查看库存
   */
  checkInventory() {
    wx.navigateTo({
      url: '/pages/inventory/list/list'
    })
  },

  /**
   * 查看包装管理
   */
  viewPackaging() {
    wx.switchTab({
      url: '/pages/packaging/list/list'
    })
  },

  /**
   * 查看报表
   */
  viewReports() {
    wx.showToast({
      title: '报表功能开发中',
      icon: 'none'
    })
  },

  /**
   * 打开系统设置
   */
  openSettings() {
    wx.navigateTo({
      url: '/pages/settings/index/index'
    })
  },

  /**
   * 处理待办事项
   */
  handleTask(e) {
    const task = e.currentTarget.dataset.task
    console.log('处理待办事项:', task)
    
    switch (task.action) {
      case 'viewPendingOrders':
        wx.navigateTo({
          url: '/pages/order/list/list'
        })
        break
      case 'viewWorkOrder':
        wx.navigateTo({
          url: `/pages/workorder/detail/detail?id=${task.params.id}`
        })
        break
      case 'viewInventoryWarning':
        wx.navigateTo({
          url: '/pages/inventory/list/list?status=warning'
        })
        break
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        })
    }
  },

  /**
   * 查看所有订单
   */
  viewAllOrders() {
    wx.navigateTo({
      url: '/pages/order/list/list'
    })
  },

  /**
   * 查看订单详情
   */
  viewOrderDetail(e) {
    const order = e.currentTarget.dataset.order
    wx.navigateTo({
      url: `/pages/order/detail/detail?id=${order.id}`
    })
  }
})
