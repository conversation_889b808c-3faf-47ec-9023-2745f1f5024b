/* 订单创建第三步样式 */
@import "../step1/step1.wxss";

/* 配置容器 */
.config-container {
  flex: 1;
  padding: 0 20rpx;
  margin-bottom: 20rpx;
}

.config-list {
  height: 100%;
}

.config-item {
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

/* 产品头部 */
.product-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #F0F0F0;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.product-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1D1D1F;
}

.product-code {
  font-size: 24rpx;
  color: #8E8E93;
  background: #F8F9FA;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}

.product-quantity {
  text-align: right;
}

.quantity-text {
  font-size: 28rpx;
  color: #007AFF;
  font-weight: 600;
}

/* 配置表单 */
.config-form {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  font-size: 28rpx;
  color: #1D1D1F;
  margin-bottom: 16rpx;
  font-weight: 500;
}

/* 天数控制 */
.days-control {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.days-btn {
  width: 60rpx;
  height: 60rpx;
  background: #F8F9FA !important;
  color: #1D1D1F !important;
  border: 2rpx solid #F0F0F0 !important;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.days-btn[disabled] {
  background: #F0F0F0 !important;
  color: #C7C7CC !important;
}

.days-input {
  width: 120rpx;
  height: 60rpx;
  text-align: center;
  border: 2rpx solid #F0F0F0;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.days-unit {
  font-size: 28rpx;
  color: #8E8E93;
}

/* 错误信息 */
.error-message {
  margin-top: 16rpx;
}

.error-text {
  font-size: 24rpx;
  color: #FF3B30;
}

/* 批量操作 */
.batch-actions {
  background: #FFFFFF;
  padding: 20rpx;
  margin: 0 20rpx 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.batch-header {
  margin-bottom: 20rpx;
}

.batch-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1D1D1F;
}

.batch-buttons {
  display: flex;
  gap: 16rpx;
}

.batch-btn {
  flex: 1;
  height: 72rpx;
  background: #E3F2FD !important;
  color: #007AFF !important;
  border: 2rpx solid #007AFF !important;
  border-radius: 8rpx;
  font-size: 26rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}
