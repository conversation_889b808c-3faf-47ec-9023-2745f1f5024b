<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>风格1 - 简约现代风格</title>
    <style>
        /*
        设计理念：简约现代风格
        - 色彩：以白色为主，蓝色为辅，灰色点缀
        - 特点：大量留白、简洁线条、现代感强
        - 适用：追求简洁高效的现代企业
        - 字体：无衬线字体，清晰易读
        - 圆角：适中圆角，现代感
        */
        
        :root {
            --primary-color: #007AFF;
            --secondary-color: #5856D6;
            --success-color: #34C759;
            --warning-color: #FF9500;
            --error-color: #FF3B30;
            
            --text-primary: #1D1D1F;
            --text-secondary: #6E6E73;
            --text-tertiary: #8E8E93;
            
            --bg-primary: #FFFFFF;
            --bg-secondary: #F2F2F7;
            --bg-tertiary: #FAFAFA;
            
            --border-color: #E5E5EA;
            --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.08);
            --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.12);
            
            --radius-sm: 8px;
            --radius-md: 12px;
            --radius-lg: 16px;
            
            --spacing-xs: 8px;
            --spacing-sm: 12px;
            --spacing-md: 16px;
            --spacing-lg: 24px;
            --spacing-xl: 32px;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--bg-tertiary);
            color: var(--text-primary);
            line-height: 1.5;
            width: 375px;
            margin: 0 auto;
            min-height: 100vh;
        }
        
        .container {
            background-color: var(--bg-tertiary);
            min-height: 100vh;
        }
        
        /* 导航栏 */
        .navbar {
            background-color: var(--bg-primary);
            padding: var(--spacing-lg) var(--spacing-md);
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-md);
        }
        
        .nav-title {
            font-size: 28px;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .nav-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn {
            padding: var(--spacing-sm) var(--spacing-md);
            border: none;
            border-radius: var(--radius-md);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-secondary {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-medium);
        }
        
        /* 订单列表 */
        .order-list {
            padding: var(--spacing-md);
        }
        
        .order-card {
            background-color: var(--bg-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-md);
            box-shadow: var(--shadow-light);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }
        
        .order-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }
        
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--spacing-md);
        }
        
        .order-number {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .order-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-pending {
            background-color: #FFF3CD;
            color: #856404;
        }
        
        .status-confirmed {
            background-color: #D1ECF1;
            color: #0C5460;
        }
        
        .order-section {
            margin-bottom: var(--spacing-md);
        }
        
        .section-title {
            font-size: 12px;
            font-weight: 500;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: var(--spacing-xs);
        }
        
        .section-content {
            font-size: 14px;
            color: var(--text-primary);
            line-height: 1.4;
        }
        
        .order-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: var(--spacing-md);
            border-top: 1px solid var(--border-color);
            font-size: 12px;
            color: var(--text-tertiary);
        }
        
        .order-date {
            font-weight: 500;
        }
        
        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 375px;
            background-color: var(--bg-primary);
            border-top: 1px solid var(--border-color);
            padding: var(--spacing-sm) 0;
            display: flex;
            justify-content: space-around;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: var(--spacing-xs);
            color: var(--text-tertiary);
            text-decoration: none;
            font-size: 10px;
            transition: color 0.2s ease;
        }
        
        .nav-item.active {
            color: var(--primary-color);
        }
        
        .nav-icon {
            width: 24px;
            height: 24px;
            background-color: currentColor;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 导航栏 -->
        <div class="navbar">
            <div class="nav-header">
                <h1 class="nav-title">订单管理</h1>
                <div class="nav-actions">
                    <button class="btn btn-primary">新建订单</button>
                    <button class="btn btn-secondary">设置</button>
                </div>
            </div>
        </div>
        
        <!-- 订单列表 -->
        <div class="order-list">
            <div class="order-card">
                <div class="order-header">
                    <div class="order-number">PO20240101001</div>
                    <div class="order-status status-pending">待确认</div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">客户信息</div>
                    <div class="section-content">
                        华东机械制造有限公司<br>
                        联系人：张工 | 13800138000
                    </div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">产品信息</div>
                    <div class="section-content">
                        精密齿轮 (GEAR001) × 100件<br>
                        高精度传动齿轮，适用于各种机械设备
                    </div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">包装信息</div>
                    <div class="section-content">防潮包装 | 预计3-5个工作日</div>
                </div>
                
                <div class="order-meta">
                    <span class="order-date">2024-01-15 09:30</span>
                    <span>￥15,000.00</span>
                </div>
            </div>
            
            <div class="order-card">
                <div class="order-header">
                    <div class="order-number">PO20240101002</div>
                    <div class="order-status status-confirmed">已确认</div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">客户信息</div>
                    <div class="section-content">
                        江南精工科技股份有限公司<br>
                        联系人：李经理 | 13900139000
                    </div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">产品信息</div>
                    <div class="section-content">
                        不锈钢板材 (STEEL002) × 50平方米<br>
                        304不锈钢板材，厚度3mm
                    </div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">包装信息</div>
                    <div class="section-content">标准包装 | 预计2-3个工作日</div>
                </div>
                
                <div class="order-meta">
                    <span class="order-date">2024-01-14 14:20</span>
                    <span>￥4,275.00</span>
                </div>
            </div>
        </div>
        
        <!-- 底部导航 -->
        <div class="bottom-nav">
            <a href="#" class="nav-item">
                <div class="nav-icon"></div>
                <span>首页</span>
            </a>
            <a href="#" class="nav-item active">
                <div class="nav-icon"></div>
                <span>订单</span>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon"></div>
                <span>工单</span>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon"></div>
                <span>包装</span>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon"></div>
                <span>出入库</span>
            </a>
        </div>
    </div>
</body>
</html>
