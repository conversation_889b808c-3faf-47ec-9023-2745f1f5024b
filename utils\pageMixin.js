/**
 * 页面基类Mixin
 * 提供通用的页面逻辑，减少重复代码
 */

const { errorHandler, handleError } = require('./errorHandler')
const utils = require('./index')

/**
 * 页面基类Mixin
 * 包含通用的页面生命周期处理、加载状态管理、错误处理等
 */
const PageMixin = {
  data: {
    // 通用状态
    loading: false,
    refreshing: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    
    // 错误状态
    error: null,
    retryCount: 0,
    maxRetries: 3,
    
    // UI状态
    showEmpty: false,
    showError: false
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    console.log(`页面加载: ${this.route}`, options)
    this.pageOptions = options || {}
    
    // 调用子类的初始化方法
    if (typeof this.initPage === 'function') {
      this.initPage(options)
    }
    
    // 自动加载数据
    if (this.autoLoad !== false) {
      this.loadPageData()
    }
  },

  /**
   * 页面显示
   */
  onShow() {
    console.log(`页面显示: ${this.route}`)
    
    // 调用子类的显示方法
    if (typeof this.onPageShow === 'function') {
      this.onPageShow()
    }
    
    // 自动刷新数据
    if (this.autoRefresh === true) {
      this.refreshData()
    }
  },

  /**
   * 页面隐藏
   */
  onHide() {
    console.log(`页面隐藏: ${this.route}`)
    
    // 调用子类的隐藏方法
    if (typeof this.onPageHide === 'function') {
      this.onPageHide()
    }
  },

  /**
   * 页面卸载
   */
  onUnload() {
    console.log(`页面卸载: ${this.route}`)
    
    // 清理定时器
    this.clearTimers()
    
    // 调用子类的卸载方法
    if (typeof this.onPageUnload === 'function') {
      this.onPageUnload()
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    console.log(`下拉刷新: ${this.route}`)
    this.refreshData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    console.log(`上拉加载: ${this.route}`)
    if (this.data.hasMore && !this.data.loading) {
      this.loadMore()
    }
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    const shareInfo = this.getShareInfo ? this.getShareInfo() : {}
    
    return {
      title: shareInfo.title || '生产管理系统',
      path: shareInfo.path || this.route,
      imageUrl: shareInfo.imageUrl || ''
    }
  },

  /**
   * 加载页面数据
   */
  async loadPageData() {
    if (this.data.loading) return
    
    this.setData({ 
      loading: true, 
      error: null,
      showError: false 
    })
    
    try {
      // 调用子类的数据加载方法
      if (typeof this.loadData === 'function') {
        await this.loadData()
      }
      
      this.setData({ 
        showEmpty: this.checkEmpty(),
        retryCount: 0
      })
      
    } catch (error) {
      this.handleLoadError(error)
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    if (this.data.refreshing) return
    
    this.setData({ 
      refreshing: true,
      page: 1,
      hasMore: true,
      error: null,
      showError: false
    })
    
    try {
      // 调用子类的数据刷新方法
      if (typeof this.refreshData === 'function') {
        await this.refreshData()
      } else if (typeof this.loadData === 'function') {
        await this.loadData(true) // 传入刷新标志
      }
      
      this.setData({ 
        showEmpty: this.checkEmpty(),
        retryCount: 0
      })
      
    } catch (error) {
      this.handleLoadError(error)
    } finally {
      this.setData({ refreshing: false })
    }
  },

  /**
   * 加载更多数据
   */
  async loadMore() {
    if (this.data.loading || !this.data.hasMore) return
    
    this.setData({ loading: true })
    
    try {
      const nextPage = this.data.page + 1
      
      // 调用子类的加载更多方法
      if (typeof this.loadMoreData === 'function') {
        const hasMore = await this.loadMoreData(nextPage)
        this.setData({ 
          page: nextPage,
          hasMore: hasMore !== false
        })
      }
      
    } catch (error) {
      this.handleLoadError(error)
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 处理加载错误
   */
  handleLoadError(error) {
    console.error('页面数据加载失败:', error)
    
    const retryCount = this.data.retryCount + 1
    const canRetry = retryCount < this.data.maxRetries
    
    this.setData({
      error: error.message || '数据加载失败',
      showError: true,
      retryCount,
      showEmpty: false
    })
    
    // 显示错误提示
    if (canRetry) {
      utils.showError(`加载失败，还可重试${this.data.maxRetries - retryCount}次`)
    } else {
      utils.showError('加载失败，请检查网络连接')
    }
    
    // 记录错误日志
    errorHandler.log(error, `PAGE_LOAD_ERROR:${this.route}`)
  },

  /**
   * 重试加载
   */
  retryLoad() {
    if (this.data.retryCount >= this.data.maxRetries) {
      utils.showError('重试次数已达上限')
      return
    }
    
    this.loadPageData()
  },

  /**
   * 检查是否为空数据
   */
  checkEmpty() {
    // 子类可以重写此方法
    if (typeof this.isEmpty === 'function') {
      return this.isEmpty()
    }
    
    // 默认检查逻辑
    const dataKeys = ['list', 'data', 'items']
    for (const key of dataKeys) {
      const value = this.data[key]
      if (Array.isArray(value)) {
        return value.length === 0
      }
    }
    
    return false
  },

  /**
   * 显示加载状态
   */
  showLoading(title = '加载中...') {
    utils.showLoading(title)
  },

  /**
   * 隐藏加载状态
   */
  hideLoading() {
    utils.hideLoading()
  },

  /**
   * 显示成功提示
   */
  showSuccess(title = '操作成功') {
    utils.showSuccess(title)
  },

  /**
   * 显示错误提示
   */
  showError(title = '操作失败') {
    utils.showError(title)
  },

  /**
   * 显示确认对话框
   */
  async showConfirm(content, title = '提示') {
    return utils.showConfirm(content, title)
  },

  /**
   * 页面跳转
   */
  navigateTo(url, params = {}) {
    utils.navigateTo(url, params)
  },

  /**
   * 页面重定向
   */
  redirectTo(url, params = {}) {
    utils.redirectTo(url, params)
  },

  /**
   * 返回上一页
   */
  navigateBack(delta = 1) {
    utils.navigateBack(delta)
  },

  /**
   * 切换TabBar页面
   */
  switchTab(url) {
    utils.switchTab(url)
  },

  /**
   * 设置页面标题
   */
  setTitle(title) {
    wx.setNavigationBarTitle({ title })
  },

  /**
   * 防抖处理
   */
  debounce(func, wait = 300) {
    return utils.debounce(func, wait)
  },

  /**
   * 节流处理
   */
  throttle(func, limit = 300) {
    return utils.throttle(func, limit)
  },

  /**
   * 清理定时器
   */
  clearTimers() {
    if (this.timers) {
      this.timers.forEach(timer => clearTimeout(timer))
      this.timers = []
    }
  },

  /**
   * 添加定时器
   */
  addTimer(callback, delay) {
    if (!this.timers) {
      this.timers = []
    }
    
    const timer = setTimeout(callback, delay)
    this.timers.push(timer)
    return timer
  },

  /**
   * 安全的setData
   */
  safeSetData(data, callback) {
    try {
      this.setData(data, callback)
    } catch (error) {
      console.error('setData失败:', error)
      errorHandler.log(error, 'SET_DATA_ERROR')
    }
  },

  /**
   * 获取页面参数
   */
  getPageOptions() {
    return this.pageOptions || {}
  },

  /**
   * 获取页面路径
   */
  getPagePath() {
    return this.route || ''
  }
}

/**
 * 创建页面
 * @param {Object} pageConfig 页面配置
 * @returns {Object} 页面对象
 */
function createPage(pageConfig) {
  // 合并Mixin和页面配置
  const page = Object.assign({}, PageMixin, pageConfig)
  
  // 处理生命周期方法的合并
  const lifecycleMethods = ['onLoad', 'onShow', 'onHide', 'onUnload', 'onPullDownRefresh', 'onReachBottom']
  
  lifecycleMethods.forEach(method => {
    if (PageMixin[method] && pageConfig[method]) {
      const mixinMethod = PageMixin[method]
      const pageMethod = pageConfig[method]
      
      page[method] = function(...args) {
        // 先执行Mixin的方法
        mixinMethod.apply(this, args)
        // 再执行页面的方法
        pageMethod.apply(this, args)
      }
    }
  })
  
  return page
}

module.exports = {
  PageMixin,
  createPage
}
