<!--生产管理系统 - 任务管理页面-->
<view class="page-container">
  <!-- 页面头部操作区 - 简约现代风格 -->
  <view class="header-actions">
    <button class="btn btn-primary" bindtap="showFilterModal">
      高级筛选
    </button>
    <button class="btn btn-secondary" bindtap="refreshTaskList">
      刷新
    </button>
  </view>

  <!-- 搜索栏 -->
  <view class="search-container">
    <view class="search-box">
      <input
        class="search-input"
        value="{{ searchKeyword }}"
        placeholder="搜索工序名称、工单号、产品名称"
        bindinput="onSearchInput"
        bindconfirm="onSearchConfirm"
      />
      <text class="clear-btn" wx:if="{{ searchKeyword }}" bindtap="clearSearch">清除</text>
    </view>
  </view>

  <!-- 状态筛选 -->
  <view class="filter-container">
    <scroll-view class="filter-scroll" scroll-x>
      <view class="filter-tabs">
        <view
          class="filter-tab {{ activeStatus === item.value ? 'active' : '' }}"
          wx:for="{{ statusOptions }}"
          wx:key="value"
          bindtap="onStatusChange"
          data-status="{{ item.value }}"
        >
          {{ item.label }}
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 任务列表 -->
  <view class="task-list">
    <view
      class="task-card"
      wx:for="{{ taskList }}"
      wx:key="id"
      bindtap="viewTaskDetail"
      data-task="{{ item }}"
    >
      <!-- 任务头部 -->
      <view class="task-header">
        <text class="task-name">{{ item.processName }}</text>
        <view class="task-status {{ item.status }}">
          {{ item.statusText }}
        </view>
      </view>

      <!-- 工单信息 - 简约现代风格 -->
      <view class="workorder-section">
        <view class="section-title">
          <text class="title-text">工单信息</text>
        </view>
        <view class="workorder-info">
          <text class="workorder-no">{{ item.workOrderNo }}</text>
          <text class="product-name">产品：{{ item.productName }}</text>
          <text class="product-code" wx:if="{{ item.productCode }}">编码：{{ item.productCode }}</text>
        </view>
      </view>

      <!-- 进度信息 - 简约现代风格 -->
      <view class="progress-section">
        <view class="section-title">
          <text class="title-text">生产进度</text>
        </view>
        <view class="progress-info">
          <view class="progress-main">
            <view class="progress-bar">
              <view
                class="progress-fill {{ item.status }}"
                style="width: {{ item.progress }}%"
              ></view>
            </view>
            <text class="progress-text">{{ item.progressText }}</text>
          </view>
          <view class="progress-data">
            <text class="data-item">计划：{{ item.plannedQuantity }}kg</text>
            <text class="data-item qualified">良品：{{ item.qualifiedQuantity }}kg</text>
            <text class="data-item defective">不良：{{ item.defectiveQuantity }}kg</text>
          </view>
        </view>
      </view>

      <!-- 分配信息 - 简约现代风格 -->
      <view class="assignment-section">
        <view class="section-title">
          <text class="title-text">分配信息</text>
        </view>
        <view class="assignment-info">
          <text class="assignment-status {{ item.assignedTo ? 'assigned' : 'unassigned' }}">
            {{ item.assignedText }}
          </text>
          <text class="workstation" wx:if="{{ item.workstation }}">工位：{{ item.workstation }}</text>
        </view>
      </view>

      <!-- 任务底部 -->
      <view class="task-footer">
        <text class="task-date">{{ item.plannedStartTimeText }}</text>
      </view>

      <!-- 操作按钮 -->
      <view class="task-actions">
        <button
          class="action-btn secondary"
          bindtap="showAssignModal"
          data-task="{{ item }}"
          catchtap="true"
        >
          分配
        </button>
        <button
          class="action-btn primary"
          bindtap="claimTask"
          data-task="{{ item }}"
          catchtap="true"
          wx:if="{{ !item.assignedTo }}"
        >
          领取任务
        </button>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:if="{{ taskList.length === 0 && !loading }}" class="empty-state">
      <text class="empty-text">暂无任务数据</text>
      <text class="empty-hint">工单创建工序后会自动生成任务</text>
    </view>

    <!-- 加载状态 -->
    <view wx:if="{{ loading }}" class="loading-state">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 加载更多 -->
    <view wx:if="{{ hasMore && taskList.length > 0 }}" class="load-more">
      <text class="load-more-text">上拉加载更多</text>
    </view>
  </view>

  <!-- 高级筛选弹窗 -->
  <view wx:if="{{ showFilterModal }}" class="modal-overlay" bindtap="hideFilterModal">
    <view class="filter-modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">高级筛选</text>
        <button class="modal-close" bindtap="hideFilterModal">×</button>
      </view>

      <view class="filter-content">
        <view class="filter-section">
          <text class="filter-section-title">任务状态</text>
          <view class="filter-options">
            <view
              class="filter-option {{ activeStatus === item.value ? 'active' : '' }}"
              wx:for="{{ statusOptions }}"
              wx:key="value"
              bindtap="onFilterStatusChange"
              data-status="{{ item.value }}"
            >
              {{ item.label }}
            </view>
          </view>
        </view>

        <view class="filter-section">
          <text class="filter-section-title">分配员工</text>
          <view class="filter-options">
            <view
              class="filter-option {{ currentEmployee === item.value ? 'active' : '' }}"
              wx:for="{{ employeeOptions }}"
              wx:key="value"
              bindtap="onEmployeeChange"
              data-employee="{{ item.value }}"
            >
              {{ item.label }}
            </view>
          </view>
        </view>

        <view class="filter-actions">
          <button class="btn btn-secondary" bindtap="resetFilter">重置</button>
          <button class="btn btn-primary" bindtap="applyFilter">应用筛选</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 任务分配弹窗 -->
  <view wx:if="{{ showAssignModal }}" class="modal-overlay" bindtap="hideAssignModal">
    <view class="assign-modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">分配任务</text>
        <button class="modal-close" bindtap="hideAssignModal">×</button>
      </view>

      <view class="assign-content">
        <view class="task-info">
          <text class="task-name">{{ selectedTask.processName }}</text>
          <text class="task-order">{{ selectedTask.workOrderNo }}</text>
        </view>

        <view class="employee-select">
          <text class="select-label">选择员工</text>
          <picker
            range="{{ employeeOptions }}"
            range-key="label"
            value="{{ selectedEmployeeIndex }}"
            bindchange="onAssignEmployeeChange"
          >
            <view class="picker-display">
              {{ selectedEmployeeIndex > 0 ? employeeOptions[selectedEmployeeIndex].label : '请选择员工' }}
            </view>
          </picker>
        </view>

        <view class="modal-actions">
          <button class="cancel-btn" bindtap="hideAssignModal">取消</button>
          <button class="confirm-btn" bindtap="confirmAssignTask">确认分配</button>
        </view>
      </view>
    </view>
  </view>
</view>
