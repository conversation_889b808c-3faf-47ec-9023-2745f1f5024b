<!--首页 - 简约现代风格-->
<view class="modern-page">
  <!-- 页面头部 -->
  <view class="modern-navbar">
    <text class="modern-navbar-title">生产管理</text>
    <view class="modern-navbar-actions">
      <view class="modern-tag modern-tag-primary">v1.0</view>
    </view>
  </view>

  <view class="modern-container">
    <!-- 顶部统计卡片 - 简约现代风格 -->
    <view class="stats-container">
      <view class="modern-card stats-card" bindtap="navigateToOrders">
        <view class="stats-content">
          <view class="stats-number">{{ orderStats.total }}</view>
          <view class="stats-label">总订单</view>
          <view class="stats-trend">
            <text class="trend-icon">📈</text>
            <text class="trend-text">今日新增 {{ orderStats.todayNew }}</text>
          </view>
        </view>
      </view>

      <view class="modern-card stats-card" bindtap="navigateToWorkOrders">
        <view class="stats-content">
          <view class="stats-number">{{ workOrderStats.total }}</view>
          <view class="stats-label">工单数</view>
          <view class="stats-trend">
            <text class="trend-icon">⚡</text>
            <text class="trend-text">进行中 {{ workOrderStats.processing }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 快捷操作 - 简约现代风格 -->
    <view class="modern-section quick-actions">
      <view class="modern-section-title">
        <text class="modern-section-icon">⚡</text>
        <text class="modern-section-text">快捷操作</text>
      </view>
      <view class="action-grid">
        <view class="action-item modern-card" bindtap="createOrder">
          <view class="action-icon">✨</view>
          <text class="action-text">新建订单</text>
        </view>
        <view class="action-item modern-card" bindtap="createWorkOrder">
          <view class="action-icon">🏭</view>
          <text class="action-text">创建工单</text>
        </view>
        <view class="action-item modern-card" bindtap="inventoryIn">
          <view class="action-icon">📦</view>
          <text class="action-text">入库</text>
        </view>
        <view class="action-item modern-card" bindtap="inventoryOut">
          <view class="action-icon">📤</view>
          <text class="action-text">出库</text>
        </view>
        <view class="action-item modern-card" bindtap="checkInventory">
          <view class="action-icon">📊</view>
          <text class="action-text">库存查询</text>
        </view>
        <view class="action-item modern-card" bindtap="viewPackaging">
          <view class="action-icon">📋</view>
          <text class="action-text">包装管理</text>
        </view>
        <view class="action-item modern-card" bindtap="viewReports">
          <view class="action-icon">📈</view>
          <text class="action-text">数据报表</text>
        </view>
      </view>
    </view>

    <!-- 待处理事项 - 简约现代风格 -->
    <view class="modern-section pending-tasks">
      <view class="modern-section-title">
        <text class="modern-section-icon">📋</text>
        <text class="modern-section-text">待处理事项</text>
        <view class="modern-tag modern-tag-neutral task-count">{{ pendingTasks.length }}</view>
      </view>

      <view class="modern-list task-list" wx:if="{{ pendingTasks.length > 0 }}">
        <view
          class="modern-list-item task-item"
          wx:for="{{ pendingTasks }}"
          wx:key="id"
          bindtap="handleTask"
          data-task="{{ item }}"
        >
          <view class="task-icon {{ item.type }}">
            <text>{{ item.icon }}</text>
          </view>
          <view class="task-content">
            <view class="task-title">{{ item.title }}</view>
            <view class="task-desc">{{ item.description }}</view>
            <view class="task-time">{{ item.timeText }}</view>
          </view>
          <view class="task-arrow">›</view>
        </view>
      </view>

      <view class="modern-empty" wx:else>
        <text class="modern-empty-icon">✅</text>
        <view class="modern-empty-text">暂无待处理事项</view>
      </view>
    </view>

    <!-- 最近订单 - 简约现代风格 -->
    <view class="modern-section recent-orders">
      <view class="modern-section-title">
        <text class="modern-section-icon">📊</text>
        <text class="modern-section-text">最近订单</text>
        <button class="modern-btn modern-btn-ghost modern-btn-small view-all" bindtap="viewAllOrders">
          查看全部
        </button>
      </view>

      <view class="order-list" wx:if="{{ recentOrders.length > 0 }}">
        <view
          class="modern-card order-item"
          wx:for="{{ recentOrders }}"
          wx:key="id"
          bindtap="viewOrderDetail"
          data-order="{{ item }}"
        >
          <view class="modern-card-header">
            <text class="order-no">{{ item.orderNo }}</text>
            <view class="modern-tag order-status {{ item.status }}">
              {{ item.statusText }}
            </view>
          </view>
          <view class="modern-card-body">
            <view class="order-info">
              <text class="customer-name">🏢 {{ item.customerName }}</text>
              <text class="product-name">🔧 {{ item.productName }}</text>
            </view>
          </view>
          <view class="modern-card-footer">
            <text class="order-time">{{ item.timeText }}</text>
          </view>
        </view>
      </view>

      <view class="modern-empty" wx:else>
        <text class="modern-empty-icon">📋</text>
        <view class="modern-empty-text">暂无订单数据</view>
      </view>
    </view>

    <!-- 加载状态 - 简约现代风格 -->
    <view class="modern-loading" wx:if="{{ loading }}">
      <text class="modern-loading-text">加载中...</text>
    </view>
  </view>
</view>
