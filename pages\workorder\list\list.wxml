<!--工单列表页面-->
<view class="page-container">
  <!-- 工单列表 -->
  <view class="workorder-list">
    <view class="workorder-card" wx:for="{{ workOrderList }}" wx:key="id" bindtap="viewWorkOrderDetail" data-workorder="{{ item }}">

      <!-- 1. 顶部状态区 -->
      <view class="card-header">
        <!-- 左侧状态标签 -->
        <view class="status-tags">
          <view class="status-tag executing">执行中</view>
          <view class="status-tag overdue">逾期</view>
        </view>

        <!-- 中间产品标题 -->
        <view class="product-title">{{ item.productCode }} | {{ item.productName }} | {{ item.material }}</view>

        <!-- 右侧计划数框 -->
        <view class="plan-quantity-box">
          <text class="plan-label">计划数</text>
          <text class="plan-value">{{ item.plannedQuantity }}</text>
        </view>
      </view>

      <!-- 2. 工单信息区 -->
      <view class="workorder-info-section">
        <!-- 工单编号行 -->
        <view class="workorder-number-row">
          <text class="file-icon">📄</text>
          <text class="workorder-number">工单编号：{{ item.workOrderNo }}</text>
        </view>

        <!-- 数据统计区 -->
        <view class="data-stats-section">
          <view class="stats-columns">
            <view class="stat-item">
              <text class="stat-label">计划数</text>
              <text class="stat-value">{{ item.plannedQuantity }}</text>
            </view>
            <view class="stat-item">
              <text class="stat-label">完工数</text>
              <text class="stat-value">{{ item.completedQuantity }}</text>
            </view>
            <view class="stat-item">
              <text class="stat-label">待完成数</text>
              <text class="stat-value pending">{{ item.pendingQuantity }}</text>
            </view>
          </view>

          <!-- 环形进度图 -->
          <view class="circular-progress">
            <view class="progress-circle">
              <text class="progress-text">{{ item.progressPercent }}%</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 3. 进度时间区 -->
      <view class="progress-time-section">
        <!-- 计划时间 -->
        <view class="time-info">
          <view class="time-row">
            <text class="calendar-icon">📅</text>
            <text class="time-text">计划开始：{{ item.plannedStartTime }}</text>
          </view>
          <view class="time-row">
            <text class="calendar-icon">📅</text>
            <text class="time-text">计划结束：{{ item.plannedEndTime }}</text>
          </view>
        </view>

        <!-- 工单进度 -->
        <view class="progress-info">
          <view class="progress-header">
            <text class="progress-icon">📊</text>
            <text class="progress-title">工单进度（%）：</text>
          </view>
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{ item.progressPercent }}%"></view>
          </view>
        </view>

        <!-- 工单说明 -->
        <view class="description-info">
          <text class="text-icon">📝</text>
          <text class="description-text">工单说明：{{ item.description || '-' }}</text>
        </view>
      </view>

      <!-- 4. 步骤流程区 -->
      <view class="process-steps-section">
        <view class="step-item" wx:for="{{ item.processes }}" wx:key="id" wx:for-item="process">
          <view class="step-icon-container">
            <view class="step-icon {{ process.status }}">
              <text class="check-icon" wx:if="{{ process.status === 'completed' }}">✓</text>
              <view class="task-count" wx:if="{{ process.taskCount > 0 }}">{{ process.taskCount }}</view>
            </view>
          </view>
          <text class="step-name">{{ process.name }}</text>
          <text class="step-progress">{{ process.progress }}%</text>
        </view>
      </view>

      <!-- 5. 底部操作区 -->
      <view class="card-actions">
        <button class="quick-action-btn" catchtap="showQuickActionModal" data-workorder="{{ item }}">快速操作</button>
      </view>

    </view>
  </view>

  <!-- 快速操作弹窗 -->
  <view class="modal-overlay" wx:if="{{ showQuickActionModal }}" bindtap="hideQuickActionModal">
    <view class="quick-action-modal" catchtap="stopPropagation">

      <!-- 标题栏（顶部区域） -->
      <view class="modal-header">
        <text class="modal-title">快速操作</text>
        <button class="modal-close-btn" bindtap="hideQuickActionModal">×</button>
      </view>

      <!-- 内容区 -->
      <view class="modal-content">

        <!-- 3.1 优先级模块 -->
        <view class="form-section">
          <text class="section-label">优先级</text>
          <view class="button-group">
            <button
              class="option-btn {{ selectedPriority === 'urgent' ? 'selected' : '' }}"
              bindtap="selectPriority"
              data-priority="urgent"
            >
              加急
              <text class="check-icon" wx:if="{{ selectedPriority === 'urgent' }}">✓</text>
            </button>
            <button
              class="option-btn {{ selectedPriority === 'paused' ? 'selected' : '' }}"
              bindtap="selectPriority"
              data-priority="paused"
            >
              暂停
              <text class="check-icon" wx:if="{{ selectedPriority === 'paused' }}">✓</text>
            </button>
            <button
              class="option-btn {{ selectedPriority === 'none' ? 'selected' : '' }}"
              bindtap="selectPriority"
              data-priority="none"
            >
              无
              <text class="check-icon" wx:if="{{ selectedPriority === 'none' }}">✓</text>
            </button>
          </view>
        </view>

        <!-- 3.2 同步更新模块 -->
        <view class="form-section">
          <view class="checkbox-row">
            <view class="checkbox {{ syncToTasks ? 'checked' : '' }}" bindtap="toggleSyncToTasks">
              <text class="check-icon" wx:if="{{ syncToTasks }}">✓</text>
            </view>
            <text class="checkbox-label">同步更新至任务</text>
          </view>
        </view>

        <!-- 3.3 执行状态模块 -->
        <view class="form-section">
          <text class="section-label">执行状态</text>
          <view class="button-group">
            <button
              class="option-btn {{ selectedStatus === 'not_started' ? 'selected' : '' }}"
              bindtap="selectStatus"
              data-status="not_started"
            >
              未开始
              <text class="check-icon" wx:if="{{ selectedStatus === 'not_started' }}">✓</text>
            </button>
            <button
              class="option-btn {{ selectedStatus === 'executing' ? 'selected' : '' }}"
              bindtap="selectStatus"
              data-status="executing"
            >
              执行中
              <text class="check-icon" wx:if="{{ selectedStatus === 'executing' }}">✓</text>
            </button>
            <button
              class="option-btn {{ selectedStatus === 'finished' ? 'selected' : '' }}"
              bindtap="selectStatus"
              data-status="finished"
            >
              已结束
              <text class="check-icon" wx:if="{{ selectedStatus === 'finished' }}">✓</text>
            </button>
          </view>
        </view>

      </view>

      <!-- 底部操作区 -->
      <view class="modal-actions">
        <button class="action-btn confirm-btn" bindtap="confirmQuickAction">确定更改</button>
        <button class="action-btn cancel-btn" bindtap="hideQuickActionModal">取消</button>
      </view>

    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{ workOrderList.length === 0 }}">
    <view class="empty-icon">🏭</view>
    <text class="empty-text">暂无工单数据</text>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <button class="action-btn primary" bindtap="createWorkOrder">
      新建工单
    </button>
  </view>
</view>
