<!--工艺路线管理页面 - 简约现代风格-->
<view class="modern-page">
  <!-- 页面头部 -->
  <view class="modern-navbar">
    <view class="header-content">
      <text class="modern-navbar-title">工艺路线管理</text>
      <text class="page-subtitle">管理工艺路线配置</text>
    </view>
    <view class="modern-navbar-actions">
      <button class="modern-btn modern-btn-primary modern-btn-small" bindtap="showCreateModal">
        新建路线
      </button>
    </view>
  </view>

  <view class="modern-container">
    <!-- 搜索栏 -->
    <view class="search-container">
      <view class="modern-input-wrapper">
        <input
          class="modern-input"
          placeholder="搜索工艺路线名称或描述"
          value="{{ searchKeyword }}"
          bindinput="onSearchInput"
          bindconfirm="onSearchConfirm"
        />
        <view class="search-icon">🔍</view>
      </view>
    </view>

    <!-- 工艺路线列表 -->
    <view class="routes-section">
      <view class="modern-section">
        <view class="modern-section-title">
          <text class="modern-section-icon">🛤️</text>
          <text class="modern-section-text">工艺路线列表</text>
          <view class="route-count">{{ filteredRoutes.length }}</view>
        </view>

        <!-- 工艺路线卡片列表 -->
        <view class="route-list" wx:if="{{ filteredRoutes.length > 0 }}">
          <view
            class="modern-card route-card"
            wx:for="{{ filteredRoutes }}"
            wx:key="id"
            bindtap="viewRouteDetail"
            data-route="{{ item }}"
          >
            <view class="route-header">
              <view class="route-info">
                <text class="route-name">{{ item.name }}</text>
                <view class="route-meta">
                  <text class="route-id">ID: {{ item.id }}</text>
                  <view class="modern-tag modern-tag-primary">{{ item.category }}</view>
                </view>
              </view>
              <view class="route-actions">
                <button
                  class="action-btn edit-btn"
                  bindtap="editRoute"
                  data-route="{{ item }}"
                  catchtap="true"
                >
                  ✏️
                </button>
                <button
                  class="action-btn delete-btn"
                  bindtap="deleteRoute"
                  data-id="{{ item.id }}"
                  data-name="{{ item.name }}"
                  catchtap="true"
                >
                  🗑️
                </button>
              </view>
            </view>

            <view class="route-body">
              <text class="route-description">
                {{ item.description || '暂无描述' }}
              </text>
              
              <!-- 工序列表 -->
              <view class="process-list-preview" wx:if="{{ item.processes.length > 0 }}">
                <view class="process-preview-title">包含工序：</view>
                <view class="process-tags">
                  <view
                    class="process-tag"
                    wx:for="{{ item.processes }}"
                    wx:key="processId"
                    wx:for-item="process"
                  >
                    {{ process.processName || process.processId }}
                  </view>
                </view>
              </view>
            </view>

            <view class="route-footer">
              <view class="route-stats">
                <view class="stat-item">
                  <text class="stat-label">工序数：</text>
                  <text class="stat-value">{{ item.processes.length }}</text>
                </view>
                <view class="stat-item">
                  <text class="stat-label">总工时：</text>
                  <text class="stat-value">{{ item.totalHours }}小时</text>
                </view>
              </view>
              <text class="route-time">
                {{ item.createdAt }}
              </text>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view class="modern-empty" wx:if="{{ filteredRoutes.length === 0 && !loading }}">
          <text class="modern-empty-icon">🛤️</text>
          <view class="modern-empty-text">
            <view>{{ searchKeyword ? '未找到匹配的工艺路线' : '暂无工艺路线数据' }}</view>
            <view>{{ searchKeyword ? '请尝试其他关键词' : '点击"新建路线"开始添加' }}</view>
          </view>
        </view>

        <!-- 加载状态 -->
        <view class="modern-loading" wx:if="{{ loading }}">
          <text class="modern-loading-text">加载中...</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 创建/编辑工艺路线弹窗 -->
  <view class="modal-overlay" wx:if="{{ showModal }}" bindtap="hideModal">
    <view class="modal-container large-modal" catchtap="true">
      <view class="modal-header">
        <text class="modal-title">{{ isEditing ? '编辑工艺路线' : '新建工艺路线' }}</text>
        <button class="modal-close" bindtap="hideModal">✕</button>
      </view>

      <view class="modal-body">
        <view class="form-group">
          <text class="form-label">路线名称 *</text>
          <input
            class="modern-input"
            placeholder="请输入工艺路线名称"
            value="{{ formData.name }}"
            bindinput="onNameInput"
            maxlength="100"
          />
        </view>

        <view class="form-group">
          <text class="form-label">路线分类</text>
          <input
            class="modern-input"
            placeholder="请输入路线分类"
            value="{{ formData.category }}"
            bindinput="onCategoryInput"
            maxlength="20"
          />
        </view>

        <view class="form-group">
          <text class="form-label">路线描述</text>
          <textarea
            class="modern-textarea"
            placeholder="请输入工艺路线描述（可选）"
            value="{{ formData.description }}"
            bindinput="onDescriptionInput"
            maxlength="500"
            auto-height
          />
        </view>

        <view class="form-group">
          <view class="form-label-with-action">
            <text class="form-label">工序配置 *</text>
            <button class="add-process-btn" bindtap="showProcessSelector">
              添加工序
            </button>
          </view>
          
          <!-- 已选工序列表 -->
          <view class="selected-processes" wx:if="{{ formData.processes.length > 0 }}">
            <view
              class="process-item"
              wx:for="{{ formData.processes }}"
              wx:key="processId"
              wx:for-item="process"
            >
              <view class="process-order">{{ index + 1 }}</view>
              <view class="process-info">
                <text class="process-name">{{ process.processName }}</text>
                <text class="process-hours">{{ process.estimatedHours }}小时</text>
              </view>
              <view class="process-controls">
                <button
                  class="control-btn"
                  bindtap="moveProcessUp"
                  data-index="{{ index }}"
                  disabled="{{ index === 0 }}"
                >
                  ↑
                </button>
                <button
                  class="control-btn"
                  bindtap="moveProcessDown"
                  data-index="{{ index }}"
                  disabled="{{ index === formData.processes.length - 1 }}"
                >
                  ↓
                </button>
                <button
                  class="control-btn remove-btn"
                  bindtap="removeProcess"
                  data-index="{{ index }}"
                >
                  ✕
                </button>
              </view>
            </view>
          </view>

          <!-- 空状态 -->
          <view class="empty-processes" wx:if="{{ formData.processes.length === 0 }}">
            <text class="empty-text">暂未添加工序，请点击"添加工序"按钮</text>
          </view>

          <!-- 总工时显示 -->
          <view class="total-hours" wx:if="{{ formData.processes.length > 0 }}">
            <text class="total-label">总预计工时：</text>
            <text class="total-value">{{ totalHours }}小时</text>
          </view>
        </view>
      </view>

      <view class="modal-footer">
        <button class="modern-btn modern-btn-secondary" bindtap="hideModal">
          取消
        </button>
        <button
          class="modern-btn modern-btn-primary"
          bindtap="saveRoute"
          disabled="{{ !isFormValid || saving }}"
        >
          {{ saving ? '保存中...' : '保存' }}
        </button>
      </view>
    </view>
  </view>

  <!-- 工序选择弹窗 -->
  <view class="modal-overlay" wx:if="{{ showProcessModal }}" bindtap="hideProcessModal">
    <view class="modal-container" catchtap="true">
      <view class="modal-header">
        <text class="modal-title">选择工序</text>
        <button class="modal-close" bindtap="hideProcessModal">✕</button>
      </view>

      <view class="modal-body">
        <!-- 工序搜索 -->
        <view class="process-search">
          <input
            class="modern-input"
            placeholder="搜索工序名称"
            value="{{ processSearchKeyword }}"
            bindinput="onProcessSearchInput"
          />
        </view>

        <!-- 可选工序列表 -->
        <view class="available-processes">
          <view
            class="available-process-item"
            wx:for="{{ availableProcesses }}"
            wx:key="id"
            bindtap="selectProcess"
            data-process="{{ item }}"
          >
            <view class="process-info">
              <text class="process-name">{{ item.name }}</text>
              <text class="process-category">{{ item.category }}</text>
            </view>
            <text class="process-hours">{{ item.estimatedHours }}小时</text>
          </view>
        </view>

        <!-- 空状态 -->
        <view class="modern-empty" wx:if="{{ availableProcesses.length === 0 }}">
          <text class="modern-empty-icon">⚙️</text>
          <view class="modern-empty-text">
            <view>暂无可选工序</view>
            <view>请先在工序管理中添加工序</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 删除确认弹窗 -->
  <view class="modal-overlay" wx:if="{{ showDeleteModal }}" bindtap="hideDeleteModal">
    <view class="modal-container delete-modal" catchtap="true">
      <view class="modal-header">
        <text class="modal-title">确认删除</text>
        <button class="modal-close" bindtap="hideDeleteModal">✕</button>
      </view>

      <view class="modal-body">
        <view class="delete-warning">
          <text class="warning-icon">⚠️</text>
          <view class="warning-text">
            <view>确定要删除工艺路线"{{ deleteTarget.name }}"吗？</view>
            <view class="warning-note">此操作不可撤销</view>
          </view>
        </view>
      </view>

      <view class="modal-footer">
        <button class="modern-btn modern-btn-secondary" bindtap="hideDeleteModal">
          取消
        </button>
        <button
          class="modern-btn delete-confirm-btn"
          bindtap="confirmDelete"
          disabled="{{ deleting }}"
        >
          {{ deleting ? '删除中...' : '确认删除' }}
        </button>
      </view>
    </view>
  </view>
</view>
