/* 工单详情页面样式 - 严格按照设计规范 */
.page-container {
  background-color: #F8F8F8; /* 页面背景色 */
  min-height: 100vh;
  padding: 32rpx; /* 16px */
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom, 20rpx)); /* 为底部操作区留出空间 */
  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', sans-serif;
  box-sizing: border-box;
}

/* 模块1：工单头部信息区（可折叠） */
.header-info-section {
  background-color: #FFFFFF; /* 卡片内背景 */
  border-radius: 16rpx; /* 8px */
  padding: 24rpx; /* 12px */
  margin-bottom: 32rpx; /* 16px模块间距 */
  border: 2rpx solid #ECECEC; /* 边框 */
}

.header-content {
  margin-bottom: 24rpx;
}

.header-row {
  margin-bottom: 16rpx; /* 行间距 */
}

.header-row:last-child {
  margin-bottom: 0;
}

/* 第一行：状态标签 + 工单编号 */
.header-row:first-child {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.status-tags {
  display: flex;
  gap: 8rpx; /* 4px间距 */
}

.status-tag {
  font-size: 24rpx; /* 12px */
  color: #FFFFFF;
  padding: 8rpx 16rpx;
  border-radius: 8rpx; /* 4px圆角 */
}

.status-tag.executing {
  background-color: #409EFF; /* 蓝色 */
}

.status-tag.overdue {
  background-color: #F56C6C; /* 红色 */
}

.status-tag.completed {
  background-color: #67C23A; /* 绿色 */
}

.workorder-no {
  font-size: 32rpx; /* 16px */
  font-weight: 700; /* 加粗 */
  color: #333333; /* 黑色 */
  margin-left: 16rpx; /* 8px右距 */
}

/* 第二行：计划时间 */
.time-info {
  display: flex;
  align-items: center;
  gap: 8rpx; /* 4px间距 */
}

.calendar-icon {
  font-size: 24rpx;
  color: #999999; /* 灰色图标 */
}

.time-text {
  font-size: 24rpx; /* 12px */
  color: #999999; /* 灰色 */
}

/* 第三行：产品信息 */
.product-info {
  display: flex;
  align-items: center;
  gap: 8rpx; /* 4px间距 */
  cursor: pointer;
}

.product-icon {
  font-size: 24rpx;
  color: #999999; /* 灰色图标 */
}

.product-text {
  font-size: 28rpx; /* 14px */
  color: #409EFF; /* 蓝色可点击 */
}

/* 第四行：工单进度 */
.progress-info {
  display: flex;
  align-items: center;
  gap: 8rpx; /* 4px间距 */
}

.progress-icon {
  font-size: 24rpx;
  color: #999999; /* 灰色图标 */
}

.progress-text {
  font-size: 28rpx; /* 14px */
  color: #333333; /* 黑色 */
}

.progress-bar {
  width: 360rpx; /* 180px */
  height: 8rpx; /* 4px */
  background-color: #ECECEC; /* 灰色背景 */
  border-radius: 8rpx; /* 4px圆角 */
  margin-left: 16rpx; /* 8px右距 */
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #67C23A; /* 绿色填充 */
  transition: width 0.3s ease;
}

/* 折叠箭头 */
.collapse-toggle {
  text-align: center;
  padding-top: 16rpx;
  border-top: 1rpx solid #ECECEC;
}

.collapse-icon {
  font-size: 24rpx;
  color: #999999; /* 灰色箭头 */
  transition: transform 0.3s ease;
}

.collapse-icon.collapsed {
  transform: rotate(180deg);
}

/* 模块2：标签切换栏 */
.tab-switch-section {
  margin-bottom: 32rpx; /* 16px模块间距 */
}

.tab-container {
  display: flex;
  justify-content: center;
  gap: 16rpx; /* 8px间距 */
}

.tab-item {
  font-size: 28rpx; /* 14px */
  padding: 16rpx 32rpx; /* 8px 16px内边距 */
  border-radius: 16rpx; /* 8px圆角 */
  transition: all 0.3s ease;
}

.tab-item.active {
  background-color: #67C23A; /* 绿色背景 */
  color: #FFFFFF; /* 白字 */
}

.tab-item:not(.active) {
  background-color: #ECECEC; /* 灰色背景 */
  color: #333333; /* 黑字 */
}

/* 模块3：工序卡片列表 */
.process-cards-section {
  margin-bottom: 32rpx; /* 16px模块间距 */
}

.process-group {
  margin-bottom: 32rpx;
}

.process-card {
  background-color: #FFFFFF; /* 白背景 */
  border-radius: 16rpx; /* 8px圆角 */
  border: 2rpx solid #ECECEC; /* 1px边框 */
  padding: 24rpx; /* 12px内边距 */
  margin-bottom: 32rpx; /* 16px卡片间距 */
}

/* 卡片顶部行 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.left-section {
  display: flex;
  align-items: center;
  gap: 8rpx; /* 4px间距 */
}

.process-number {
  width: 32rpx; /* 16px直径 */
  height: 32rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: #FFFFFF;
  font-weight: bold;
}

.process-number.completed {
  background-color: #67C23A; /* 绿色 */
}

.process-number.active {
  background-color: #409EFF; /* 蓝色 */
}

.process-name {
  font-size: 28rpx; /* 14px */
  color: #333333; /* 黑色 */
  margin-left: 8rpx;
}

.plan-quantity-tag {
  background-color: #ECECEC; /* 灰色圆角框 */
  color: #409EFF; /* 蓝色字 */
  font-size: 24rpx; /* 12px */
  padding: 8rpx 16rpx; /* 4px 8px内边距 */
  border-radius: 8rpx;
}

/* 卡片中部行 */
.card-middle {
  margin-bottom: 16rpx;
}

.process-code {
  display: flex;
  align-items: center;
  gap: 8rpx; /* 4px间距 */
  margin-bottom: 12rpx;
}

.file-icon {
  font-size: 24rpx;
  color: #999999; /* 灰色图标 */
}

.code-text {
  font-size: 24rpx; /* 12px */
  color: #999999; /* 灰色 */
}

.quantity-data {
  display: flex;
  gap: 32rpx; /* 16px间距 */
}

.data-item {
  font-size: 28rpx; /* 14px */
  color: #333333; /* 黑色 */
}

.data-item.qualified {
  color: #67C23A; /* 绿色 */
}

.data-item.defective {
  color: #F56C6C; /* 红色 */
}

/* 卡片底部行 */
.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16rpx;
}

.progress-section {
  display: flex;
  align-items: center;
  gap: 16rpx; /* 8px间距 */
  flex: 1;
}

.progress-bar {
  width: 200rpx;
  height: 8rpx; /* 4px */
  background-color: #ECECEC; /* 灰色背景 */
  border-radius: 8rpx; /* 4px圆角 */
  overflow: hidden;
}

.progress-bar.completed .progress-fill {
  background-color: #67C23A; /* 绿色填充 */
}

.progress-bar:not(.completed) .progress-fill {
  background-color: #ECECEC; /* 灰色填充0% */
}

.progress-percent {
  font-size: 24rpx; /* 12px */
  color: #333333; /* 黑色 */
}

.permission-info {
  display: flex;
  align-items: center;
  gap: 8rpx; /* 4px间距 */
}

.permission-icon {
  font-size: 24rpx;
  color: #999999; /* 灰色图标 */
}

.permission-text {
  font-size: 24rpx; /* 12px */
  color: #999999; /* 灰色 */
}

.action-buttons {
  display: flex;
  gap: 16rpx; /* 8px间距 */
}

.action-btn {
  font-size: 24rpx; /* 12px */
  padding: 16rpx 32rpx; /* 8px 16px内边距 */
  border-radius: 8rpx; /* 4px圆角 */
  border: 2rpx solid #DCDCDC; /* 灰色边框 */
  background-color: #FFFFFF; /* 白背景 */
  color: #333333; /* 黑字 */
}

.action-btn::after {
  border: none;
}

.action-btn:active {
  background-color: #F8F8F8;
}

/* 用料明细占位内容 */
.materials-section {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  margin-bottom: 32rpx;
}

.placeholder-content {
  color: #999999;
}

.placeholder-text {
  font-size: 28rpx;
}

/* 模块4：底部操作区 - 使用独特类名避免冲突 */
.workorder-detail-bottom-actions {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  background-color: #FFFFFF !important;
  padding: 20rpx 24rpx !important;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom, 20rpx)) !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  box-shadow: 0 -2rpx 16rpx rgba(0, 0, 0, 0.1) !important;
  z-index: 9999 !important;
  border-top: 1rpx solid #F0F0F0 !important;
  box-sizing: border-box !important;
}

.workorder-detail-left-actions {
  flex: 1;
  margin-right: 16rpx;
  max-width: 120rpx;
}

.workorder-detail-more-btn {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 4rpx !important;
  font-size: 22rpx !important;
  color: #666666 !important;
  padding: 12rpx 16rpx !important;
  background-color: #F8F8F8 !important;
  border: 1rpx solid #E0E0E0 !important;
  border-radius: 10rpx !important;
  width: 100% !important;
  max-width: 120rpx !important;
  height: 56rpx !important;
  box-sizing: border-box !important;
}

.workorder-detail-more-btn::after {
  border: none !important;
}

.workorder-detail-more-icon {
  font-size: 16rpx !important;
  color: #666666 !important;
}

.workorder-detail-more-text {
  font-size: 24rpx !important;
  color: #666666 !important;
}

.workorder-detail-right-actions {
  display: flex !important;
  gap: 12rpx !important;
  flex-shrink: 0 !important;
}

.workorder-detail-priority-btn {
  font-size: 22rpx !important;
  padding: 12rpx 16rpx !important;
  border-radius: 10rpx !important;
  background-color: #F0F0F0 !important;
  color: #333333 !important;
  border: none !important;
  width: 100rpx !important;
  height: 56rpx !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-sizing: border-box !important;
}

.workorder-detail-priority-btn::after {
  border: none !important;
}

.workorder-detail-priority-btn:active {
  background-color: #E8E8E8 !important;
}

.workorder-detail-finish-btn {
  font-size: 22rpx !important;
  padding: 12rpx 16rpx !important;
  border-radius: 10rpx !important;
  background-color: #FF4757 !important;
  color: #FFFFFF !important;
  border: none !important;
  width: 80rpx !important;
  height: 56rpx !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-sizing: border-box !important;
}

.workorder-detail-finish-btn::after {
  border: none !important;
}

.workorder-detail-finish-btn:active {
  background-color: #E84057 !important;
}

/* 小屏幕适配 - 进一步优化 */
@media (max-width: 450px) {
  .workorder-detail-bottom-actions {
    padding: 16rpx 20rpx !important;
    padding-bottom: calc(16rpx + env(safe-area-inset-bottom, 16rpx)) !important;
  }

  .workorder-detail-left-actions {
    margin-right: 12rpx !important;
    max-width: 100rpx !important;
  }

  .workorder-detail-more-btn {
    width: 100rpx !important;
    height: 48rpx !important;
    font-size: 20rpx !important;
    padding: 8rpx 12rpx !important;
  }

  .workorder-detail-priority-btn {
    width: 80rpx !important;
    height: 48rpx !important;
    font-size: 20rpx !important;
    padding: 8rpx 12rpx !important;
  }

  .workorder-detail-finish-btn {
    width: 60rpx !important;
    height: 48rpx !important;
    font-size: 20rpx !important;
    padding: 8rpx 12rpx !important;
  }

  .workorder-detail-right-actions {
    gap: 8rpx !important;
  }
}

/* 超小屏幕适配 */
@media (max-width: 375px) {
  .workorder-detail-bottom-actions {
    padding: 12rpx 16rpx !important;
    padding-bottom: calc(12rpx + env(safe-area-inset-bottom, 12rpx)) !important;
  }

  .workorder-detail-left-actions {
    margin-right: 8rpx !important;
    max-width: 80rpx !important;
  }

  .workorder-detail-more-btn {
    width: 80rpx !important;
    height: 44rpx !important;
    font-size: 18rpx !important;
    gap: 2rpx !important;
  }

  .workorder-detail-priority-btn {
    width: 70rpx !important;
    height: 44rpx !important;
    font-size: 18rpx !important;
  }

  .workorder-detail-finish-btn {
    width: 50rpx !important;
    height: 44rpx !important;
    font-size: 18rpx !important;
  }

  .workorder-detail-right-actions {
    gap: 6rpx !important;
  }
}
