/* 设置页面样式 - 简约现代风格 */
@import '../../../styles/modern-simple.wxss';

/* 页面头部增强 */
.header-content {
  flex: 1;
}

.page-subtitle {
  font-size: 24rpx;
  color: #8E8E93;
  margin-top: 8rpx;
  display: block;
}

/* 列表项样式增强 */
.modern-list-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 12rpx;
  transition: all 0.2s ease;
  border: 1rpx solid #F2F2F7;
}

.modern-list-item:active {
  transform: translateY(2rpx) scale(0.99);
  background-color: #F8F9FA;
}

.modern-list-item:last-child {
  margin-bottom: 0;
}

/* 移除图标样式 */

.list-item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.list-item-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #1D1D1F;
  margin-bottom: 8rpx;
}

.list-item-desc {
  font-size: 24rpx;
  color: #8E8E93;
  line-height: 1.4;
}

.list-item-arrow {
  margin-left: 16rpx;
  flex-shrink: 0;
}

.arrow-icon {
  font-size: 28rpx;
  color: #C7C7CC;
  font-weight: 500;
}

/* 操作按钮区域 */
.action-section {
  margin-top: 40rpx;
  padding: 32rpx 0;
  display: flex;
  gap: 16rpx;
}

.action-section .modern-btn {
  flex: 1;
  height: 88rpx;
  font-size: 28rpx;
  border-radius: 16rpx;
}

/* 系统信息特殊样式 */
.modern-section:last-of-type .modern-list-item {
  cursor: default;
}

.modern-section:last-of-type .modern-list-item:active {
  transform: none;
  background-color: #FFFFFF;
}

.modern-section:last-of-type .list-item-arrow {
  display: none;
}

/* 分组间距 */
.modern-section {
  margin-bottom: 40rpx;
}

.modern-section:last-of-type {
  margin-bottom: 20rpx;
}

/* 响应式调整 */
@media (max-width: 400px) {
  /* 移除响应式图标样式 */
  
  .list-item-title {
    font-size: 30rpx;
  }
  
  .list-item-desc {
    font-size: 22rpx;
  }
  
  .action-section {
    flex-direction: column;
  }
  
  .action-section .modern-btn {
    flex: none;
  }
}
