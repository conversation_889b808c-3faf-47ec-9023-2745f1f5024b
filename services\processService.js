/**
 * 工序服务 - 管理工序数据
 */

class ProcessService {
  constructor() {
    this.storageKey = 'erp_processes'
    this.initDefaultProcesses()
  }

  /**
   * 初始化默认工序数据
   */
  initDefaultProcesses() {
    const existingProcesses = this.getProcessesFromStorage()
    if (!existingProcesses || existingProcesses.length === 0) {
      const defaultProcesses = [
        {
          id: 'proc_001',
          name: '压铸',
          description: '其他表面有花纹,表面有细小凸点,表面有推杆印痕,表面有裂纹,局部凸起,局部凹陷,内部有气孔,产生结构松散,内部有缺陷,充填不满,内有气孔',
          estimatedHours: 2.0,
          category: '成型',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'proc_002',
          name: '去毛刺',
          description: '其他,飞边,磨伤',
          estimatedHours: 0.5,
          category: '后处理',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'proc_003',
          name: '车',
          description: '尺寸偏差,表面粗糙,表面伤痕',
          estimatedHours: 1.5,
          category: '机加工',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'proc_004',
          name: '钻',
          description: '孔径偏大、适当太孔径小,孔不圆,孔位置差,孔倾斜,孔表面粗糙',
          estimatedHours: 1.0,
          category: '机加工',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'proc_005',
          name: '铣',
          description: '表面粗糙,表面伤痕',
          estimatedHours: 2.5,
          category: '机加工',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'proc_006',
          name: '氧化',
          description: '',
          estimatedHours: 4.0,
          category: '表面处理',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ]
      this.saveProcessesToStorage(defaultProcesses)
    }
  }

  /**
   * 从本地存储获取工序数据
   */
  getProcessesFromStorage() {
    try {
      const data = wx.getStorageSync(this.storageKey)
      return data ? JSON.parse(data) : []
    } catch (error) {
      console.error('获取工序数据失败:', error)
      return []
    }
  }

  /**
   * 保存工序数据到本地存储
   */
  saveProcessesToStorage(processes) {
    try {
      wx.setStorageSync(this.storageKey, JSON.stringify(processes))
      return true
    } catch (error) {
      console.error('保存工序数据失败:', error)
      return false
    }
  }

  /**
   * 获取所有工序列表
   */
  async getProcessList() {
    try {
      const processes = this.getProcessesFromStorage()
      return {
        success: true,
        data: processes.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      }
    } catch (error) {
      console.error('获取工序列表失败:', error)
      return {
        success: false,
        message: '获取工序列表失败',
        data: []
      }
    }
  }

  /**
   * 根据ID获取工序详情
   */
  async getProcessById(id) {
    try {
      const processes = this.getProcessesFromStorage()
      const process = processes.find(item => item.id === id)
      
      if (process) {
        return {
          success: true,
          data: process
        }
      } else {
        return {
          success: false,
          message: '工序不存在'
        }
      }
    } catch (error) {
      console.error('获取工序详情失败:', error)
      return {
        success: false,
        message: '获取工序详情失败'
      }
    }
  }

  /**
   * 创建新工序
   */
  async createProcess(processData) {
    try {
      // 验证必填字段
      if (!processData.name || !processData.name.trim()) {
        return {
          success: false,
          message: '工序名称不能为空'
        }
      }

      if (!processData.estimatedHours || processData.estimatedHours <= 0) {
        return {
          success: false,
          message: '预计工时必须大于0'
        }
      }

      const processes = this.getProcessesFromStorage()
      
      // 检查工序名称是否已存在
      const existingProcess = processes.find(item => 
        item.name.trim().toLowerCase() === processData.name.trim().toLowerCase()
      )
      
      if (existingProcess) {
        return {
          success: false,
          message: '工序名称已存在'
        }
      }

      // 创建新工序
      const newProcess = {
        id: 'proc_' + Date.now(),
        name: processData.name.trim(),
        description: processData.description ? processData.description.trim() : '',
        estimatedHours: parseFloat(processData.estimatedHours),
        category: processData.category ? processData.category.trim() : '其他',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      processes.push(newProcess)
      
      if (this.saveProcessesToStorage(processes)) {
        return {
          success: true,
          message: '工序创建成功',
          data: newProcess
        }
      } else {
        return {
          success: false,
          message: '保存工序失败'
        }
      }
    } catch (error) {
      console.error('创建工序失败:', error)
      return {
        success: false,
        message: '创建工序失败'
      }
    }
  }

  /**
   * 更新工序信息
   */
  async updateProcess(id, processData) {
    try {
      // 验证必填字段
      if (!processData.name || !processData.name.trim()) {
        return {
          success: false,
          message: '工序名称不能为空'
        }
      }

      if (!processData.estimatedHours || processData.estimatedHours <= 0) {
        return {
          success: false,
          message: '预计工时必须大于0'
        }
      }

      const processes = this.getProcessesFromStorage()
      const processIndex = processes.findIndex(item => item.id === id)
      
      if (processIndex === -1) {
        return {
          success: false,
          message: '工序不存在'
        }
      }

      // 检查工序名称是否与其他工序重复
      const existingProcess = processes.find(item => 
        item.id !== id && 
        item.name.trim().toLowerCase() === processData.name.trim().toLowerCase()
      )
      
      if (existingProcess) {
        return {
          success: false,
          message: '工序名称已存在'
        }
      }

      // 更新工序信息
      processes[processIndex] = {
        ...processes[processIndex],
        name: processData.name.trim(),
        description: processData.description ? processData.description.trim() : '',
        estimatedHours: parseFloat(processData.estimatedHours),
        category: processData.category ? processData.category.trim() : '其他',
        updatedAt: new Date().toISOString()
      }

      if (this.saveProcessesToStorage(processes)) {
        return {
          success: true,
          message: '工序更新成功',
          data: processes[processIndex]
        }
      } else {
        return {
          success: false,
          message: '保存工序失败'
        }
      }
    } catch (error) {
      console.error('更新工序失败:', error)
      return {
        success: false,
        message: '更新工序失败'
      }
    }
  }

  /**
   * 删除工序
   */
  async deleteProcess(id) {
    try {
      const processes = this.getProcessesFromStorage()
      const processIndex = processes.findIndex(item => item.id === id)
      
      if (processIndex === -1) {
        return {
          success: false,
          message: '工序不存在'
        }
      }

      processes.splice(processIndex, 1)
      
      if (this.saveProcessesToStorage(processes)) {
        return {
          success: true,
          message: '工序删除成功'
        }
      } else {
        return {
          success: false,
          message: '删除工序失败'
        }
      }
    } catch (error) {
      console.error('删除工序失败:', error)
      return {
        success: false,
        message: '删除工序失败'
      }
    }
  }

  /**
   * 搜索工序
   */
  async searchProcesses(keyword) {
    try {
      const processes = this.getProcessesFromStorage()
      
      if (!keyword || !keyword.trim()) {
        return this.getProcessList()
      }

      const filteredProcesses = processes.filter(process =>
        process.name.toLowerCase().includes(keyword.toLowerCase()) ||
        process.description.toLowerCase().includes(keyword.toLowerCase()) ||
        process.category.toLowerCase().includes(keyword.toLowerCase())
      )

      return {
        success: true,
        data: filteredProcesses.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      }
    } catch (error) {
      console.error('搜索工序失败:', error)
      return {
        success: false,
        message: '搜索工序失败',
        data: []
      }
    }
  }

  /**
   * 获取工序分类列表
   */
  async getProcessCategories() {
    try {
      const processes = this.getProcessesFromStorage()
      const categories = [...new Set(processes.map(p => p.category))]
      
      return {
        success: true,
        data: categories.sort()
      }
    } catch (error) {
      console.error('获取工序分类失败:', error)
      return {
        success: false,
        message: '获取工序分类失败',
        data: []
      }
    }
  }
}

// 导出单例实例
module.exports = new ProcessService()
