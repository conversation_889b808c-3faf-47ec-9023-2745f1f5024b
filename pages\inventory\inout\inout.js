// 库存出入库页面逻辑
const { formatDate } = require('../../../utils/dateUtils')

Page({
  data: {
    operationType: 'in', // 'in' | 'out'
    
    // 表单数据
    formData: {
      productName: '',
      productCode: '',
      quantity: 1,
      unit: '件',
      reason: '',
      reasonText: '',
      supplier: '',
      departmentName: '',
      recipient: '',
      operationDate: '',
      notes: ''
    },
    
    // 选择器数据
    productOptions: [
      { id: 'prod_001', name: '产品A', code: 'PA001', unit: '件', stock: 100 },
      { id: 'prod_002', name: '产品B', code: 'PB002', unit: '套', stock: 50 },
      { id: 'prod_003', name: '产品C', code: 'PC003', unit: '个', stock: 200 },
      { id: 'prod_004', name: '原材料A', code: 'RM001', unit: 'kg', stock: 500 },
      { id: 'prod_005', name: '原材料B', code: 'RM002', unit: 'L', stock: 300 }
    ],
    selectedProductIndex: -1,
    selectedProduct: null,
    
    reasonOptions: [],
    selectedReasonIndex: -1,
    
    departmentOptions: [
      { id: 'dept_001', name: '生产部' },
      { id: 'dept_002', name: '质检部' },
      { id: 'dept_003', name: '包装部' },
      { id: 'dept_004', name: '仓储部' }
    ],
    selectedDepartmentIndex: -1,
    
    // 计算属性
    afterStock: 0,
    isFormValid: false,
    maxDate: ''
  },

  onLoad(options) {
    console.log('库存出入库页面加载', options)
    this.initPage()

    // 如果指定了操作类型
    if (options.type && (options.type === 'in' || options.type === 'out')) {
      this.setData({ operationType: options.type })
      this.updateReasonOptions()
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时重新加载数据
    this.loadInventoryData()
  },

  /**
   * 设置操作类型（供外部调用）
   */
  setOperationType(type) {
    if (type === 'in' || type === 'out') {
      this.setData({
        operationType: type
      })
      this.updateReasonOptions()
      this.resetForm()
    }
  },

  /**
   * 初始化页面
   */
  initPage() {
    // 设置最大日期为今天
    const today = new Date()
    const maxDate = formatDate(today, 'YYYY-MM-DD')
    const operationDate = formatDate(today, 'YYYY-MM-DD')
    
    this.setData({ 
      maxDate,
      'formData.operationDate': operationDate
    })
    
    this.updateReasonOptions()
    this.loadInventoryData()
  },

  /**
   * 更新原因选项
   */
  updateReasonOptions() {
    const inReasons = [
      { value: 'purchase', label: '采购入库' },
      { value: 'production', label: '生产入库' },
      { value: 'return', label: '退货入库' },
      { value: 'adjustment', label: '盘点调整' },
      { value: 'other', label: '其他' }
    ]
    
    const outReasons = [
      { value: 'production', label: '生产领用' },
      { value: 'sale', label: '销售出库' },
      { value: 'damage', label: '损耗出库' },
      { value: 'adjustment', label: '盘点调整' },
      { value: 'other', label: '其他' }
    ]
    
    this.setData({
      reasonOptions: this.data.operationType === 'in' ? inReasons : outReasons,
      selectedReasonIndex: -1,
      'formData.reason': '',
      'formData.reasonText': ''
    })
  },

  /**
   * 加载库存数据
   */
  loadInventoryData() {
    try {
      // 从本地存储获取库存数据
      const inventory = wx.getStorageSync('inventory') || {}
      
      // 更新产品库存信息
      const updatedProducts = this.data.productOptions.map(product => {
        const stockInfo = inventory[product.id]
        return {
          ...product,
          stock: stockInfo ? stockInfo.stock : product.stock
        }
      })
      
      this.setData({ productOptions: updatedProducts })
    } catch (error) {
      console.error('加载库存数据失败:', error)
    }
  },

  /**
   * 切换操作类型
   */
  switchOperation(e) {
    const type = e.currentTarget.dataset.type
    this.setData({ operationType: type })
    this.updateReasonOptions()
    this.calculateAfterStock()
    this.validateForm()
  },

  /**
   * 选择产品
   */
  selectProduct(e) {
    const index = e.detail.value
    const product = this.data.productOptions[index]
    
    this.setData({
      selectedProductIndex: index,
      selectedProduct: product,
      'formData.productName': product.name,
      'formData.productCode': product.code,
      'formData.unit': product.unit
    })
    
    this.calculateAfterStock()
    this.validateForm()
  },

  /**
   * 选择原因
   */
  selectReason(e) {
    const index = e.detail.value
    const reason = this.data.reasonOptions[index]
    
    this.setData({
      selectedReasonIndex: index,
      'formData.reason': reason.value,
      'formData.reasonText': reason.label
    })
    
    this.validateForm()
  },

  /**
   * 选择部门
   */
  selectDepartment(e) {
    const index = e.detail.value
    const department = this.data.departmentOptions[index]
    
    this.setData({
      selectedDepartmentIndex: index,
      'formData.departmentName': department.name
    })
  },

  /**
   * 选择操作日期
   */
  selectOperationDate(e) {
    this.setData({
      'formData.operationDate': e.detail.value
    })
  },

  /**
   * 输入事件处理
   */
  onQuantityInput(e) {
    const quantity = parseInt(e.detail.value) || 1
    this.setData({ 'formData.quantity': quantity })
    this.calculateAfterStock()
    this.validateForm()
  },

  onSupplierInput(e) {
    this.setData({ 'formData.supplier': e.detail.value })
  },

  onRecipientInput(e) {
    this.setData({ 'formData.recipient': e.detail.value })
  },

  onNotesInput(e) {
    this.setData({ 'formData.notes': e.detail.value })
  },

  /**
   * 数量操作
   */
  decreaseQuantity() {
    const quantity = Math.max(1, this.data.formData.quantity - 1)
    this.setData({ 'formData.quantity': quantity })
    this.calculateAfterStock()
    this.validateForm()
  },

  increaseQuantity() {
    const quantity = this.data.formData.quantity + 1
    this.setData({ 'formData.quantity': quantity })
    this.calculateAfterStock()
    this.validateForm()
  },

  /**
   * 计算操作后库存
   */
  calculateAfterStock() {
    if (!this.data.selectedProduct) {
      this.setData({ afterStock: 0 })
      return
    }
    
    const currentStock = this.data.selectedProduct.stock || 0
    const quantity = this.data.formData.quantity
    
    let afterStock
    if (this.data.operationType === 'in') {
      afterStock = currentStock + quantity
    } else {
      afterStock = currentStock - quantity
    }
    
    this.setData({ afterStock })
  },

  /**
   * 验证表单
   */
  validateForm() {
    const { productName, quantity, reason } = this.data.formData
    const isValid = productName && quantity > 0 && reason
    this.setData({ isFormValid: isValid })
  },

  /**
   * 取消
   */
  cancel() {
    wx.navigateBack()
  },

  /**
   * 提交操作
   */
  submitOperation() {
    if (!this.data.isFormValid) {
      wx.showToast({
        title: '请完善操作信息',
        icon: 'none'
      })
      return
    }

    // 出库时检查库存
    if (this.data.operationType === 'out' && this.data.afterStock < 0) {
      wx.showToast({
        title: '库存不足',
        icon: 'none'
      })
      return
    }

    const operationText = this.data.operationType === 'in' ? '入库' : '出库'
    wx.showModal({
      title: `确认${operationText}`,
      content: `确定要执行此${operationText}操作吗？`,
      success: (res) => {
        if (res.confirm) {
          this.executeOperation()
        }
      }
    })
  },

  /**
   * 执行操作
   */
  async executeOperation() {
    try {
      const operationText = this.data.operationType === 'in' ? '入库' : '出库'
      wx.showLoading({ title: `${operationText}中...` })

      // 生成操作记录
      const operationRecord = {
        id: `operation_${Date.now()}`,
        type: this.data.operationType,
        productId: this.data.selectedProduct.id,
        productName: this.data.formData.productName,
        productCode: this.data.formData.productCode,
        quantity: this.data.formData.quantity,
        unit: this.data.formData.unit,
        reason: this.data.formData.reason,
        reasonText: this.data.formData.reasonText,
        supplier: this.data.formData.supplier,
        departmentName: this.data.formData.departmentName,
        recipient: this.data.formData.recipient,
        operationDate: this.data.formData.operationDate,
        notes: this.data.formData.notes,
        beforeStock: this.data.selectedProduct.stock || 0,
        afterStock: this.data.afterStock,
        createdAt: new Date().toISOString(),
        operator: '当前用户' // 实际应用中应该从用户信息获取
      }

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 更新库存
      this.updateInventory(operationRecord)

      // 保存操作记录
      this.saveOperationRecord(operationRecord)

      wx.hideLoading()

      wx.showToast({
        title: `${operationText}成功`,
        icon: 'success'
      })

      // 延迟返回
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)

    } catch (error) {
      console.error('执行操作失败:', error)
      wx.hideLoading()
      wx.showToast({
        title: '操作失败',
        icon: 'error'
      })
    }
  },

  /**
   * 更新库存
   */
  updateInventory(record) {
    try {
      // 获取当前库存数据
      const inventory = wx.getStorageSync('inventory') || {}

      // 更新指定产品的库存
      inventory[record.productId] = {
        productId: record.productId,
        productName: record.productName,
        productCode: record.productCode,
        unit: record.unit,
        stock: record.afterStock,
        lastUpdated: record.createdAt
      }

      // 保存库存数据
      wx.setStorageSync('inventory', inventory)

    } catch (error) {
      console.error('更新库存失败:', error)
    }
  },

  /**
   * 保存操作记录
   */
  saveOperationRecord(record) {
    try {
      // 获取操作记录
      const records = wx.getStorageSync('inventoryRecords') || []

      // 添加新记录
      records.unshift(record)

      // 保存操作记录
      wx.setStorageSync('inventoryRecords', records)

    } catch (error) {
      console.error('保存操作记录失败:', error)
    }
  }
})
