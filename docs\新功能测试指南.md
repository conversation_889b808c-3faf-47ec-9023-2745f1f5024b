# 🎯 新功能测试指南

## 📱 **新的底部导航栏配置**

**TabBar页面**（可直接通过底部导航访问）：
- 🏠 **首页** - `pages/index/index`
- 📝 **下单** - `pages/order/create/create`
- 🏭 **工单** - `pages/workorder/list/list`
- 📋 **包装** - `pages/packaging/list/list`
- 📦 **出入库** - `pages/inventory/inout/inout`

**非TabBar页面**（需要通过页面跳转访问）：
- 订单列表 - `pages/order/list/list`
- 订单详情 - `pages/order/detail/detail`
- 工单创建 - `pages/workorder/create/create`
- 工单详情 - `pages/workorder/detail/detail`
- 库存列表 - `pages/inventory/list/list`

## ✅ **已完成的功能转换**

### 1. 订单创建功能（已完善）
**页面路径**: `pages/order/create/create` - **TabBar直达**

**新增功能**:
- ✨ **订单号自动生成**：页面加载时自动生成订单号
- ✨ **产品编码自动填充**：选择产品时自动填充编码
- ✨ **包装信息管理**：包装类型选择和包装要求
- ✨ **增强表单验证**：实时验证数量、单价、日期等
- ✨ **智能工单创建**：订单创建成功后可直接创建工单

**测试步骤**:
1. **通过TabBar直接进入下单页面**
2. **验证订单信息**：
   - 检查订单号是否自动生成（格式：PO + 时间戳）
3. **填写客户信息**：
   - 选择客户（自动填充联系人和电话）
   - 手动修改联系人和电话
4. **配置产品信息**：
   - 选择产品（自动填充产品编码、单位、单价）
   - 输入产品规格
   - 调整数量（测试+/-按钮和直接输入）
   - 修改单价（验证数字输入）
5. **设置包装信息**：
   - 选择包装类型（标准、防潮、木箱、托盘、定制）
   - 填写包装要求
6. **配置交付信息**：
   - 选择交付日期（验证不能选择过去日期）
   - 设置优先级（低、普通、高、紧急）
   - 填写交付地址
7. **添加备注信息**
8. **验证总金额计算**（数量 × 单价）
9. **测试表单验证**：
   - 必填项为空时的提示
   - 数量范围验证（1-10000）
   - 单价范围验证（0-999999）
   - 日期验证（不能是过去）
10. **保存草稿功能**
11. **提交订单**：
    - 验证提交确认弹窗
    - 成功后的工单创建选择

**验证点**:
- [ ] 订单号自动生成且格式正确
- [ ] 产品选择时编码、单位、单价自动填充
- [ ] 包装类型选择器正常工作
- [ ] 数量调整按钮响应正确（+/-）
- [ ] 总金额实时计算准确
- [ ] 表单验证提示清晰准确
- [ ] 日期选择限制正确（不能选过去）
- [ ] 草稿保存和恢复功能正常
- [ ] 提交成功后弹窗选择正确
- [ ] 可选择创建工单或返回列表
- [ ] 新订单数据结构完整（包含包装信息）

### 2. 工单创建功能
**页面路径**: `pages/workorder/create/create`

**测试步骤**:
1. 选择创建方式：
   - **从订单创建**: 选择现有订单，自动填充信息
   - **手动创建**: 手动输入所有信息
2. 设置生产配置（数量、优先级、开始时间）
3. 添加工单备注
4. 提交创建

**验证点**:
- [ ] 创建方式选择正常
- [ ] 从订单创建时信息自动填充
- [ ] 手动创建时表单验证正确
- [ ] 工单创建成功
- [ ] 关联订单状态更新为"生产中"

### 3. 库存出入库功能
**页面路径**: `pages/inventory/inout/inout`

**测试步骤**:
1. 选择操作类型（入库/出库）
2. 选择产品
3. 查看当前库存
4. 输入操作数量
5. 选择操作原因
6. 填写相关信息（供应商/部门/领用人）
7. 查看操作预览
8. 确认操作

**验证点**:
- [ ] 操作类型切换正常
- [ ] 当前库存显示正确
- [ ] 操作后库存计算正确
- [ ] 出库时库存不足检查
- [ ] 操作记录保存成功
- [ ] 库存数据实时更新

### 4. 首页快捷操作
**页面路径**: `pages/index/index`

**新增快捷操作**:
- 入库（跳转到入库页面）
- 出库（跳转到出库页面）
- 库存查询（原有功能）

**验证点**:
- [ ] 入库按钮跳转正确
- [ ] 出库按钮跳转正确
- [ ] 参数传递正确（type=in/out）

## 🔄 **完整业务流程测试**

### 流程1: 订单到工单完整流程
1. **创建订单** → 2. **确认订单** → 3. **从订单创建工单** → 4. **验证关联**

**详细步骤**:
```
1. 首页 → 点击"新建订单"
2. 填写完整订单信息 → 提交
3. 订单列表 → 找到新订单 → 点击"创建工单"
4. 工单创建页 → 验证订单信息自动填充 → 提交
5. 验证订单状态变为"生产中"
6. 工单列表 → 验证新工单存在
```

### 流程2: 库存管理完整流程
1. **查看库存** → 2. **入库操作** → 3. **验证更新** → 4. **出库操作** → 5. **验证减少**

**详细步骤**:
```
1. 库存列表 → 记录某产品当前库存数量
2. 首页 → 点击"入库" → 选择该产品 → 入库10件
3. 返回库存列表 → 验证库存增加10件
4. 首页 → 点击"出库" → 选择该产品 → 出库5件
5. 返回库存列表 → 验证库存减少5件
```

## 📱 **界面和交互测试**

### 表单交互
- [ ] 选择器（picker）正常工作
- [ ] 输入框焦点和输入正常
- [ ] 数量调整按钮响应及时
- [ ] 表单验证提示清晰
- [ ] 提交按钮状态正确

### 页面导航
- [ ] 页面跳转正常
- [ ] 参数传递正确
- [ ] 返回功能正常
- [ ] TabBar切换正常

### 数据同步
- [ ] 创建后列表实时更新
- [ ] 状态变更同步显示
- [ ] 库存数据实时同步

## 🐛 **错误处理测试**

### 表单验证
- [ ] 必填项为空时提示
- [ ] 数量为0或负数时提示
- [ ] 出库数量超过库存时提示
- [ ] 日期选择限制正确

### 边界条件
- [ ] 库存为0时出库处理
- [ ] 大数量输入处理
- [ ] 特殊字符输入处理
- [ ] 网络异常处理

## 📊 **数据验证测试**

### 本地存储验证
```javascript
// 在控制台执行以下命令验证数据存储
wx.getStorageSync('orders')        // 订单数据
wx.getStorageSync('workOrders')    // 工单数据  
wx.getStorageSync('inventory')     // 库存数据
wx.getStorageSync('inventoryRecords') // 操作记录
```

### 数据结构验证
- [ ] 订单数据结构完整
- [ ] 工单数据结构完整
- [ ] 库存数据结构完整
- [ ] 操作记录数据完整

## 🎯 **重点测试场景**

### 场景1: 新用户首次使用
1. 清空所有本地数据
2. 按照业务流程依次操作
3. 验证数据累积正确

### 场景2: 数据一致性
1. 在多个页面间切换
2. 验证数据同步正确
3. 检查状态更新传播

### 场景3: 异常恢复
1. 表单填写一半退出
2. 重新进入验证草稿恢复
3. 网络异常时的处理

## 📝 **测试报告模板**

```
测试时间：2024年__月__日
测试人员：________
测试环境：微信开发者工具

✅ 新功能测试结果：
□ 订单创建功能 - 通过/失败
□ 工单创建功能 - 通过/失败
□ 库存出入库功能 - 通过/失败
□ 首页快捷操作 - 通过/失败

✅ 业务流程测试：
□ 订单到工单流程 - 通过/失败
□ 库存管理流程 - 通过/失败

✅ 界面交互测试：
□ 表单交互 - 通过/失败
□ 页面导航 - 通过/失败
□ 数据同步 - 通过/失败

发现问题：
1. ________________
2. ________________
3. ________________

总体评价：□ 完全通过 □ 基本通过 □ 需要修复

建议改进：
1. ________________
2. ________________
```

## 🚀 **下一步开发计划**

测试通过后，可以继续完善：

1. **订单详情页面** - 显示订单完整信息
2. **工单详情页面** - 显示工单进度和任务
3. **库存详情页面** - 显示库存历史记录
4. **数据统计页面** - 添加图表和报表
5. **用户权限管理** - 添加角色和权限控制

---

**🎯 测试重点：确保所有新增功能正常工作，数据流转正确，用户体验良好！**
