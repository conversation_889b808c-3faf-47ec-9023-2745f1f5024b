<!--生产管理系统 - 设置页面 - 简约现代风格-->
<view class="modern-page">
  <!-- 页面头部 - 简约现代风格 -->
  <view class="modern-navbar">
    <view class="header-content">
      <text class="modern-navbar-title">系统设置</text>
      <text class="page-subtitle">管理客户、产品和包装信息</text>
    </view>
  </view>

  <!-- 设置选项列表 - 简约现代风格 -->
  <view class="modern-container">
    <view class="modern-list">
      <view
        class="modern-list-item setting-item"
        wx:for="{{ settingsOptions }}"
        wx:key="id"
        bindtap="navigateToSetting"
        data-url="{{ item.url }}"
      >
        <view class="setting-icon">
          <text class="icon-text">{{ item.icon }}</text>
        </view>

        <view class="setting-content">
          <view class="setting-title">{{ item.title }}</view>
          <view class="setting-description">{{ item.description }}</view>
        </view>

        <view class="setting-arrow">
          <text class="arrow-icon">›</text>
        </view>
      </view>
    </view>

    <!-- 底部说明 - 简约现代风格 -->
    <view class="modern-section footer-info">
      <view class="modern-section-title">
        <text class="modern-section-icon">💡</text>
        <text class="modern-section-text">使用提示</text>
      </view>
      <view class="modern-section-content">
        在这里可以管理订单相关的基础数据，包括客户信息、产品规格和包装要求。
      </view>
    </view>
  </view>
</view>
