/* 工艺路线管理页面样式 - 简约现代风格 */
@import '../../../styles/modern-simple.wxss';

/* 页面头部增强 */
.header-content {
  flex: 1;
}

.page-subtitle {
  font-size: 24rpx;
  color: #8E8E93;
  margin-top: 8rpx;
  display: block;
}

/* 搜索栏样式 */
.search-container {
  margin-bottom: 24rpx;
}

.modern-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.modern-input {
  flex: 1;
  height: 88rpx;
  padding: 0 24rpx 0 60rpx;
  background-color: #FFFFFF;
  border: 2rpx solid #E5E5EA;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #1D1D1F;
}

.modern-input:focus {
  border-color: #007AFF;
  box-shadow: 0 0 0 6rpx rgba(0, 122, 255, 0.1);
}

.search-icon {
  position: absolute;
  left: 24rpx;
  font-size: 32rpx;
  color: #8E8E93;
  z-index: 1;
}

/* 工艺路线列表样式 */
.routes-section {
  margin-bottom: 40rpx;
}

.route-count {
  background-color: #007AFF;
  color: #FFFFFF;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  margin-left: auto;
  min-width: 40rpx;
  text-align: center;
}

.route-list {
  margin-top: 20rpx;
}

.route-card {
  margin-bottom: 16rpx;
  transition: all 0.2s ease;
}

.route-card:active {
  transform: translateY(2rpx) scale(0.99);
}

.route-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.route-info {
  flex: 1;
}

.route-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1D1D1F;
  display: block;
  margin-bottom: 12rpx;
}

.route-meta {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.route-id {
  font-size: 20rpx;
  color: #8E8E93;
  background-color: #F2F2F7;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.route-actions {
  display: flex;
  gap: 12rpx;
  margin-left: 16rpx;
}

.action-btn {
  width: 64rpx;
  height: 64rpx;
  border-radius: 12rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  transition: all 0.2s ease;
}

.edit-btn {
  background-color: #E3F2FD;
  color: #007AFF;
}

.edit-btn:active {
  background-color: #BBDEFB;
  transform: scale(0.95);
}

.delete-btn {
  background-color: #FFEBEE;
  color: #FF3B30;
}

.delete-btn:active {
  background-color: #FFCDD2;
  transform: scale(0.95);
}

.route-body {
  margin-bottom: 16rpx;
}

.route-description {
  font-size: 28rpx;
  color: #48484A;
  line-height: 1.5;
  margin-bottom: 16rpx;
}

/* 工序预览样式 */
.process-list-preview {
  margin-top: 16rpx;
}

.process-preview-title {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 12rpx;
}

.process-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.process-tag {
  background-color: #F2F2F7;
  color: #48484A;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  border: 1rpx solid #E5E5EA;
}

.route-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16rpx;
  border-top: 1rpx solid #E5E5EA;
}

.route-stats {
  display: flex;
  gap: 24rpx;
}

.stat-item {
  display: flex;
  align-items: center;
}

.stat-label {
  font-size: 24rpx;
  color: #8E8E93;
  margin-right: 8rpx;
}

.stat-value {
  font-size: 24rpx;
  font-weight: 600;
  color: #007AFF;
}

.route-time {
  font-size: 20rpx;
  color: #8E8E93;
}

/* 大弹窗样式 */
.large-modal {
  max-width: 700rpx;
  max-height: 90vh;
}

.large-modal .modal-body {
  max-height: 600rpx;
}

/* 表单增强样式 */
.form-label-with-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.add-process-btn {
  background-color: #007AFF;
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 12rpx 20rpx;
  border-radius: 12rpx;
  border: none;
}

.add-process-btn:active {
  background-color: #0056CC;
}

/* 已选工序列表样式 */
.selected-processes {
  border: 2rpx solid #E5E5EA;
  border-radius: 16rpx;
  overflow: hidden;
  margin-top: 12rpx;
}

.process-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background-color: #FFFFFF;
  border-bottom: 1rpx solid #E5E5EA;
}

.process-item:last-child {
  border-bottom: none;
}

.process-order {
  width: 48rpx;
  height: 48rpx;
  background-color: #007AFF;
  color: #FFFFFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.process-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.process-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #1D1D1F;
  margin-bottom: 4rpx;
}

.process-hours {
  font-size: 24rpx;
  color: #8E8E93;
}

.process-controls {
  display: flex;
  gap: 8rpx;
  margin-left: 16rpx;
}

.control-btn {
  width: 56rpx;
  height: 56rpx;
  border-radius: 8rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  transition: all 0.2s ease;
}

.control-btn:not(.remove-btn) {
  background-color: #F2F2F7;
  color: #007AFF;
}

.control-btn:not(.remove-btn):active {
  background-color: #E5E5EA;
}

.control-btn:disabled {
  background-color: #F2F2F7;
  color: #C7C7CC;
}

.remove-btn {
  background-color: #FFEBEE;
  color: #FF3B30;
}

.remove-btn:active {
  background-color: #FFCDD2;
}

/* 空状态样式 */
.empty-processes {
  padding: 40rpx;
  text-align: center;
  background-color: #F8F9FA;
  border: 2rpx dashed #E5E5EA;
  border-radius: 16rpx;
  margin-top: 12rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #8E8E93;
}

/* 总工时显示 */
.total-hours {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx;
  background-color: #F0F8FF;
  border-radius: 12rpx;
  margin-top: 16rpx;
}

.total-label {
  font-size: 28rpx;
  color: #48484A;
  margin-right: 8rpx;
}

.total-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #007AFF;
}

/* 工序选择弹窗样式 */
.process-search {
  margin-bottom: 20rpx;
}

.available-processes {
  max-height: 400rpx;
  overflow-y: auto;
}

.available-process-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background-color: #FFFFFF;
  border: 1rpx solid #E5E5EA;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
  transition: all 0.2s ease;
}

.available-process-item:active {
  background-color: #F8F9FA;
  transform: scale(0.98);
}

.available-process-item:last-child {
  margin-bottom: 0;
}

.available-process-item .process-info {
  flex: 1;
}

.available-process-item .process-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #1D1D1F;
  margin-bottom: 4rpx;
}

.available-process-item .process-category {
  font-size: 24rpx;
  color: #8E8E93;
}

.available-process-item .process-hours {
  font-size: 24rpx;
  font-weight: 600;
  color: #007AFF;
}

/* 删除确认弹窗样式 */
.delete-modal .modal-body {
  padding: 40rpx 32rpx;
}

.delete-warning {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.warning-icon {
  font-size: 48rpx;
  color: #FF9500;
  flex-shrink: 0;
}

.warning-text {
  flex: 1;
}

.warning-text > view:first-child {
  font-size: 32rpx;
  font-weight: 500;
  color: #1D1D1F;
  margin-bottom: 8rpx;
}

.warning-note {
  font-size: 24rpx;
  color: #8E8E93;
}

.delete-confirm-btn {
  background-color: #FF3B30;
  color: #FFFFFF;
}

.delete-confirm-btn:active {
  background-color: #D70015;
}

.delete-confirm-btn:disabled {
  background-color: #FFCDD2;
  color: #FF8A80;
}

/* 响应式调整 */
@media (max-width: 400px) {
  .route-actions {
    flex-direction: column;
    gap: 8rpx;
  }
  
  .action-btn {
    width: 56rpx;
    height: 56rpx;
    font-size: 24rpx;
  }
  
  .route-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 12rpx;
  }
  
  .route-stats {
    gap: 16rpx;
  }
  
  .process-controls {
    flex-direction: column;
    gap: 4rpx;
  }
  
  .control-btn {
    width: 48rpx;
    height: 48rpx;
    font-size: 20rpx;
  }
}
