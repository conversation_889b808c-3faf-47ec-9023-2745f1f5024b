/* 工序管理页面样式 - 简约现代风格 */
@import '../../../styles/modern-simple.wxss';

/* 页面头部增强 */
.header-content {
  flex: 1;
}

.page-subtitle {
  font-size: 24rpx;
  color: #8E8E93;
  margin-top: 8rpx;
  display: block;
}

/* 搜索栏样式 */
.search-container {
  margin-bottom: 24rpx;
}

.modern-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.modern-input {
  flex: 1;
  height: 88rpx;
  padding: 0 24rpx 0 60rpx;
  background-color: #FFFFFF;
  border: 2rpx solid #E5E5EA;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #1D1D1F;
}

.modern-input:focus {
  border-color: #007AFF;
  box-shadow: 0 0 0 6rpx rgba(0, 122, 255, 0.1);
}

.search-icon {
  position: absolute;
  left: 24rpx;
  font-size: 32rpx;
  color: #8E8E93;
  z-index: 1;
}

/* 分类筛选样式 */
.filter-section {
  margin-bottom: 32rpx;
}

.category-scroll {
  white-space: nowrap;
}

.category-filters {
  display: flex;
  gap: 16rpx;
  padding: 0 4rpx;
}

.category-filter {
  flex-shrink: 0;
  padding: 12rpx 24rpx;
  background-color: #F2F2F7;
  color: #8E8E93;
  border-radius: 20rpx;
  font-size: 24rpx;
  transition: all 0.2s ease;
}

.category-filter.active {
  background-color: #007AFF;
  color: #FFFFFF;
}

.category-filter:active {
  transform: scale(0.95);
}

/* 工序列表样式 */
.processes-section {
  margin-bottom: 40rpx;
}

.process-count {
  background-color: #007AFF;
  color: #FFFFFF;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  margin-left: auto;
  min-width: 40rpx;
  text-align: center;
}

.process-list {
  margin-top: 20rpx;
}

.process-card {
  margin-bottom: 16rpx;
  transition: all 0.2s ease;
}

.process-card:active {
  transform: translateY(2rpx) scale(0.99);
}

.process-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.process-info {
  flex: 1;
}

.process-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1D1D1F;
  display: block;
  margin-bottom: 12rpx;
}

.process-meta {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.process-id {
  font-size: 20rpx;
  color: #8E8E93;
  background-color: #F2F2F7;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.process-actions {
  display: flex;
  gap: 12rpx;
  margin-left: 16rpx;
}

.action-btn {
  width: 64rpx;
  height: 64rpx;
  border-radius: 12rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  transition: all 0.2s ease;
}

.edit-btn {
  background-color: #E3F2FD;
  color: #007AFF;
}

.edit-btn:active {
  background-color: #BBDEFB;
  transform: scale(0.95);
}

.delete-btn {
  background-color: #FFEBEE;
  color: #FF3B30;
}

.delete-btn:active {
  background-color: #FFCDD2;
  transform: scale(0.95);
}

.process-body {
  margin-bottom: 16rpx;
}

.process-description {
  font-size: 28rpx;
  color: #48484A;
  line-height: 1.5;
}

.process-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16rpx;
  border-top: 1rpx solid #E5E5EA;
}

.process-hours {
  display: flex;
  align-items: center;
}

.hours-label {
  font-size: 24rpx;
  color: #8E8E93;
  margin-right: 8rpx;
}

.hours-value {
  font-size: 24rpx;
  font-weight: 600;
  color: #007AFF;
}

.process-time {
  font-size: 20rpx;
  color: #8E8E93;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
}

.modal-container {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 16rpx 32rpx rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E5EA;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1D1D1F;
}

.modal-close {
  width: 64rpx;
  height: 64rpx;
  border-radius: 12rpx;
  border: none;
  background-color: #F2F2F7;
  color: #8E8E93;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:active {
  background-color: #E5E5EA;
}

.modal-body {
  padding: 32rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 32rpx;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #1D1D1F;
  margin-bottom: 12rpx;
}

.modern-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 24rpx;
  background-color: #FFFFFF;
  border: 2rpx solid #E5E5EA;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #1D1D1F;
  line-height: 1.5;
  box-sizing: border-box;
}

.modern-textarea:focus {
  border-color: #007AFF;
  box-shadow: 0 0 0 6rpx rgba(0, 122, 255, 0.1);
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 1rpx solid #E5E5EA;
}

.modal-footer .modern-btn {
  flex: 1;
}

/* 删除确认弹窗样式 */
.delete-modal .modal-body {
  padding: 40rpx 32rpx;
}

.delete-warning {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.warning-icon {
  font-size: 48rpx;
  color: #FF9500;
  flex-shrink: 0;
}

.warning-text {
  flex: 1;
}

.warning-text > view:first-child {
  font-size: 32rpx;
  font-weight: 500;
  color: #1D1D1F;
  margin-bottom: 8rpx;
}

.warning-note {
  font-size: 24rpx;
  color: #8E8E93;
}

.delete-confirm-btn {
  background-color: #FF3B30;
  color: #FFFFFF;
}

.delete-confirm-btn:active {
  background-color: #D70015;
}

.delete-confirm-btn:disabled {
  background-color: #FFCDD2;
  color: #FF8A80;
}

/* 响应式调整 */
@media (max-width: 400px) {
  .process-actions {
    flex-direction: column;
    gap: 8rpx;
  }
  
  .action-btn {
    width: 56rpx;
    height: 56rpx;
    font-size: 24rpx;
  }
  
  .process-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 8rpx;
  }
}
