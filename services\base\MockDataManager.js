/**
 * Mock数据管理器
 * 统一管理所有Mock数据，避免数据分散在各个服务文件中
 */

class MockDataManager {
  constructor() {
    this.data = {
      orders: [],
      customers: [],
      products: [],
      categories: [],
      packaging: [],
      workOrders: [],
      tasks: [],
      inventory: [],
      processes: [],
      routes: []
    }
    
    this.initializeData()
  }

  /**
   * 初始化所有Mock数据
   */
  initializeData() {
    this.initCustomers()
    this.initCategories()
    this.initProducts()
    this.initPackaging()
    this.initOrders()
    this.initWorkOrders()
    this.initTasks()
    this.initInventory()
    this.initProcesses()
    this.initRoutes()
  }

  /**
   * 初始化客户数据
   */
  initCustomers() {
    this.data.customers = [
      {
        id: 'customer_001',
        name: '华东制造有限公司',
        contact: '张经理',
        phone: '13800138001',
        address: '上海市浦东新区张江高科技园区',
        email: '<EMAIL>',
        notes: '重要客户，长期合作伙伴',
        createdAt: '2024-01-01T08:00:00.000Z',
        updatedAt: '2024-01-01T08:00:00.000Z'
      },
      {
        id: 'customer_002',
        name: '江南机械集团',
        contact: '李总',
        phone: '13800138002',
        address: '江苏省苏州市工业园区',
        email: '<EMAIL>',
        notes: '大客户，订单量大',
        createdAt: '2024-01-02T09:15:00.000Z',
        updatedAt: '2024-01-02T09:15:00.000Z'
      },
      {
        id: 'customer_003',
        name: '北方重工股份',
        contact: '王主管',
        phone: '13800138003',
        address: '辽宁省沈阳市铁西区',
        email: '<EMAIL>',
        notes: '国企客户，付款及时',
        createdAt: '2024-01-03T11:30:00.000Z',
        updatedAt: '2024-01-03T11:30:00.000Z'
      },
      {
        id: 'customer_004',
        name: '南方电机制造',
        contact: '赵工程师',
        phone: '13800138004',
        address: '广东省深圳市南山区',
        email: '<EMAIL>',
        notes: '技术要求高，质量标准严格',
        createdAt: '2024-01-04T14:20:00.000Z',
        updatedAt: '2024-01-04T14:20:00.000Z'
      }
    ]
  }

  /**
   * 初始化分类数据
   */
  initCategories() {
    this.data.categories = [
      {
        id: 'cat_001',
        name: '电子产品',
        description: '各类电子设备和配件',
        createdAt: '2024-01-01T08:00:00.000Z',
        updatedAt: '2024-01-01T08:00:00.000Z'
      },
      {
        id: 'cat_002',
        name: '机械配件',
        description: '机械设备相关配件',
        createdAt: '2024-01-01T08:00:00.000Z',
        updatedAt: '2024-01-01T08:00:00.000Z'
      },
      {
        id: 'cat_003',
        name: '原材料',
        description: '生产用原材料',
        createdAt: '2024-01-01T08:00:00.000Z',
        updatedAt: '2024-01-01T08:00:00.000Z'
      }
    ]
  }

  /**
   * 初始化产品数据
   */
  initProducts() {
    this.data.products = [
      {
        id: 'product_001',
        name: '精密轴承',
        code: 'PRD001',
        categoryId: 'cat_002',
        specification: '内径50mm，外径90mm',
        unit: '个',
        price: 120.00,
        description: '高精度工业轴承',
        createdAt: '2024-01-01T08:00:00.000Z',
        updatedAt: '2024-01-01T08:00:00.000Z'
      },
      {
        id: 'product_002',
        name: '控制电路板',
        code: 'PRD002',
        categoryId: 'cat_001',
        specification: '12V，50A',
        unit: '块',
        price: 350.00,
        description: '工业控制用电路板',
        createdAt: '2024-01-01T08:00:00.000Z',
        updatedAt: '2024-01-01T08:00:00.000Z'
      },
      {
        id: 'product_003',
        name: '不锈钢管',
        code: 'PRD003',
        categoryId: 'cat_003',
        specification: '直径25mm，长度2m',
        unit: '根',
        price: 85.00,
        description: '304不锈钢管材',
        createdAt: '2024-01-01T08:00:00.000Z',
        updatedAt: '2024-01-01T08:00:00.000Z'
      }
    ]
  }

  /**
   * 初始化包装数据
   */
  initPackaging() {
    this.data.packaging = [
      {
        id: 'pkg_001',
        name: '标准包装',
        description: '普通纸箱包装',
        specifications: '适用于一般产品',
        createdAt: '2024-01-01T08:00:00.000Z',
        updatedAt: '2024-01-01T08:00:00.000Z'
      },
      {
        id: 'pkg_002',
        name: '防潮包装',
        description: '防潮纸箱+塑料薄膜',
        specifications: '适用于电子产品',
        createdAt: '2024-01-01T08:00:00.000Z',
        updatedAt: '2024-01-01T08:00:00.000Z'
      },
      {
        id: 'pkg_003',
        name: '木箱包装',
        description: '实木箱体包装',
        specifications: '适用于重型设备',
        createdAt: '2024-01-01T08:00:00.000Z',
        updatedAt: '2024-01-01T08:00:00.000Z'
      }
    ]
  }

  /**
   * 初始化订单数据
   */
  initOrders() {
    this.data.orders = [
      {
        id: 'order_001',
        orderNo: 'PO20240101001',
        customerId: 'customer_001',
        customerName: '华东制造有限公司',
        customerContact: '张经理',
        customerPhone: '13800138001',
        productId: 'product_001',
        productName: '精密轴承',
        productCode: 'PRD001',
        specification: '内径50mm，外径90mm',
        quantity: 100,
        unit: '个',
        unitPrice: 120.00,
        totalAmount: 12000.00,
        deliveryDate: '2024-02-15',
        status: 'confirmed',
        priority: 'normal',
        packagingId: 'pkg_001',
        packagingName: '标准包装',
        notes: '按时交货，质量要求高',
        createdAt: '2024-01-01T08:00:00.000Z',
        updatedAt: '2024-01-02T10:30:00.000Z'
      },
      {
        id: 'order_002',
        orderNo: 'PO20240102001',
        customerId: 'customer_002',
        customerName: '江南机械集团',
        customerContact: '李总',
        customerPhone: '13800138002',
        productId: 'product_002',
        productName: '控制电路板',
        productCode: 'PRD002',
        specification: '12V，50A',
        quantity: 50,
        unit: '块',
        unitPrice: 350.00,
        totalAmount: 17500.00,
        deliveryDate: '2024-02-20',
        status: 'processing',
        priority: 'high',
        packagingId: 'pkg_002',
        packagingName: '防潮包装',
        notes: '紧急订单，优先生产',
        createdAt: '2024-01-02T09:15:00.000Z',
        updatedAt: '2024-01-03T14:20:00.000Z'
      }
    ]
  }

  /**
   * 初始化工单数据
   */
  initWorkOrders() {
    this.data.workOrders = [
      {
        id: 'wo_001',
        workOrderNo: 'WO20240101001',
        orderId: 'order_001',
        orderNo: 'PO20240101001',
        productName: '精密轴承',
        productCode: 'PRD001',
        quantity: 100,
        status: 'in_progress',
        completedTasks: 2,
        totalTasks: 5,
        progressPercent: 40,
        startDate: '2024-01-05',
        expectedEndDate: '2024-02-10',
        createdAt: '2024-01-05T08:00:00.000Z',
        updatedAt: '2024-01-10T16:30:00.000Z'
      }
    ]
  }

  /**
   * 初始化任务数据
   */
  initTasks() {
    this.data.tasks = [
      {
        id: 'task_001',
        workOrderId: 'wo_001',
        workOrderNo: 'WO20240101001',
        processName: '原料准备',
        productName: '精密轴承',
        status: 'completed',
        assignedTo: 'emp_001',
        assignedName: '张师傅',
        estimatedHours: 4,
        actualHours: 3.5,
        startTime: '2024-01-05T08:00:00.000Z',
        endTime: '2024-01-05T11:30:00.000Z',
        createdAt: '2024-01-05T08:00:00.000Z',
        updatedAt: '2024-01-05T11:30:00.000Z'
      }
    ]
  }

  /**
   * 初始化库存数据
   */
  initInventory() {
    this.data.inventory = [
      {
        id: 'inv_001',
        productId: 'product_001',
        productName: '精密轴承',
        productCode: 'PRD001',
        currentStock: 150,
        safetyStock: 100,
        unit: '个',
        status: 'sufficient',
        lastUpdateTime: '2024-01-15T14:30:00.000Z'
      },
      {
        id: 'inv_002',
        productId: 'product_002',
        productName: '控制电路板',
        productCode: 'PRD002',
        currentStock: 25,
        safetyStock: 50,
        unit: '块',
        status: 'warning',
        lastUpdateTime: '2024-01-15T14:30:00.000Z'
      }
    ]
  }

  /**
   * 初始化工艺数据
   */
  initProcesses() {
    this.data.processes = [
      {
        id: 'process_001',
        name: '原料准备',
        description: '准备生产所需的原材料',
        estimatedHours: 2,
        sequence: 1,
        createdAt: '2024-01-01T08:00:00.000Z',
        updatedAt: '2024-01-01T08:00:00.000Z'
      },
      {
        id: 'process_002',
        name: '机械加工',
        description: '使用机床进行精密加工',
        estimatedHours: 8,
        sequence: 2,
        createdAt: '2024-01-01T08:00:00.000Z',
        updatedAt: '2024-01-01T08:00:00.000Z'
      }
    ]
  }

  /**
   * 初始化工艺路线数据
   */
  initRoutes() {
    this.data.routes = [
      {
        id: 'route_001',
        name: '精密轴承生产路线',
        productId: 'product_001',
        processes: [
          { processId: 'process_001', sequence: 1 },
          { processId: 'process_002', sequence: 2 }
        ],
        totalEstimatedHours: 10,
        createdAt: '2024-01-01T08:00:00.000Z',
        updatedAt: '2024-01-01T08:00:00.000Z'
      }
    ]
  }

  /**
   * 获取指定类型的数据
   * @param {string} type 数据类型
   * @returns {Array} 数据数组
   */
  getData(type) {
    return this.data[type] || []
  }

  /**
   * 设置指定类型的数据
   * @param {string} type 数据类型
   * @param {Array} data 数据数组
   */
  setData(type, data) {
    if (this.data.hasOwnProperty(type)) {
      this.data[type] = data
    }
  }

  /**
   * 获取所有数据类型
   * @returns {Array} 数据类型数组
   */
  getDataTypes() {
    return Object.keys(this.data)
  }

  /**
   * 重置指定类型的数据
   * @param {string} type 数据类型
   */
  resetData(type) {
    if (this.data.hasOwnProperty(type)) {
      this.data[type] = []
      // 重新初始化该类型的数据
      const initMethod = `init${type.charAt(0).toUpperCase() + type.slice(1)}`
      if (typeof this[initMethod] === 'function') {
        this[initMethod]()
      }
    }
  }

  /**
   * 重置所有数据
   */
  resetAllData() {
    this.getDataTypes().forEach(type => {
      this.resetData(type)
    })
  }
}

// 创建单例实例
const mockDataManager = new MockDataManager()

module.exports = mockDataManager
