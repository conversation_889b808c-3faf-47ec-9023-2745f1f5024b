/* 订单创建第二步样式 */
@import "../step1/step1.wxss";

/* 搜索容器 */
.search-container {
  padding: 20rpx;
  background: #FFFFFF;
  margin-bottom: 20rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background: #F8F9FA;
  border-radius: 8rpx;
  padding: 0 20rpx;
  height: 80rpx;
}

.search-icon {
  font-size: 28rpx;
  color: #8E8E93;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #1D1D1F;
}

/* 类别容器 */
.category-container {
  background: #FFFFFF;
  margin-bottom: 20rpx;
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: flex;
  padding: 20rpx;
  gap: 16rpx;
}

.category-item {
  flex-shrink: 0;
  padding: 16rpx 32rpx;
  background: #F8F9FA;
  border-radius: 40rpx;
  border: 2rpx solid transparent;
}

.category-item.active {
  background: #E3F2FD;
  border-color: #007AFF;
}

.category-name {
  font-size: 28rpx;
  color: #1D1D1F;
  white-space: nowrap;
}

.category-item.active .category-name {
  color: #007AFF;
  font-weight: 600;
}

/* 产品容器 */
.product-container {
  flex: 1;
  background: #FFFFFF;
  margin-bottom: 20rpx;
}

.product-list {
  height: 100%;
  padding: 20rpx;
}

.product-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #FFFFFF;
  border: 2rpx solid #F0F0F0;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.product-info {
  flex: 1;
  margin-right: 20rpx;
}

.product-header {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.product-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #1D1D1F;
  margin-right: 16rpx;
}

.product-code {
  font-size: 24rpx;
  color: #8E8E93;
  background: #F8F9FA;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}

.product-spec {
  font-size: 26rpx;
  color: #3C3C43;
  margin-bottom: 8rpx;
}

.product-meta {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.product-unit {
  font-size: 24rpx;
  color: #8E8E93;
}

.product-price {
  font-size: 28rpx;
  font-weight: 600;
  color: #007AFF;
}

/* 产品操作 */
.product-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.quantity-control {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  background: #F8F9FA !important;
  color: #1D1D1F !important;
  border: 2rpx solid #F0F0F0 !important;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-btn[disabled] {
  background: #F0F0F0 !important;
  color: #C7C7CC !important;
}

.quantity-input {
  width: 80rpx;
  height: 60rpx;
  text-align: center;
  border: 2rpx solid #F0F0F0;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.select-btn {
  padding: 12rpx 24rpx;
  background: #007AFF !important;
  color: #FFFFFF !important;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.select-btn.selected {
  background: #34C759 !important;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #1D1D1F;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #8E8E93;
}

/* 已选产品摘要 */
.selected-summary {
  background: #FFFFFF;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-top: 1rpx solid #F0F0F0;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.summary-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1D1D1F;
}

.summary-total {
  font-size: 28rpx;
  font-weight: 600;
  color: #007AFF;
}

.summary-list {
  white-space: nowrap;
}

.summary-item {
  display: inline-flex;
  align-items: center;
  background: #F8F9FA;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  margin-right: 12rpx;
}

.summary-name {
  font-size: 24rpx;
  color: #1D1D1F;
  margin-right: 8rpx;
}

.summary-quantity {
  font-size: 24rpx;
  color: #007AFF;
  font-weight: 600;
}
