# 生产管理系统小程序

## 项目简介

这是一个基于微信小程序原生开发的生产管理系统，专为制造业企业设计，提供完整的订单管理、客户管理、产品管理和包装管理功能。系统采用现代化的设计理念和用户体验，支持完整的生产订单业务流程。

## 🚀 核心功能

### 📋 订单管理
- **订单列表**：卡片式展示，包含客户信息、产品详情、包装信息
- **4步骤下单流程**：
  1. 基本信息（客户选择、交货日期、优先级）
  2. 产品选择（支持搜索、分类筛选、数量配置）
  3. 配置详情（包装类型、生产天数、特殊要求）
  4. 确认提交（订单预览、最终确认）

### 👥 客户管理
- 客户信息的增删改查
- 联系人、电话、邮箱、地址管理
- 客户选项下拉选择

### 📦 产品管理
- 产品信息管理（名称、编码、规格、单位、价格）
- 产品分类管理
- 产品搜索和筛选

### 📋 包装管理
- 包装类型管理（标准包装、防潮包装、木箱包装等）
- 包装规格说明
- 包装选项配置

### 🎨 设计系统
- 统一的设计令牌（Design Tokens）
- 响应式布局
- 移动端优化
- 加载状态和错误处理

## 🛠 技术栈

- **前端框架**：微信小程序原生开发
- **数据存储**：本地模拟数据（Mock Data）
- **架构模式**：服务层模式（Service Layer Pattern）
- **样式系统**：CSS 自定义属性（CSS Custom Properties）
- **状态管理**：页面级状态管理

## 📱 **底部导航栏配置**

**TabBar页面**（可直接通过底部导航访问）：
- 🏠 **首页** - 数据统计、快捷操作、待处理事项
- 📝 **订单** - 订单列表和管理（原"下单"改为"订单"）
- 🏭 **工单** - 工单列表和管理
- 📋 **包装** - 包装任务管理
- 📦 **出入库** - 库存出入库操作

**非TabBar页面**（需要通过页面跳转访问）：
- 订单创建（4步骤流程）、订单详情
- 客户管理、产品管理、包装管理
- 工单创建、工单详情
- 库存列表

## 项目结构

```
miniprogram-erp/
├── app.js                 # 小程序入口文件
├── app.json              # 小程序配置文件
├── app.wxss              # 全局样式文件
├── project.config.json   # 项目配置文件
├── sitemap.json          # 站点地图配置
├── 
├── pages/                # 页面目录
│   ├── index/            # 首页
│   ├── order/            # 订单相关页面
│   │   ├── list/         # 订单列表
│   │   ├── detail/       # 订单详情
│   │   └── create/       # 创建订单
│   ├── workorder/        # 工单相关页面
│   ├── inventory/        # 库存相关页面
│   └── packaging/        # 包装相关页面
├── 
├── styles/               # 样式目录
│   └── design-tokens.wxss # 设计Token系统
├── 
├── utils/                # 工具函数
│   ├── dateUtils.js      # 日期处理工具
│   └── common.js         # 通用工具函数
├── 
├── services/             # 数据服务
│   └── orderService.js   # 订单服务 (Mock数据)
├──
├── images/               # 图片资源 (暂时为空，TabBar使用文字导航)
└── 
└── docs/                 # 项目文档
    ├── UniApp转纯小程序转换计划.md
    └── 转换执行步骤.md
```

## 核心功能模块

### 1. 首页 (pages/index)
- **数据概览**: 订单统计、工单进度
- **快捷操作**: 新建订单、创建工单、库存查询
- **待处理事项**: 智能提醒和任务列表
- **最近订单**: 最新订单快速访问

### 2. 订单管理 (pages/order)
- **订单列表**: 搜索、筛选、分页加载
- **订单详情**: 完整订单信息展示
- **订单创建**: 表单化订单录入
- **状态管理**: 订单生命周期跟踪

### 3. 工单管理 (pages/workorder)
- **工单列表**: 生产工单概览
- **工单详情**: 工单进度和任务详情
- **工单创建**: 基于订单创建生产工单
- **进度跟踪**: 实时生产进度更新

### 4. 库存管理 (pages/inventory)
- **库存查询**: 产品库存实时查看
- **预警系统**: 库存不足自动提醒
- **状态分类**: 充足/预警/不足状态管理

## 设计系统

### 色彩规范
```css
/* 主色调 */
--primary-color: #007AFF;      /* 主品牌色 */
--success-color: #34C759;      /* 成功色 */
--warning-color: #FF9500;      /* 警告色 */
--error-color: #FF3B30;        /* 错误色 */

/* 文本色彩 */
--text-primary: #1D1D1F;       /* 主要文本 */
--text-secondary: #3C3C43;     /* 次要文本 */
--text-tertiary: #8E8E93;      /* 辅助文本 */

/* 背景色彩 */
--bg-primary: #FFFFFF;         /* 主背景 */
--bg-secondary: #F8F9FA;       /* 次背景 */
--bg-page: #FAFAFA;           /* 页面背景 */
```

### 字体系统
- **字体大小**: 20rpx - 48rpx 的层级化字体规范
- **字体粗细**: 400 (常规) / 500 (中等) / 600 (半粗) / 700 (粗体)
- **行高**: 1.2 (紧凑) / 1.4 (常规) / 1.6 (宽松)

### 间距系统
- **基础间距**: 8rpx 的倍数 (8, 12, 16, 20, 24, 32, 40, 48rpx)
- **组件间距**: 统一的内外边距规范
- **页面布局**: 标准化的页面边距和内容区域

## 开发指南

### 环境要求
- 微信开发者工具 (最新稳定版)
- 微信小程序基础库 2.19.4+
- Node.js 14+ (用于开发工具)

### 快速开始

1. **导入项目**
   ```bash
   # 使用微信开发者工具导入项目
   # 项目目录: miniprogram-erp/
   # AppID: 使用测试号或申请正式AppID
   ```

2. **配置项目**
   - 修改 `project.config.json` 中的 `appid`
   - 检查 `app.json` 中的页面路径配置
   - 确认 `sitemap.json` 的索引规则

3. **开发调试**
   - 在微信开发者工具中打开项目
   - 使用模拟器或真机预览
   - 查看控制台日志进行调试

### 代码规范

#### 文件命名
- 页面文件: 使用小写字母和连字符 (kebab-case)
- 工具函数: 使用驼峰命名 (camelCase)
- 常量: 使用大写字母和下划线 (UPPER_SNAKE_CASE)

#### 代码风格
```javascript
// 使用 const/let 而不是 var
const orderService = require('../../services/orderService')

// 函数命名使用动词开头
function loadOrderList() { }
function handleOrderClick() { }

// 事件处理函数使用 on 前缀
onLoad() { }
onShow() { }
onOrderTap() { }
```

#### 样式规范
```css
/* 使用设计Token变量 */
.order-card {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
}

/* 类名使用BEM命名法 */
.order-list { }
.order-list__item { }
.order-list__item--active { }
```

## 数据服务

### Mock数据结构
当前使用Mock数据进行开发，主要数据结构包括：

#### 订单数据 (Order)
```javascript
{
  id: 'order_001',
  orderNo: 'PO20240101001',
  customerName: '客户名称',
  customerContact: '联系人',
  customerPhone: '联系电话',
  productName: '产品名称',
  productCode: '产品编码',
  specification: '规格说明',
  quantity: 100,
  unit: '件',
  unitPrice: 50.00,
  totalAmount: 5000.00,
  deliveryDate: '2024-02-15',
  status: 'confirmed',
  priority: 'normal',
  notes: '备注信息',
  createdAt: '2024-01-01T08:00:00.000Z',
  updatedAt: '2024-01-02T10:30:00.000Z'
}
```

#### 工单数据 (WorkOrder)
```javascript
{
  id: 'wo_001',
  workOrderNo: 'WO20240101001',
  orderNo: 'PO20240101001',
  productName: '产品名称',
  status: 'in_progress',
  completedTasks: 2,
  totalTasks: 5,
  progressPercent: 40
}
```

#### 库存数据 (Inventory)
```javascript
{
  id: 'inv_001',
  productName: '产品名称',
  productCode: '产品编码',
  currentStock: 150,
  safetyStock: 100,
  unit: '件',
  status: 'sufficient',
  lastUpdateTime: '2024-01-15 14:30'
}
```

### 服务接口
所有数据服务都返回统一的响应格式：

```javascript
// 成功响应
{
  success: true,
  data: [...],
  message: '操作成功'
}

// 错误响应
{
  success: false,
  message: '错误信息',
  code: 'ERROR_CODE'
}
```

## 部署说明

### 开发环境
- 使用微信开发者工具进行本地开发
- Mock数据支持完整的业务流程测试
- 支持热重载和实时调试

### 生产环境
1. **代码审核**: 确保代码质量和性能
2. **资源优化**: 压缩图片和代码文件
3. **版本管理**: 更新版本号和更新日志
4. **小程序发布**: 通过微信公众平台发布

## 性能优化

### 代码优化
- **按需加载**: 页面级别的代码分割
- **图片优化**: 使用WebP格式和适当尺寸
- **缓存策略**: 合理使用本地存储和缓存

### 用户体验优化
- **加载状态**: 所有异步操作都有加载提示
- **错误处理**: 友好的错误信息和重试机制
- **离线支持**: 关键数据的本地缓存

## 后续规划

### 短期目标 (1-2周)
- [ ] 完善订单详情页面
- [ ] 实现订单创建功能
- [ ] 添加工单详情和创建功能
- [ ] 优化用户交互体验

### 中期目标 (1个月)
- [ ] 集成真实的后端API
- [ ] 添加用户认证和权限管理
- [ ] 实现数据同步和离线支持
- [ ] 添加更多业务功能模块

### 长期目标 (3个月)
- [ ] 完整的ERP功能覆盖
- [ ] 高级数据分析和报表
- [ ] 多角色权限管理
- [ ] 系统集成和API开放

## 技术支持

### 问题反馈
如遇到技术问题，请提供以下信息：
- 微信开发者工具版本
- 基础库版本
- 具体错误信息和复现步骤
- 设备型号和系统版本

### 开发文档
- [微信小程序官方文档](https://developers.weixin.qq.com/miniprogram/dev/framework/)
- [小程序设计指南](https://developers.weixin.qq.com/miniprogram/design/)
- [小程序开发最佳实践](https://developers.weixin.qq.com/miniprogram/dev/framework/performance/)

## 更新日志

### v1.0.0 (2024-01-15)
- ✅ 完成UniApp到纯小程序的转换
- ✅ 实现基础项目结构和配置
- ✅ 完成首页和订单列表功能
- ✅ 建立完整的设计系统
- ✅ 实现Mock数据服务
- ✅ 添加工单和库存管理基础页面

---

**开发团队**: ERP项目组  
**最后更新**: 2024年1月15日  
**项目状态**: 开发中 🚧
