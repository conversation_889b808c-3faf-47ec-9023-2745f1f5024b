<!--生产管理系统 - 包装管理页面-->
<view class="page-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <button class="btn btn-primary" bindtap="showAddPackaging">
      <text class="btn-icon">➕</text>
      <text class="btn-text">新建包装</text>
    </button>
  </view>

  <!-- 包装列表 -->
  <view class="packaging-list" wx:if="{{ packagingList.length > 0 }}">
    <view 
      class="packaging-card"
      wx:for="{{ packagingList }}"
      wx:key="id"
    >
      <!-- 包装信息 -->
      <view class="packaging-info">
        <view class="packaging-header">
          <view class="packaging-name">{{ item.name }}</view>
          <view class="packaging-type">{{ item.type }}</view>
        </view>
        
        <view class="packaging-description" wx:if="{{ item.description }}">
          <text class="description-text">{{ item.description }}</text>
        </view>
        
        <view class="packaging-specifications" wx:if="{{ item.specifications }}">
          <view class="spec-title">规格说明：</view>
          <text class="spec-text">{{ item.specifications }}</text>
        </view>
        
        <view class="packaging-notes" wx:if="{{ item.notes }}">
          <text class="notes-text">{{ item.notes }}</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="packaging-actions">
        <button 
          class="action-btn secondary"
          bindtap="editPackaging"
          data-packaging="{{ item }}"
        >
          编辑
        </button>
        <button 
          class="action-btn danger"
          bindtap="deletePackaging"
          data-packaging="{{ item }}"
        >
          删除
        </button>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:else>
    <text class="empty-icon">📋</text>
    <text class="empty-text">暂无包装信息</text>
    <text class="empty-hint">点击上方按钮添加包装</text>
  </view>

  <!-- 添加/编辑表单弹窗 -->
  <view class="form-overlay" wx:if="{{ showAddForm }}" bindtap="hideForm">
    <view class="form-container" catchtap="true">
      <view class="form-header">
        <text class="form-title">{{ editingId ? '编辑包装' : '新建包装' }}</text>
        <button class="close-btn" bindtap="hideForm">✕</button>
      </view>

      <view class="form-content">
        <view class="form-group">
          <text class="form-label">包装名称 *</text>
          <input 
            class="form-input"
            value="{{ formData.name }}"
            placeholder="请输入包装名称"
            data-field="name"
            bindinput="onFormInput"
          />
        </view>

        <view class="form-group">
          <text class="form-label">包装类型 *</text>
          <picker 
            class="form-picker"
            mode="selector"
            range="{{ typeOptions }}"
            range-key="label"
            value="{{ formData.type }}"
            bindchange="onPickerChange"
          >
            <view class="picker-display">
              {{ formData.type || '请选择包装类型' }}
            </view>
          </picker>
        </view>

        <view class="form-group">
          <text class="form-label">包装描述</text>
          <textarea 
            class="form-textarea"
            value="{{ formData.description }}"
            placeholder="请输入包装描述"
            data-field="description"
            bindinput="onFormInput"
          />
        </view>

        <view class="form-group">
          <text class="form-label">规格说明</text>
          <textarea 
            class="form-textarea"
            value="{{ formData.specifications }}"
            placeholder="请输入规格说明，如尺寸、材质、承重等"
            data-field="specifications"
            bindinput="onFormInput"
          />
        </view>

        <view class="form-group">
          <text class="form-label">备注信息</text>
          <textarea 
            class="form-textarea"
            value="{{ formData.notes }}"
            placeholder="请输入备注信息"
            data-field="notes"
            bindinput="onFormInput"
          />
        </view>
      </view>

      <view class="form-actions">
        <button class="btn btn-secondary" bindtap="hideForm">取消</button>
        <button class="btn btn-primary" bindtap="savePackaging" disabled="{{ loading }}">
          {{ loading ? '保存中...' : '保存' }}
        </button>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{ loading }}">
    <view class="loading-content">
      <text class="loading-text">处理中...</text>
    </view>
  </view>
</view>
