/**
 * 订单服务 - 重构版本
 * 基于BaseService实现，提供统一的订单管理功能
 */

const BaseService = require('./base/BaseService')
const mockDataManager = require('./base/MockDataManager')

class OrderService extends BaseService {
  constructor() {
    super('order')
    this.initMockData()
  }

  /**
   * 初始化Mock数据到本地存储
   */
  initMockData() {
    const existingData = this.getDataFromStorage()
    if (existingData.length === 0) {
      const mockOrders = mockDataManager.getData('orders')
      this.saveDataToStorage(mockOrders)
    }
  }

  /**
   * 重写关键词匹配方法，适配订单特有字段
   * @param {Object} order 订单对象
   * @param {string} keyword 关键词
   * @returns {boolean} 是否匹配
   */
  matchKeyword(order, keyword) {
    const searchFields = [
      'orderNo', 'customerName', 'customerContact', 'customerPhone',
      'productName', 'productCode', 'specification', 'notes'
    ]
    
    return searchFields.some(field => {
      const value = order[field]
      return value && value.toString().toLowerCase().includes(keyword)
    })
  }

  /**
   * 获取订单列表（重写基类方法以添加特殊处理）
   * @param {Object} params 查询参数
   * @returns {Promise} 订单列表
   */
  async getOrderList(params = {}) {
    return this.simulateAsync(() => {
      const data = this.getDataFromStorage()
      
      // 使用基类的过滤方法
      let filteredData = this.filterData(data, params)
      
      // 订单特有的过滤逻辑
      if (params.priority && params.priority !== 'all') {
        filteredData = filteredData.filter(order => order.priority === params.priority)
      }
      
      if (params.customerId) {
        filteredData = filteredData.filter(order => order.customerId === params.customerId)
      }
      
      if (params.productId) {
        filteredData = filteredData.filter(order => order.productId === params.productId)
      }
      
      // 排序
      const sortedData = this.sortData(filteredData, params.sortBy || 'createdAt', params.sortOrder || 'desc')
      
      // 分页
      const result = this.paginateData(sortedData, params.page, params.pageSize)
      
      return this.createResponse(true, {
        list: result.items,
        hasMore: result.pagination.hasMore,
        total: result.pagination.total,
        page: result.pagination.page,
        pageSize: result.pagination.pageSize
      })
    })
  }

  /**
   * 获取订单详情
   * @param {string} orderId 订单ID
   * @returns {Promise} 订单详情
   */
  async getOrderDetail(orderId) {
    return this.getById(orderId)
  }

  /**
   * 创建订单
   * @param {Object} orderData 订单数据
   * @returns {Promise} 创建结果
   */
  async createOrder(orderData) {
    const validationRules = {
      customerName: {
        required: true,
        label: '客户名称',
        maxLength: 100
      },
      customerContact: {
        required: true,
        label: '联系人',
        maxLength: 50
      },
      customerPhone: {
        required: true,
        label: '联系电话',
        pattern: /^1[3-9]\d{9}$/
      },
      productName: {
        required: true,
        label: '产品名称',
        maxLength: 100
      },
      productCode: {
        required: true,
        label: '产品编码',
        maxLength: 50
      },
      quantity: {
        required: true,
        label: '数量',
        type: 'number',
        validator: (value) => value > 0 || '数量必须大于0'
      },
      deliveryDate: {
        required: true,
        label: '交货日期',
        validator: (value) => {
          const deliveryDate = new Date(value)
          const today = new Date()
          today.setHours(0, 0, 0, 0)
          return deliveryDate >= today || '交货日期不能早于今天'
        }
      }
    }

    // 生成订单号
    const orderNo = this.generateOrderNo()
    
    const newOrderData = {
      ...orderData,
      orderNo,
      status: orderData.status || 'pending',
      priority: orderData.priority || 'normal'
    }

    return this.create(newOrderData, validationRules)
  }

  /**
   * 更新订单
   * @param {string} orderId 订单ID
   * @param {Object} updateData 更新数据
   * @returns {Promise} 更新结果
   */
  async updateOrder(orderId, updateData) {
    const validationRules = {
      customerName: {
        label: '客户名称',
        maxLength: 100
      },
      customerContact: {
        label: '联系人',
        maxLength: 50
      },
      customerPhone: {
        label: '联系电话',
        pattern: /^1[3-9]\d{9}$/
      },
      productName: {
        label: '产品名称',
        maxLength: 100
      },
      quantity: {
        label: '数量',
        type: 'number',
        validator: (value) => !value || value > 0 || '数量必须大于0'
      },
      deliveryDate: {
        label: '交货日期',
        validator: (value) => {
          if (!value) return true
          const deliveryDate = new Date(value)
          const today = new Date()
          today.setHours(0, 0, 0, 0)
          return deliveryDate >= today || '交货日期不能早于今天'
        }
      }
    }

    return this.update(orderId, updateData, validationRules)
  }

  /**
   * 删除订单
   * @param {string} orderId 订单ID
   * @returns {Promise} 删除结果
   */
  async deleteOrder(orderId) {
    return this.simulateAsync(() => {
      // 检查订单状态，某些状态下不允许删除
      const orderResult = this.getById(orderId)
      if (!orderResult.success) {
        return orderResult
      }

      const order = orderResult.data
      if (['processing', 'completed'].includes(order.status)) {
        return this.createResponse(false, null, '该状态的订单不允许删除', 'STATUS_NOT_ALLOWED')
      }

      return this.delete(orderId)
    })
  }

  /**
   * 更新订单状态
   * @param {string} orderId 订单ID
   * @param {string} status 新状态
   * @returns {Promise} 更新结果
   */
  async updateOrderStatus(orderId, status) {
    const validStatuses = ['pending', 'confirmed', 'processing', 'completed', 'cancelled']
    
    if (!validStatuses.includes(status)) {
      return this.createResponse(false, null, '无效的订单状态', 'INVALID_STATUS')
    }

    return this.update(orderId, { status })
  }

  /**
   * 获取订单统计信息
   * @returns {Promise} 统计信息
   */
  async getOrderStats() {
    return this.simulateAsync(() => {
      const data = this.getDataFromStorage()
      const today = new Date().toISOString().split('T')[0]
      
      const stats = {
        total: data.length,
        pending: data.filter(order => order.status === 'pending').length,
        confirmed: data.filter(order => order.status === 'confirmed').length,
        processing: data.filter(order => order.status === 'processing').length,
        completed: data.filter(order => order.status === 'completed').length,
        cancelled: data.filter(order => order.status === 'cancelled').length,
        todayNew: data.filter(order => order.createdAt.startsWith(today)).length,
        urgent: data.filter(order => order.priority === 'urgent').length,
        high: data.filter(order => order.priority === 'high').length
      }

      return this.createResponse(true, stats)
    })
  }

  /**
   * 获取最近订单
   * @param {number} limit 数量限制
   * @returns {Promise} 最近订单列表
   */
  async getRecentOrders(limit = 5) {
    return this.simulateAsync(() => {
      const data = this.getDataFromStorage()
      const sortedData = this.sortData(data, 'createdAt', 'desc')
      const recentOrders = sortedData.slice(0, limit)
      
      return this.createResponse(true, recentOrders)
    })
  }

  /**
   * 生成订单号
   * @returns {string} 订单号
   */
  generateOrderNo() {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const timestamp = now.getTime().toString().slice(-6)
    
    return `PO${year}${month}${day}${timestamp}`
  }

  /**
   * 批量更新订单状态
   * @param {Array} orderIds 订单ID数组
   * @param {string} status 新状态
   * @returns {Promise} 更新结果
   */
  async batchUpdateStatus(orderIds, status) {
    return this.simulateAsync(() => {
      const data = this.getDataFromStorage()
      let updatedCount = 0
      
      orderIds.forEach(orderId => {
        const index = data.findIndex(order => order.id === orderId)
        if (index !== -1) {
          data[index].status = status
          data[index].updatedAt = new Date().toISOString()
          updatedCount++
        }
      })
      
      if (this.saveDataToStorage(data)) {
        return this.createResponse(true, { updatedCount }, `成功更新${updatedCount}个订单`)
      } else {
        return this.createResponse(false, null, '批量更新失败', 'BATCH_UPDATE_ERROR')
      }
    })
  }

  /**
   * 搜索订单（兼容旧接口）
   * @param {Object} params 搜索参数
   * @returns {Promise} 搜索结果
   */
  async searchOrders(params) {
    return this.getOrderList(params)
  }
}

// 创建单例实例
const orderService = new OrderService()

module.exports = orderService
