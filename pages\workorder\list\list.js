// 工单列表页面逻辑
Page({
  data: {
    workOrderList: [
      {
        id: 'wo_001',
        workOrderNo: 'GD2025070001',
        productCode: 'CP20240006',
        productName: '铝锭',
        material: 'ADC12',
        plannedQuantity: 1000,
        completedQuantity: 0,
        pendingQuantity: 1000,
        progressPercent: 0,
        plannedStartTime: '2025-07-31 00:00',
        plannedEndTime: '2025-07-31 23:59',
        description: '',
        processes: [
          {
            id: 'process_001',
            name: '压铸',
            status: 'completed',
            progress: 0,
            taskCount: 4
          },
          {
            id: 'process_002',
            name: '去毛刺',
            status: 'pending',
            progress: 0,
            taskCount: 0
          },
          {
            id: 'process_003',
            name: '包装入库',
            status: 'pending',
            progress: 0,
            taskCount: 0
          }
        ]
      },
      {
        id: 'wo_002',
        workOrderNo: 'GD2025070002',
        productCode: 'CP20240007',
        productName: '精密齿轮',
        material: 'A380',
        plannedQuantity: 500,
        completedQuantity: 200,
        pendingQuantity: 300,
        progressPercent: 40,
        plannedStartTime: '2025-08-01 08:00',
        plannedEndTime: '2025-08-01 18:00',
        description: '高精度要求',
        processes: [
          {
            id: 'process_004',
            name: '压铸',
            status: 'completed',
            progress: 100,
            taskCount: 0
          },
          {
            id: 'process_005',
            name: '去毛刺',
            status: 'completed',
            progress: 40,
            taskCount: 2
          },
          {
            id: 'process_006',
            name: '包装入库',
            status: 'pending',
            progress: 0,
            taskCount: 0
          }
        ]
      }
    ],

    // 快速操作弹窗状态
    showQuickActionModal: false,
    selectedWorkOrder: null,
    selectedPriority: 'none', // 默认选中"无"
    selectedStatus: 'executing', // 默认选中"执行中"
    syncToTasks: false // 默认不选中同步更新
  },

  onLoad() {
    console.log('工单列表页面加载')
  },

  onShow() {
    console.log('工单列表页面显示')
  },

  /**
   * 创建工单
   */
  createWorkOrder() {
    wx.navigateTo({
      url: '/pages/workorder/create/create'
    })
  },

  /**
   * 查看工单详情
   */
  viewWorkOrderDetail(e) {
    const workOrder = e.currentTarget.dataset.workorder
    console.log('点击工单卡片:', workOrder)

    if (!workOrder || !workOrder.id) {
      console.error('工单数据异常:', workOrder)
      wx.showToast({
        title: '工单数据异常',
        icon: 'error'
      })
      return
    }

    console.log('跳转到工单详情页面，工单ID:', workOrder.id)
    wx.navigateTo({
      url: `/pages/workorder/detail/detail?id=${workOrder.id}`
    })
  },

  /**
   * 显示快速操作弹窗
   */
  showQuickActionModal(e) {
    const workOrder = e.currentTarget.dataset.workorder
    console.log('显示快速操作弹窗，工单:', workOrder)

    this.setData({
      showQuickActionModal: true,
      selectedWorkOrder: workOrder,
      selectedPriority: 'none', // 重置为默认值
      selectedStatus: 'executing', // 重置为默认值
      syncToTasks: false // 重置为默认值
    })
  },

  /**
   * 隐藏快速操作弹窗
   */
  hideQuickActionModal() {
    this.setData({
      showQuickActionModal: false,
      selectedWorkOrder: null
    })
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止点击弹窗内容区域时关闭弹窗
  },

  /**
   * 选择优先级
   */
  selectPriority(e) {
    const priority = e.currentTarget.dataset.priority
    this.setData({
      selectedPriority: priority
    })
  },

  /**
   * 选择执行状态
   */
  selectStatus(e) {
    const status = e.currentTarget.dataset.status
    this.setData({
      selectedStatus: status
    })
  },

  /**
   * 切换同步更新至任务
   */
  toggleSyncToTasks() {
    this.setData({
      syncToTasks: !this.data.syncToTasks
    })
  },

  /**
   * 确认快速操作
   */
  confirmQuickAction() {
    const { selectedWorkOrder, selectedPriority, selectedStatus, syncToTasks } = this.data

    console.log('确认快速操作:', {
      workOrder: selectedWorkOrder?.workOrderNo,
      priority: selectedPriority,
      status: selectedStatus,
      syncToTasks: syncToTasks
    })

    // 这里可以调用API更新工单状态
    wx.showToast({
      title: '操作成功',
      icon: 'success'
    })

    // 关闭弹窗
    this.hideQuickActionModal()

    // 刷新列表
    // this.loadWorkOrderList()
  }
})
