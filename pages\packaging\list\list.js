// 任务管理页面逻辑
const TaskService = require('../../../services/taskService.js')
const { formatDate } = require('../../../utils/dateUtils.js')
const { getStatusText, debounce } = require('../../../utils/common.js')

Page({
  data: {
    // 搜索和筛选
    searchKeyword: '',
    activeStatus: 'all',
    currentEmployee: 'all',
    statusOptions: [
      { label: '全部', value: 'all' },
      { label: '待开始', value: 'pending' },
      { label: '进行中', value: 'in_progress' },
      { label: '已完成', value: 'completed' },
      { label: '已暂停', value: 'paused' }
    ],

    // 员工选项
    employeeOptions: [
      { label: '全部员工', value: 'all' }
    ],

    // 列表数据
    taskList: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,

    // UI状态
    showFilterModal: false,
    showAssignModal: false,
    selectedTask: null,
    selectedEmployeeIndex: -1
  },

  onLoad(options) {
    console.log('任务管理页面加载', options)
    this.initPage()
  },

  onShow() {
    // 从其他页面返回时刷新数据
    this.refreshTaskList()
  },

  onPullDownRefresh() {
    this.refreshTaskList()
  },

  onReachBottom() {
    this.loadMoreTasks()
  },

  /**
   * 初始化页面
   */
  async initPage() {
    await this.loadEmployeeOptions()
    await this.loadTaskList()
  },

  /**
   * 加载员工选项
   */
  async loadEmployeeOptions() {
    try {
      const result = await TaskService.getEmployeeList()
      if (result.success) {
        const employeeOptions = [
          { label: '全部员工', value: 'all' },
          ...result.data.map(emp => ({
            label: emp.name,
            value: emp.id
          }))
        ]
        this.setData({ employeeOptions })
      }
    } catch (error) {
      console.error('加载员工选项失败:', error)
    }
  },

  /**
   * 加载任务列表
   */
  async loadTaskList() {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      const params = {
        page: this.data.page,
        pageSize: this.data.pageSize,
        status: this.data.activeStatus,
        employeeId: this.data.currentEmployee === 'all' ? null : this.data.currentEmployee,
        keyword: this.data.searchKeyword
      }

      const result = await TaskService.getTaskList(params)

      if (result.success) {
        const formattedTasks = result.data.map(task => this.formatTaskForDisplay(task))

        console.log('原始任务数据:', result.data.map(t => ({ id: t.id, name: t.processName })))
        console.log('格式化后任务数据:', formattedTasks.map(t => ({ id: t.id, name: t.processName })))

        this.setData({
          taskList: this.data.page === 1 ? formattedTasks : [...this.data.taskList, ...formattedTasks],
          hasMore: result.hasMore
        })

        console.log('任务列表加载成功，共', formattedTasks.length, '个任务')
      } else {
        wx.showToast({
          title: result.message || '加载失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('加载任务列表失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false, refreshing: false })
      wx.stopPullDownRefresh()
    }
  },

  /**
   * 刷新任务列表
   */
  async refreshTaskList() {
    this.setData({
      page: 1,
      refreshing: true,
      hasMore: true
    })
    await this.loadTaskList()
  },

  /**
   * 加载更多任务
   */
  async loadMoreTasks() {
    if (!this.data.hasMore || this.data.loading) return

    this.setData({
      page: this.data.page + 1
    })
    await this.loadTaskList()
  },

  /**
   * 格式化任务数据用于显示
   */
  formatTaskForDisplay(task) {
    const statusMap = {
      'pending': '待开始',
      'in_progress': '进行中',
      'completed': '已完成',
      'paused': '已暂停'
    }

    return {
      ...task,
      statusText: statusMap[task.status] || task.status,
      progressText: `${task.progress}%`,
      assignedText: task.assignedEmployee ? task.assignedEmployee.name : '待领取',
      plannedStartTimeText: formatDate(task.plannedStartTime, 'MM-DD HH:mm'),
      plannedEndTimeText: formatDate(task.plannedEndTime, 'MM-DD HH:mm'),
      processedRatio: task.plannedQuantity > 0 ? (task.processedQuantity / task.plannedQuantity * 100).toFixed(1) : 0
    }
  },

  /**
   * 搜索输入
   */
  onSearchInput: debounce(function(e) {
    this.setData({ searchKeyword: e.detail.value })
    this.refreshTaskList()
  }, 500),

  /**
   * 搜索确认
   */
  onSearchConfirm() {
    this.refreshTaskList()
  },

  /**
   * 清除搜索
   */
  clearSearch() {
    this.setData({ searchKeyword: '' })
    this.refreshTaskList()
  },

  /**
   * 显示筛选弹窗
   */
  showFilterModal() {
    this.setData({ showFilterModal: true })
  },

  /**
   * 隐藏筛选弹窗
   */
  hideFilterModal() {
    this.setData({ showFilterModal: false })
  },

  /**
   * 状态筛选标签点击
   */
  onStatusChange(e) {
    const status = e.currentTarget.dataset.status
    this.setData({ activeStatus: status })
    this.refreshTaskList()
  },

  /**
   * 筛选弹窗中的状态选择
   */
  onFilterStatusChange(e) {
    const status = e.currentTarget.dataset.status
    this.setData({ activeStatus: status })
  },

  /**
   * 员工筛选
   */
  onEmployeeChange(e) {
    const employee = e.currentTarget.dataset.employee
    this.setData({ currentEmployee: employee })
  },

  /**
   * 重置筛选
   */
  resetFilter() {
    this.setData({
      activeStatus: 'all',
      currentEmployee: 'all'
    })
  },

  /**
   * 应用筛选
   */
  applyFilter() {
    this.hideFilterModal()
    this.refreshTaskList()
  },

  /**
   * 查看任务详情
   */
  viewTaskDetail(e) {
    const task = e.currentTarget.dataset.task
    console.log('点击任务卡片，完整任务数据:', task)
    console.log('任务ID:', task?.id, '类型:', typeof task?.id)

    if (!task || !task.id) {
      console.error('任务数据异常:', task)
      wx.showToast({
        title: '任务数据异常',
        icon: 'error'
      })
      return
    }

    console.log('跳转到任务详情页面，任务ID:', task.id)
    wx.navigateTo({
      url: `/pages/packaging/detail/detail?id=${task.id}`
    })
  },

  /**
   * 测试跳转（用于调试）
   */
  testNavigation() {
    console.log('测试跳转到第一个任务')
    if (this.data.taskList.length > 0) {
      const firstTask = this.data.taskList[0]
      console.log('第一个任务数据:', firstTask)
      wx.navigateTo({
        url: `/pages/packaging/detail/detail?id=${firstTask.id}`
      })
    } else {
      console.log('任务列表为空')
    }
  },

  /**
   * 显示任务分配弹窗
   */
  showAssignModal(e) {
    const task = e.currentTarget.dataset.task
    this.setData({
      showAssignModal: true,
      selectedTask: task,
      selectedEmployeeIndex: -1
    })
  },

  /**
   * 隐藏任务分配弹窗
   */
  hideAssignModal() {
    this.setData({
      showAssignModal: false,
      selectedTask: null,
      selectedEmployeeIndex: -1
    })
  },

  /**
   * 选择分配员工
   */
  onAssignEmployeeChange(e) {
    this.setData({ selectedEmployeeIndex: e.detail.value })
  },

  /**
   * 确认分配任务
   */
  async confirmAssignTask() {
    const { selectedTask, selectedEmployeeIndex, employeeOptions } = this.data

    if (selectedEmployeeIndex === -1 || selectedEmployeeIndex === 0) {
      wx.showToast({
        title: '请选择员工',
        icon: 'none'
      })
      return
    }

    const employee = employeeOptions[selectedEmployeeIndex]

    try {
      const result = await TaskService.assignTask(selectedTask.id, employee.value)

      if (result.success) {
        wx.showToast({
          title: '分配成功',
          icon: 'success'
        })

        this.hideAssignModal()
        this.refreshTaskList()
      } else {
        wx.showToast({
          title: result.message || '分配失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('分配任务失败:', error)
      wx.showToast({
        title: '分配失败，请重试',
        icon: 'none'
      })
    }
  },

  /**
   * 快速操作：领取任务
   */
  async claimTask(e) {
    const task = e.currentTarget.dataset.task

    // 这里假设当前用户是系统管理员
    const currentUserId = 'employee_001'

    try {
      const result = await TaskService.assignTask(task.id, currentUserId)

      if (result.success) {
        wx.showToast({
          title: '领取成功',
          icon: 'success'
        })

        this.refreshTaskList()
      } else {
        wx.showToast({
          title: result.message || '领取失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('领取任务失败:', error)
      wx.showToast({
        title: '领取失败，请重试',
        icon: 'none'
      })
    }
  }
})
