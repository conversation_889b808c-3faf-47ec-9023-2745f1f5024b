/* 订单创建第四步样式 */
@import "../step1/step1.wxss";

/* 预览容器 */
.preview-container {
  flex: 1;
  padding: 0 20rpx;
  margin-bottom: 20rpx;
}

.preview-content {
  height: 100%;
}

.preview-section {
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

/* 区域头部 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1D1D1F;
}

.edit-btn {
  padding: 8rpx 20rpx;
  background: #E3F2FD !important;
  color: #007AFF !important;
  border: 1rpx solid #007AFF !important;
  border-radius: 20rpx;
  font-size: 24rpx;
}

/* 信息网格 */
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-label {
  font-size: 24rpx;
  color: #8E8E93;
}

.info-value {
  font-size: 28rpx;
  color: #1D1D1F;
  font-weight: 500;
}

.info-value.priority-urgent {
  color: #FF9500;
}

.info-value.priority-emergency {
  color: #FF3B30;
}

.info-value.priority-trial {
  color: #5856D6;
}

/* 产品列表 */
.product-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.product-preview {
  border: 2rpx solid #F0F0F0;
  border-radius: 12rpx;
  padding: 24rpx;
}

.product-main {
  margin-bottom: 20rpx;
}

.product-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 12rpx;
}

.product-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #1D1D1F;
}

.product-code {
  font-size: 24rpx;
  color: #8E8E93;
  background: #F8F9FA;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}

.product-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.product-spec {
  font-size: 26rpx;
  color: #3C3C43;
}

.product-meta {
  display: flex;
  gap: 20rpx;
  flex-wrap: wrap;
}

.meta-item {
  font-size: 24rpx;
  color: #8E8E93;
}

.meta-item.total {
  color: #007AFF;
  font-weight: 600;
}

/* 产品配置 */
.product-config {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #F0F0F0;
}

.config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-label {
  font-size: 24rpx;
  color: #8E8E93;
}

.config-value {
  font-size: 24rpx;
  color: #1D1D1F;
}

/* 汇总网格 */
.summary-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24rpx;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 8rpx;
}

.summary-label {
  font-size: 24rpx;
  color: #8E8E93;
}

.summary-value {
  font-size: 32rpx;
  color: #1D1D1F;
  font-weight: 600;
}

.summary-value.amount {
  color: #007AFF;
}

/* 工单选项 */
.workorder-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.checkbox-text {
  font-size: 28rpx;
  color: #1D1D1F;
}

.option-desc {
  padding-left: 56rpx;
}

.desc-text {
  font-size: 24rpx;
  color: #8E8E93;
  line-height: 1.5;
}

/* 成功弹窗 */
.success-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.modal-content {
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin: 40rpx;
  text-align: center;
  max-width: 600rpx;
}

.success-icon {
  width: 120rpx;
  height: 120rpx;
  background: #34C759;
  color: #FFFFFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  font-weight: bold;
  margin: 0 auto 32rpx;
}

.success-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #1D1D1F;
  margin-bottom: 16rpx;
}

.success-desc {
  display: block;
  font-size: 28rpx;
  color: #8E8E93;
  margin-bottom: 40rpx;
}

.success-actions {
  display: flex;
  gap: 16rpx;
}

.success-btn {
  flex: 1;
  height: 88rpx;
  background: #F8F9FA !important;
  color: #1D1D1F !important;
  border: 2rpx solid #F0F0F0 !important;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.success-btn.primary {
  background: #007AFF !important;
  color: #FFFFFF !important;
  border-color: #007AFF !important;
}
