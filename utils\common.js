/**
 * 通用工具函数
 */

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} wait 等待时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} limit 时间间隔（毫秒）
 * @returns {Function} 节流后的函数
 */
function throttle(func, limit) {
  let inThrottle
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 深拷贝对象
 * @param {any} obj 要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime())
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item))
  }
  
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (let key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

/**
 * 生成唯一ID
 * @param {string} prefix 前缀
 * @returns {string} 唯一ID
 */
function generateId(prefix = 'id') {
  const timestamp = Date.now().toString(36)
  const random = Math.random().toString(36).substr(2, 5)
  return `${prefix}_${timestamp}_${random}`
}

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @returns {string} 格式化后的文件大小
 */
function formatFileSize(bytes) {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 格式化数字，添加千分位分隔符
 * @param {number} num 数字
 * @returns {string} 格式化后的数字
 */
function formatNumber(num) {
  if (typeof num !== 'number') return num
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

/**
 * 获取状态文本
 * @param {string} status 状态值
 * @param {string} type 状态类型
 * @returns {string} 状态文本
 */
function getStatusText(status, type = 'order') {
  const statusMaps = {
    order: {
      'pending': '待确认',
      'confirmed': '已确认',
      'processing': '生产中',
      'completed': '已完成',
      'cancelled': '已取消'
    },
    workorder: {
      'draft': '草稿',
      'pending': '待开始',
      'in_progress': '进行中',
      'completed': '已完成',
      'cancelled': '已取消'
    },
    inventory: {
      'sufficient': '充足',
      'warning': '预警',
      'shortage': '不足'
    }
  }
  
  return statusMaps[type]?.[status] || status
}

/**
 * 获取优先级文本
 * @param {string} priority 优先级值
 * @returns {string} 优先级文本
 */
function getPriorityText(priority) {
  const priorityMap = {
    'low': '低',
    'normal': '普通',
    'high': '高',
    'urgent': '紧急'
  }
  
  return priorityMap[priority] || priority
}

/**
 * 验证手机号
 * @param {string} phone 手机号
 * @returns {boolean} 是否有效
 */
function validatePhone(phone) {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

/**
 * 验证邮箱
 * @param {string} email 邮箱
 * @returns {boolean} 是否有效
 */
function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 获取数组中的唯一值
 * @param {Array} arr 数组
 * @returns {Array} 去重后的数组
 */
function getUniqueArray(arr) {
  return [...new Set(arr)]
}

/**
 * 安全的JSON解析
 * @param {string} str JSON字符串
 * @param {any} defaultValue 默认值
 * @returns {any} 解析结果
 */
function safeJsonParse(str, defaultValue = null) {
  try {
    return JSON.parse(str)
  } catch (error) {
    console.error('JSON解析失败:', error)
    return defaultValue
  }
}

/**
 * 检查对象是否为空
 * @param {object} obj 对象
 * @returns {boolean} 是否为空
 */
function isEmpty(obj) {
  if (obj == null) return true
  if (Array.isArray(obj) || typeof obj === 'string') return obj.length === 0
  if (typeof obj === 'object') return Object.keys(obj).length === 0
  return false
}

module.exports = {
  debounce,
  throttle,
  deepClone,
  generateId,
  formatFileSize,
  formatNumber,
  getStatusText,
  getPriorityText,
  validatePhone,
  validateEmail,
  getUniqueArray,
  safeJsonParse,
  isEmpty
}
