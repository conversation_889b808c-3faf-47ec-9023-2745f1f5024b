<!--订单创建第一步：基本信息-->
<view class="step-container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <view class="navbar-left" bindtap="goBack">
        <text class="back-icon">‹</text>
        <text class="back-text">返回</text>
      </view>
      <text class="navbar-title">创建订单 - 基本信息</text>
      <view class="navbar-right"></view>
    </view>
  </view>

  <!-- 步骤指示器 -->
  <view class="step-indicator">
    <view class="step-item active">
      <view class="step-number">1</view>
      <text class="step-text">基本信息</text>
    </view>
    <view class="step-line"></view>
    <view class="step-item">
      <view class="step-number">2</view>
      <text class="step-text">选择产品</text>
    </view>
    <view class="step-line"></view>
    <view class="step-item">
      <view class="step-number">3</view>
      <text class="step-text">配置详情</text>
    </view>
    <view class="step-line"></view>
    <view class="step-item">
      <view class="step-number">4</view>
      <text class="step-text">确认提交</text>
    </view>
  </view>

  <!-- 表单内容 -->
  <view class="form-container">
    <!-- 订单信息区域 -->
    <view class="form-section">
      <view class="section-title">订单信息</view>
      
      <view class="form-item">
        <text class="form-label">订单编号</text>
        <input 
          class="form-input"
          value="{{ orderData.orderNo }}"
          placeholder="自动生成"
          disabled
        />
      </view>
      
      <view class="form-item">
        <text class="form-label">要求交货日期 <text class="required">*</text></text>
        <picker
          mode="date"
          value="{{ orderData.deliveryDate }}"
          start="{{ minDate }}"
          bindchange="onDeliveryDateChange"
        >
          <view class="picker-input">
            <text class="picker-text {{ !orderData.deliveryDate ? 'placeholder' : '' }}">
              {{ orderData.deliveryDate || '请选择交货日期' }}
            </text>
            <text class="picker-arrow">📅</text>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="form-label">优先级</text>
        <picker
          range="{{ priorityOptions }}"
          range-key="label"
          value="{{ priorityIndex }}"
          bindchange="onPriorityChange"
        >
          <view class="picker-input">
            <text class="picker-text">{{ priorityOptions[priorityIndex].label }}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 客户信息区域 -->
    <view class="form-section">
      <view class="section-title">客户信息</view>
      
      <view class="form-item">
        <text class="form-label">客户名称 <text class="required">*</text></text>
        <picker
          range="{{ customerOptions }}"
          range-key="name"
          value="{{ customerIndex }}"
          bindchange="onCustomerChange"
        >
          <view class="picker-input">
            <text class="picker-text {{ !selectedCustomer ? 'placeholder' : '' }}">
              {{ selectedCustomer ? selectedCustomer.name : '请选择客户' }}
            </text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>

      <view class="form-item" wx:if="{{ selectedCustomer }}">
        <text class="form-label">联系人</text>
        <input 
          class="form-input"
          value="{{ selectedCustomer.contact }}"
          disabled
        />
      </view>

      <view class="form-item" wx:if="{{ selectedCustomer }}">
        <text class="form-label">联系电话</text>
        <input 
          class="form-input"
          value="{{ selectedCustomer.phone }}"
          disabled
        />
      </view>
    </view>

    <!-- 备注信息 -->
    <view class="form-section">
      <view class="section-title">备注信息</view>
      
      <view class="form-item">
        <text class="form-label">订单备注</text>
        <textarea
          class="form-textarea"
          value="{{ orderData.notes }}"
          placeholder="请输入订单备注信息（可选）"
          bindinput="onNotesInput"
          maxlength="200"
        />
        <view class="char-count">{{ orderData.notes.length }}/200</view>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <button class="btn-secondary" bindtap="saveDraft">保存草稿</button>
    <button 
      class="btn-primary" 
      bindtap="nextStep" 
      disabled="{{ !isFormValid }}"
    >
      下一步：选择产品
    </button>
  </view>
</view>
