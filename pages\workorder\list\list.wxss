/* 工单列表页面样式 - 高保真商务风格 */
.page-container {
  background-color: #F5F5F5;
  min-height: 100vh;
  padding-bottom: 120rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Helvetica Neue', sans-serif;
}

.workorder-list {
  padding: 20rpx;
}

/* 整体卡片规格 */
.workorder-card {
  background-color: #FFFFFF;
  border-radius: 24rpx; /* 12px */
  padding: 40rpx 48rpx; /* 20-24px */
  margin-bottom: 32rpx; /* 16px */
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

/* 1. 顶部状态区（核心排版修正） */
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx; /* 16px */
}

/* 左侧状态标签 */
.status-tags {
  display: flex;
  gap: 8rpx; /* 4px间距 */
  flex-shrink: 0;
}

.status-tag {
  font-size: 24rpx; /* 12px */
  color: #FFFFFF;
  padding: 8rpx 16rpx; /* 紧凑内边距 */
  border-radius: 8rpx; /* 圆角色块 */
  border: none; /* 无按钮边框 */
}

.status-tag.executing {
  background-color: #409EFF; /* 蓝色圆角色块 */
}

.status-tag.overdue {
  background-color: #F56C6C; /* 红色圆角色块 */
}

/* 中间产品标题 - 同行排版 */
.product-title {
  flex: 1;
  font-size: 32rpx; /* 16px */
  font-weight: 700; /* 加粗 */
  color: #333333; /* 黑色 */
  margin: 0 40rpx; /* 状态标签与计划数间距20px */
  white-space: nowrap; /* 单行显示 */
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 右侧计划数框 - 同行排版 */
.plan-quantity-box {
  background-color: #ECECEC; /* 浅灰色圆角框 */
  border-radius: 8rpx;
  padding: 12rpx 16rpx;
  text-align: center;
  min-width: 100rpx;
  flex-shrink: 0;
}

.plan-label {
  display: block;
  font-size: 24rpx; /* 12px */
  color: #999999; /* 灰色小字 */
  margin-bottom: 2rpx;
  line-height: 1;
}

.plan-value {
  display: block;
  font-size: 32rpx; /* 16px */
  font-weight: 400; /* 正常字重 */
  color: #333333; /* 黑色大字 */
  line-height: 1;
}

/* 2. 工单信息区 */
.workorder-info-section {
  margin-bottom: 32rpx; /* 16px */
}

/* 工单编号行 - 图标替换 */
.workorder-number-row {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.file-icon {
  font-size: 28rpx; /* 线性图标尺寸 */
  color: #999999; /* 灰色线性图标 */
  margin-right: 8rpx; /* 4px间距 */
}

.workorder-number {
  font-size: 28rpx; /* 14px */
  color: #333333;
}

/* 数据统计区 - 横向并排 */
.data-stats-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.stats-columns {
  display: flex;
  gap: 24rpx; /* 12px间距 */
  flex: 1;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start; /* 左对齐 */
}

.stat-label {
  font-size: 24rpx; /* 12px */
  color: #999999;
  margin-bottom: 4rpx;
}

.stat-value {
  font-size: 28rpx; /* 14px */
  color: #333333; /* 黑色 */
  font-weight: 400;
}

.stat-value.pending {
  color: #F56C6C; /* 待完成数红色 */
}

/* 环形进度图 - 灰色空心环 */
.circular-progress {
  width: 60rpx;
  height: 60rpx;
  flex-shrink: 0;
}

.progress-circle {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  border: 4rpx solid #999999; /* 灰色空心环，边框2px */
  background: transparent; /* 无填充 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-text {
  font-size: 28rpx; /* 14px */
  color: #333333; /* 黑色 */
  font-weight: 400;
}

/* 3. 进度时间区 */
.progress-time-section {
  margin-bottom: 32rpx; /* 16px */
}

/* 计划时间 - 灰色线性图标 */
.time-info {
  margin-bottom: 24rpx;
}

.time-row {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.calendar-icon {
  font-size: 28rpx; /* 线性图标尺寸 */
  color: #999999; /* 灰色线性图标，去掉蓝色 */
  margin-right: 8rpx; /* 4px间距 */
}

.time-text {
  font-size: 28rpx; /* 14px */
  color: #333333; /* 改为黑色 */
}

/* 工单进度 - 灰色线性图标 */
.progress-info {
  margin-bottom: 24rpx;
}

.progress-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.progress-icon {
  font-size: 28rpx; /* 线性图标尺寸 */
  color: #999999; /* 灰色线性图标 */
  margin-right: 8rpx; /* 4px间距 */
}

.progress-title {
  font-size: 28rpx; /* 14px */
  color: #333333;
}

.progress-bar {
  height: 8rpx; /* 进度条高度 */
  background-color: #ECECEC; /* 灰色轨道 */
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #ECECEC; /* 0%填充，仅显示轨道 */
  transition: width 0.3s ease;
}

/* 工单说明 - 灰色线性图标 */
.description-info {
  display: flex;
  align-items: center;
}

.text-icon {
  font-size: 28rpx; /* 线性图标尺寸 */
  color: #999999; /* 灰色线性图标 */
  margin-right: 8rpx; /* 4px间距 */
}

.description-text {
  font-size: 24rpx; /* 12px说明文字 */
  color: #999999; /* 灰色 */
}

/* 4. 步骤流程区（压铸 + 去毛刺 + 包装入库） */
.process-steps-section {
  display: flex;
  justify-content: space-between;
  margin-bottom: 32rpx; /* 16px */
  padding: 0 20rpx;
  gap: 48rpx; /* 24px间距 */
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.step-icon-container {
  position: relative;
  margin-bottom: 12rpx; /* 图标与文字间距6px */
}

/* 压铸步骤 - 绿色空心圆环 */
.step-icon {
  width: 48rpx; /* 24px */
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: transparent; /* 无填充 */
}

.step-icon.completed {
  border: 4rpx solid #67C23A; /* 绿色空心圆环，边框2px */
}

.step-icon.pending {
  border: 4rpx solid #999999; /* 灰色空心圆环，边框2px */
}

.check-icon {
  color: #67C23A; /* 绿色对勾 */
  font-size: 20rpx;
  font-weight: bold;
}

/* 红色提醒 - 圆环左下角独立红色小圆点 */
.task-count {
  position: absolute;
  bottom: -4rpx;
  left: -4rpx;
  width: 16rpx; /* 8px直径 */
  height: 16rpx;
  background-color: #F56C6C; /* 红色小圆点 */
  color: #FFFFFF;
  border-radius: 50%;
  font-size: 20rpx; /* 10px */
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.step-name {
  font-size: 28rpx; /* 14px */
  color: #333333; /* 黑色 */
  margin-bottom: 4rpx;
  text-align: center;
}

.step-progress {
  font-size: 24rpx; /* 12px */
  color: #999999; /* 灰色 */
  text-align: center;
}

/* 5. 底部操作区（快速操作按钮） */
.card-actions {
  text-align: center;
  padding-top: 24rpx;
  border-top: 1rpx solid #F0F0F0;
}

.quick-action-btn {
  background-color: #FFFFFF; /* 白色背景 */
  border: 2rpx solid #DCDCDC; /* 浅灰色边框1px */
  border-radius: 8rpx; /* 圆角矩形 */
  padding: 20rpx 40rpx; /* 适中内边距 */
  font-size: 28rpx; /* 14px */
  color: #999999; /* 灰色文字 */
  font-weight: 400; /* 正常字重 */
}

.quick-action-btn::after {
  border: none; /* 取消厚重边框 */
}

.quick-action-btn:active {
  background-color: #F8F8F8; /* 简洁扁平风 */
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  color: #999999;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  line-height: 1.5;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  padding: 20rpx;
  border-top: 1rpx solid var(--border-color);
  z-index: 100;
}

/* 快速操作弹窗样式 */
/* 一、弹窗整体框架 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5); /* 半透明灰色遮罩 */
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quick-action-modal {
  background-color: #FFFFFF; /* 纯白背景 */
  border-radius: 16rpx; /* 8px圆角 */
  border: 2rpx solid #E5E5E5; /* 浅灰边框 */
  width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  font-family: 'Microsoft YaHei', sans-serif;
}

/* 二、标题栏（顶部区域） */
.modal-header {
  background: linear-gradient(135deg, #007AFF, #0056CC); /* 小程序主色调渐变 */
  height: 80rpx; /* 40px高度 */
  padding: 0 32rpx; /* 16px左右内边距 */
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: 32rpx; /* 16px */
  color: #FFFFFF; /* 白色文字 */
  font-weight: 600;
}

.modal-close-btn {
  width: 40rpx; /* 20px×20px */
  height: 40rpx;
  background: transparent;
  border: none;
  color: #FFFFFF; /* 白色×图标 */
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin-right: 24rpx; /* 距离右侧12px */
}

.modal-close-btn::after {
  border: none;
}

/* 三、内容区 */
.modal-content {
  padding: 32rpx; /* 16px内边距 */
}

/* 3.1 & 3.3 优先级/执行状态模块 */
.form-section {
  margin-bottom: 32rpx; /* 16px模块间距 */
}

.section-label {
  font-size: 28rpx; /* 14px */
  color: #1D1D1F; /* 小程序主文字色 */
  display: block;
  margin-bottom: 16rpx; /* 8px上边距 */
  font-weight: 600;
}

.button-group {
  display: flex;
  gap: 16rpx; /* 8px间距 */
}

.option-btn {
  border-radius: 8rpx; /* 4px圆角 */
  background-color: #F2F2F7; /* 小程序浅灰背景 */
  color: #1D1D1F; /* 小程序文字色 */
  font-size: 28rpx; /* 14px */
  border: none;
  padding: 12rpx 24rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  position: relative;
  transition: all 0.2s ease;
}

.option-btn.selected {
  background: linear-gradient(135deg, #007AFF, #0056CC); /* 小程序主色调渐变 */
  color: #FFFFFF; /* 白色文字 */
}

.option-btn::after {
  border: none;
}

.check-icon {
  font-size: 24rpx; /* 12px×12px */
  color: #FFFFFF; /* 白色对勾 */
  font-weight: bold;
}

/* 3.2 同步更新模块 */
.checkbox-row {
  display: flex;
  align-items: center;
  gap: 16rpx; /* 8px间距 */
}

.checkbox {
  width: 28rpx; /* 14px×14px */
  height: 28rpx;
  border: 2rpx solid #8E8E93; /* 小程序灰色边框 */
  border-radius: 4rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FFFFFF;
  transition: all 0.2s ease;
}

.checkbox.checked {
  background: linear-gradient(135deg, #007AFF, #0056CC);
  border-color: #007AFF;
}

.checkbox .check-icon {
  font-size: 20rpx;
  color: #FFFFFF;
}

.checkbox-label {
  font-size: 28rpx; /* 14px */
  color: #1D1D1F; /* 小程序主文字色 */
}

/* 四、底部操作区 */
.modal-actions {
  padding: 32rpx; /* 内边距 */
  padding-top: 48rpx; /* 24px上边距 */
  display: flex;
  justify-content: center;
  gap: 32rpx; /* 16px间距 */
}

.action-btn {
  width: 240rpx; /* 120px宽度 */
  height: 72rpx; /* 36px高度 */
  border-radius: 8rpx; /* 4px圆角 */
  font-size: 28rpx; /* 14px */
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.confirm-btn {
  background: linear-gradient(135deg, #007AFF, #0056CC); /* 小程序主色调渐变 */
  color: #FFFFFF; /* 白色文字 */
  box-shadow: 0 2rpx 4rpx rgba(0, 122, 255, 0.2);
}

.confirm-btn:active {
  background: linear-gradient(135deg, #0056CC, #004499); /* 深蓝加深 */
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 1rpx 2rpx rgba(0, 122, 255, 0.15);
}

.cancel-btn {
  background-color: #FFFFFF; /* 白色背景 */
  border: 2rpx solid #E5E5EA; /* 小程序边框色 */
  color: #1D1D1F; /* 小程序文字色 */
}

.cancel-btn:active {
  background-color: #F2F2F7; /* 小程序激活背景色 */
  transform: translateY(2rpx) scale(0.98);
}

.cancel-btn::after {
  border: none;
}

.bottom-actions .action-btn {
  width: 100%;
  height: 80rpx;
  background-color: var(--primary-color);
  color: #FFFFFF;
  border: none;
  border-radius: var(--radius-base);
  font-size: 32rpx;
  font-weight: var(--font-weight-medium);
}
