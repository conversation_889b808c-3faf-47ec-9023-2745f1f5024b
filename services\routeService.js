/**
 * 工艺路线服务 - 管理工艺路线数据
 */

class RouteService {
  constructor() {
    this.storageKey = 'erp_routes'
    this.initDefaultRoutes()
  }

  /**
   * 初始化默认工艺路线数据
   */
  initDefaultRoutes() {
    const existingRoutes = this.getRoutesFromStorage()
    if (!existingRoutes || existingRoutes.length === 0) {
      const defaultRoutes = [
        {
          id: 'route_001',
          name: 'GYLX20230003加工件（氧化）',
          description: '包含工序：压铸,去毛刺,车,钻,铣,氧化,包装入库',
          processes: [
            { processId: 'proc_001', order: 1, estimatedHours: 2.0 },
            { processId: 'proc_002', order: 2, estimatedHours: 0.5 },
            { processId: 'proc_003', order: 3, estimatedHours: 1.5 },
            { processId: 'proc_004', order: 4, estimatedHours: 1.0 },
            { processId: 'proc_005', order: 5, estimatedHours: 2.5 },
            { processId: 'proc_006', order: 6, estimatedHours: 4.0 }
          ],
          totalHours: 11.5,
          category: '机械加工',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'route_002',
          name: 'GYLX20230002加工件（喷涂）',
          description: '包含工序：压铸,去毛刺,车,钻,铣,喷涂,包装入库',
          processes: [
            { processId: 'proc_001', order: 1, estimatedHours: 2.0 },
            { processId: 'proc_002', order: 2, estimatedHours: 0.5 },
            { processId: 'proc_003', order: 3, estimatedHours: 1.5 },
            { processId: 'proc_004', order: 4, estimatedHours: 1.0 },
            { processId: 'proc_005', order: 5, estimatedHours: 2.5 }
          ],
          totalHours: 7.5,
          category: '机械加工',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'route_003',
          name: 'GYLX20230001标准工艺',
          description: '包含工序：压铸,去毛刺,包装入库',
          processes: [
            { processId: 'proc_001', order: 1, estimatedHours: 2.0 },
            { processId: 'proc_002', order: 2, estimatedHours: 0.5 }
          ],
          totalHours: 2.5,
          category: '标准工艺',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ]
      this.saveRoutesToStorage(defaultRoutes)
    }
  }

  /**
   * 从本地存储获取工艺路线数据
   */
  getRoutesFromStorage() {
    try {
      const data = wx.getStorageSync(this.storageKey)
      return data ? JSON.parse(data) : []
    } catch (error) {
      console.error('获取工艺路线数据失败:', error)
      return []
    }
  }

  /**
   * 保存工艺路线数据到本地存储
   */
  saveRoutesToStorage(routes) {
    try {
      wx.setStorageSync(this.storageKey, JSON.stringify(routes))
      return true
    } catch (error) {
      console.error('保存工艺路线数据失败:', error)
      return false
    }
  }

  /**
   * 获取所有工艺路线列表
   */
  async getRouteList() {
    try {
      const routes = this.getRoutesFromStorage()
      return {
        success: true,
        data: routes.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      }
    } catch (error) {
      console.error('获取工艺路线列表失败:', error)
      return {
        success: false,
        message: '获取工艺路线列表失败',
        data: []
      }
    }
  }

  /**
   * 根据ID获取工艺路线详情
   */
  async getRouteById(id) {
    try {
      const routes = this.getRoutesFromStorage()
      const route = routes.find(item => item.id === id)
      
      if (route) {
        return {
          success: true,
          data: route
        }
      } else {
        return {
          success: false,
          message: '工艺路线不存在'
        }
      }
    } catch (error) {
      console.error('获取工艺路线详情失败:', error)
      return {
        success: false,
        message: '获取工艺路线详情失败'
      }
    }
  }

  /**
   * 创建新工艺路线
   */
  async createRoute(routeData) {
    try {
      // 验证必填字段
      if (!routeData.name || !routeData.name.trim()) {
        return {
          success: false,
          message: '工艺路线名称不能为空'
        }
      }

      if (!routeData.processes || routeData.processes.length === 0) {
        return {
          success: false,
          message: '工艺路线必须包含至少一个工序'
        }
      }

      const routes = this.getRoutesFromStorage()
      
      // 检查工艺路线名称是否已存在
      const existingRoute = routes.find(item => 
        item.name.trim().toLowerCase() === routeData.name.trim().toLowerCase()
      )
      
      if (existingRoute) {
        return {
          success: false,
          message: '工艺路线名称已存在'
        }
      }

      // 计算总工时
      const totalHours = routeData.processes.reduce((sum, process) => {
        return sum + (process.estimatedHours || 0)
      }, 0)

      // 创建新工艺路线
      const newRoute = {
        id: 'route_' + Date.now(),
        name: routeData.name.trim(),
        description: routeData.description ? routeData.description.trim() : '',
        processes: routeData.processes.map((process, index) => ({
          processId: process.processId,
          order: index + 1,
          estimatedHours: parseFloat(process.estimatedHours || 0)
        })),
        totalHours: totalHours,
        category: routeData.category ? routeData.category.trim() : '其他',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      routes.push(newRoute)
      
      if (this.saveRoutesToStorage(routes)) {
        return {
          success: true,
          message: '工艺路线创建成功',
          data: newRoute
        }
      } else {
        return {
          success: false,
          message: '保存工艺路线失败'
        }
      }
    } catch (error) {
      console.error('创建工艺路线失败:', error)
      return {
        success: false,
        message: '创建工艺路线失败'
      }
    }
  }

  /**
   * 更新工艺路线信息
   */
  async updateRoute(id, routeData) {
    try {
      // 验证必填字段
      if (!routeData.name || !routeData.name.trim()) {
        return {
          success: false,
          message: '工艺路线名称不能为空'
        }
      }

      if (!routeData.processes || routeData.processes.length === 0) {
        return {
          success: false,
          message: '工艺路线必须包含至少一个工序'
        }
      }

      const routes = this.getRoutesFromStorage()
      const routeIndex = routes.findIndex(item => item.id === id)
      
      if (routeIndex === -1) {
        return {
          success: false,
          message: '工艺路线不存在'
        }
      }

      // 检查工艺路线名称是否与其他路线重复
      const existingRoute = routes.find(item => 
        item.id !== id && 
        item.name.trim().toLowerCase() === routeData.name.trim().toLowerCase()
      )
      
      if (existingRoute) {
        return {
          success: false,
          message: '工艺路线名称已存在'
        }
      }

      // 计算总工时
      const totalHours = routeData.processes.reduce((sum, process) => {
        return sum + (process.estimatedHours || 0)
      }, 0)

      // 更新工艺路线信息
      routes[routeIndex] = {
        ...routes[routeIndex],
        name: routeData.name.trim(),
        description: routeData.description ? routeData.description.trim() : '',
        processes: routeData.processes.map((process, index) => ({
          processId: process.processId,
          order: index + 1,
          estimatedHours: parseFloat(process.estimatedHours || 0)
        })),
        totalHours: totalHours,
        category: routeData.category ? routeData.category.trim() : '其他',
        updatedAt: new Date().toISOString()
      }

      if (this.saveRoutesToStorage(routes)) {
        return {
          success: true,
          message: '工艺路线更新成功',
          data: routes[routeIndex]
        }
      } else {
        return {
          success: false,
          message: '保存工艺路线失败'
        }
      }
    } catch (error) {
      console.error('更新工艺路线失败:', error)
      return {
        success: false,
        message: '更新工艺路线失败'
      }
    }
  }

  /**
   * 删除工艺路线
   */
  async deleteRoute(id) {
    try {
      const routes = this.getRoutesFromStorage()
      const routeIndex = routes.findIndex(item => item.id === id)
      
      if (routeIndex === -1) {
        return {
          success: false,
          message: '工艺路线不存在'
        }
      }

      routes.splice(routeIndex, 1)
      
      if (this.saveRoutesToStorage(routes)) {
        return {
          success: true,
          message: '工艺路线删除成功'
        }
      } else {
        return {
          success: false,
          message: '删除工艺路线失败'
        }
      }
    } catch (error) {
      console.error('删除工艺路线失败:', error)
      return {
        success: false,
        message: '删除工艺路线失败'
      }
    }
  }

  /**
   * 搜索工艺路线
   */
  async searchRoutes(keyword) {
    try {
      const routes = this.getRoutesFromStorage()
      
      if (!keyword || !keyword.trim()) {
        return this.getRouteList()
      }

      const filteredRoutes = routes.filter(route =>
        route.name.toLowerCase().includes(keyword.toLowerCase()) ||
        route.description.toLowerCase().includes(keyword.toLowerCase()) ||
        route.category.toLowerCase().includes(keyword.toLowerCase())
      )

      return {
        success: true,
        data: filteredRoutes.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      }
    } catch (error) {
      console.error('搜索工艺路线失败:', error)
      return {
        success: false,
        message: '搜索工艺路线失败',
        data: []
      }
    }
  }
}

// 导出单例实例
module.exports = new RouteService()
