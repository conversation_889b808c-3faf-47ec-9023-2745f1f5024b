/**
 * 产品分类服务 - 管理产品分类数据
 */

class CategoryService {
  constructor() {
    this.storageKey = 'erp_categories'
    this.initDefaultCategories()
  }

  /**
   * 初始化默认分类数据
   */
  initDefaultCategories() {
    const existingCategories = this.getCategoriesFromStorage()
    if (!existingCategories || existingCategories.length === 0) {
      const defaultCategories = [
        {
          id: 'cat_001',
          name: '电子产品',
          description: '各类电子设备和配件',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'cat_002',
          name: '机械配件',
          description: '机械设备相关配件',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'cat_003',
          name: '原材料',
          description: '生产用原材料',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ]
      this.saveCategoriesToStorage(defaultCategories)
    }
  }

  /**
   * 从本地存储获取分类数据
   */
  getCategoriesFromStorage() {
    try {
      const data = wx.getStorageSync(this.storageKey)
      return data ? JSON.parse(data) : []
    } catch (error) {
      console.error('获取分类数据失败:', error)
      return []
    }
  }

  /**
   * 保存分类数据到本地存储
   */
  saveCategoriesToStorage(categories) {
    try {
      wx.setStorageSync(this.storageKey, JSON.stringify(categories))
      return true
    } catch (error) {
      console.error('保存分类数据失败:', error)
      return false
    }
  }

  /**
   * 获取所有分类列表
   */
  async getCategoryList() {
    try {
      const categories = this.getCategoriesFromStorage()
      return {
        success: true,
        data: categories.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      }
    } catch (error) {
      console.error('获取分类列表失败:', error)
      return {
        success: false,
        message: '获取分类列表失败',
        data: []
      }
    }
  }

  /**
   * 根据ID获取分类详情
   */
  async getCategoryById(id) {
    try {
      const categories = this.getCategoriesFromStorage()
      const category = categories.find(item => item.id === id)
      
      if (category) {
        return {
          success: true,
          data: category
        }
      } else {
        return {
          success: false,
          message: '分类不存在'
        }
      }
    } catch (error) {
      console.error('获取分类详情失败:', error)
      return {
        success: false,
        message: '获取分类详情失败'
      }
    }
  }

  /**
   * 创建新分类
   */
  async createCategory(categoryData) {
    try {
      // 验证必填字段
      if (!categoryData.name || !categoryData.name.trim()) {
        return {
          success: false,
          message: '分类名称不能为空'
        }
      }

      const categories = this.getCategoriesFromStorage()
      
      // 检查分类名称是否已存在
      const existingCategory = categories.find(item => 
        item.name.trim().toLowerCase() === categoryData.name.trim().toLowerCase()
      )
      
      if (existingCategory) {
        return {
          success: false,
          message: '分类名称已存在'
        }
      }

      // 创建新分类
      const newCategory = {
        id: 'cat_' + Date.now(),
        name: categoryData.name.trim(),
        description: categoryData.description ? categoryData.description.trim() : '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      categories.push(newCategory)
      
      if (this.saveCategoriesToStorage(categories)) {
        return {
          success: true,
          message: '分类创建成功',
          data: newCategory
        }
      } else {
        return {
          success: false,
          message: '保存分类失败'
        }
      }
    } catch (error) {
      console.error('创建分类失败:', error)
      return {
        success: false,
        message: '创建分类失败'
      }
    }
  }

  /**
   * 更新分类信息
   */
  async updateCategory(id, categoryData) {
    try {
      // 验证必填字段
      if (!categoryData.name || !categoryData.name.trim()) {
        return {
          success: false,
          message: '分类名称不能为空'
        }
      }

      const categories = this.getCategoriesFromStorage()
      const categoryIndex = categories.findIndex(item => item.id === id)
      
      if (categoryIndex === -1) {
        return {
          success: false,
          message: '分类不存在'
        }
      }

      // 检查分类名称是否与其他分类重复
      const existingCategory = categories.find(item => 
        item.id !== id && 
        item.name.trim().toLowerCase() === categoryData.name.trim().toLowerCase()
      )
      
      if (existingCategory) {
        return {
          success: false,
          message: '分类名称已存在'
        }
      }

      // 更新分类信息
      categories[categoryIndex] = {
        ...categories[categoryIndex],
        name: categoryData.name.trim(),
        description: categoryData.description ? categoryData.description.trim() : '',
        updatedAt: new Date().toISOString()
      }

      if (this.saveCategoriesToStorage(categories)) {
        return {
          success: true,
          message: '分类更新成功',
          data: categories[categoryIndex]
        }
      } else {
        return {
          success: false,
          message: '保存分类失败'
        }
      }
    } catch (error) {
      console.error('更新分类失败:', error)
      return {
        success: false,
        message: '更新分类失败'
      }
    }
  }

  /**
   * 删除分类
   */
  async deleteCategory(id) {
    try {
      const categories = this.getCategoriesFromStorage()
      const categoryIndex = categories.findIndex(item => item.id === id)
      
      if (categoryIndex === -1) {
        return {
          success: false,
          message: '分类不存在'
        }
      }

      // 检查是否有产品使用此分类
      // 这里可以添加检查逻辑，防止删除正在使用的分类
      
      categories.splice(categoryIndex, 1)
      
      if (this.saveCategoriesToStorage(categories)) {
        return {
          success: true,
          message: '分类删除成功'
        }
      } else {
        return {
          success: false,
          message: '删除分类失败'
        }
      }
    } catch (error) {
      console.error('删除分类失败:', error)
      return {
        success: false,
        message: '删除分类失败'
      }
    }
  }

  /**
   * 搜索分类
   */
  async searchCategories(keyword) {
    try {
      const categories = this.getCategoriesFromStorage()
      
      if (!keyword || !keyword.trim()) {
        return this.getCategoryList()
      }

      const filteredCategories = categories.filter(category =>
        category.name.toLowerCase().includes(keyword.toLowerCase()) ||
        category.description.toLowerCase().includes(keyword.toLowerCase())
      )

      return {
        success: true,
        data: filteredCategories.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      }
    } catch (error) {
      console.error('搜索分类失败:', error)
      return {
        success: false,
        message: '搜索分类失败',
        data: []
      }
    }
  }

  /**
   * 获取分类统计信息
   */
  async getCategoryStats() {
    try {
      const categories = this.getCategoriesFromStorage()
      
      return {
        success: true,
        data: {
          total: categories.length,
          recentlyAdded: categories.filter(category => {
            const createdDate = new Date(category.createdAt)
            const weekAgo = new Date()
            weekAgo.setDate(weekAgo.getDate() - 7)
            return createdDate > weekAgo
          }).length
        }
      }
    } catch (error) {
      console.error('获取分类统计失败:', error)
      return {
        success: false,
        message: '获取分类统计失败'
      }
    }
  }
}

// 导出单例实例
module.exports = new CategoryService()
