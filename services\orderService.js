/**
 * 订单服务 - Mock数据实现
 */

// 模拟订单数据
const mockOrders = [
  {
    id: 'order_001',
    orderNo: 'PO20240101001',
    customerName: '华东制造有限公司',
    customerContact: '张经理',
    customerPhone: '13800138001',
    productName: '精密齿轮组件',
    productCode: 'GEAR-001',
    specification: '模数2.5，齿数40，材质45#钢',
    quantity: 100,
    unit: '件',
    packagingType: '防潮包装',
    packagingNotes: '防潮膜+纸箱包装，每箱10件',
    deliveryDate: '2024-02-15',
    status: 'confirmed',
    priority: 'normal',
    notes: '客户要求严格按图纸加工',
    createdAt: '2024-01-01T08:00:00.000Z',
    updatedAt: '2024-01-02T10:30:00.000Z'
  },
  {
    id: 'order_002',
    orderNo: 'PO20240101002',
    customerName: '江南机械集团',
    customerContact: '李总',
    customerPhone: '13800138002',
    productName: '传动轴承座',
    productCode: 'BEARING-002',
    specification: '内径50mm，外径120mm，高度30mm',
    quantity: 200,
    unit: '件',
    packagingType: '木箱包装',
    packagingNotes: '出口木箱包装，防震泡沫填充',
    deliveryDate: '2024-02-20',
    status: 'processing',
    priority: 'high',
    notes: '紧急订单，请优先处理',
    createdAt: '2024-01-02T09:15:00.000Z',
    updatedAt: '2024-01-03T14:20:00.000Z'
  },
  {
    id: 'order_003',
    orderNo: 'PO20240101003',
    customerName: '北方重工股份',
    customerContact: '王主管',
    customerPhone: '13800138003',
    productName: '液压缸体',
    productCode: 'CYLINDER-003',
    specification: '缸径100mm，行程200mm，压力16MPa',
    quantity: 150,
    unit: '件',
    packagingType: '托盘包装',
    packagingNotes: '托盘+缠绕膜包装，便于叉车作业',
    deliveryDate: '2024-02-25',
    status: 'pending',
    priority: 'normal',
    notes: '需要提供材质证明书',
    createdAt: '2024-01-03T11:30:00.000Z',
    updatedAt: '2024-01-03T11:30:00.000Z'
  },
  {
    id: 'order_004',
    orderNo: 'PO20240101004',
    customerName: '南方电机制造',
    customerContact: '赵工程师',
    customerPhone: '13800138004',
    productName: '电机外壳',
    productCode: 'MOTOR-004',
    specification: '铝合金材质，防护等级IP65',
    quantity: 80,
    unit: '件',
    packagingType: '定制包装',
    packagingNotes: '按客户要求定制包装，带防静电处理',
    deliveryDate: '2024-03-01',
    status: 'confirmed',
    priority: 'normal',
    notes: '表面需要阳极氧化处理',
    createdAt: '2024-01-04T14:20:00.000Z',
    updatedAt: '2024-01-04T14:20:00.000Z'
  }
]

// 模拟客户数据
const mockCustomers = [
  { id: 'customer_001', name: '测试客户A', contact: '张经理', phone: '13800138001' },
  { id: 'customer_002', name: '测试客户B', contact: '李总', phone: '13800138002' },
  { id: 'customer_003', name: '测试客户C', contact: '王主管', phone: '13800138003' }
]

class OrderService {
  /**
   * 获取订单列表
   * @param {Object} params 查询参数
   * @returns {Promise} 订单列表
   */
  static async getOrderList(params = {}) {
    return new Promise((resolve) => {
      setTimeout(() => {
        let filteredOrders = [...mockOrders]
        
        // 状态筛选
        if (params.status && params.status !== 'all') {
          filteredOrders = filteredOrders.filter(order => order.status === params.status)
        }
        
        // 关键词搜索
        if (params.keyword) {
          const keyword = params.keyword.toLowerCase()
          filteredOrders = filteredOrders.filter(order => 
            order.orderNo.toLowerCase().includes(keyword) ||
            order.customerName.toLowerCase().includes(keyword) ||
            order.productName.toLowerCase().includes(keyword)
          )
        }
        
        // 分页处理
        const page = params.page || 1
        const pageSize = params.pageSize || 10
        const start = (page - 1) * pageSize
        const end = start + pageSize
        const paginatedOrders = filteredOrders.slice(start, end)
        
        console.log('模拟获取订单列表:', paginatedOrders)
        
        resolve({
          success: true,
          data: paginatedOrders,
          total: filteredOrders.length,
          page,
          pageSize,
          hasMore: end < filteredOrders.length
        })
      }, 500)
    })
  }
  
  /**
   * 获取订单详情
   * @param {string} orderId 订单ID
   * @returns {Promise} 订单详情
   */
  static async getOrderDetail(orderId) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const order = mockOrders.find(item => item.id === orderId)
        
        if (order) {
          console.log('模拟获取订单详情:', order)
          resolve({
            success: true,
            data: order
          })
        } else {
          reject({
            success: false,
            message: '订单不存在'
          })
        }
      }, 300)
    })
  }

  /**
   * 根据ID获取订单详情
   * @param {string} orderId 订单ID
   * @returns {Promise} 订单详情
   */
  static async getOrderById(orderId) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const order = mockOrders.find(item => item.id === orderId)

        if (order) {
          resolve({
            success: true,
            data: order,
            message: '获取成功'
          })
        } else {
          resolve({
            success: false,
            data: null,
            message: '订单不存在'
          })
        }
      }, 300)
    })
  }

  /**
   * 创建订单
   * @param {Object} orderData 订单数据
   * @returns {Promise} 创建结果
   */
  static async createOrder(orderData) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newOrder = {
          id: `order_${Date.now()}`,
          orderNo: `PO${new Date().toISOString().slice(0, 10).replace(/-/g, '')}${String(mockOrders.length + 1).padStart(3, '0')}`,
          ...orderData,
          status: 'pending',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
        
        mockOrders.unshift(newOrder)
        
        console.log('模拟创建订单:', newOrder)
        
        resolve({
          success: true,
          data: newOrder,
          message: '订单创建成功'
        })
      }, 800)
    })
  }
  
  /**
   * 更新订单
   * @param {string} orderId 订单ID
   * @param {Object} updateData 更新数据
   * @returns {Promise} 更新结果
   */
  static async updateOrder(orderId, updateData) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const orderIndex = mockOrders.findIndex(item => item.id === orderId)
        
        if (orderIndex !== -1) {
          mockOrders[orderIndex] = {
            ...mockOrders[orderIndex],
            ...updateData,
            updatedAt: new Date().toISOString()
          }
          
          console.log('模拟更新订单:', mockOrders[orderIndex])
          
          resolve({
            success: true,
            data: mockOrders[orderIndex],
            message: '订单更新成功'
          })
        } else {
          reject({
            success: false,
            message: '订单不存在'
          })
        }
      }, 600)
    })
  }
  
  /**
   * 删除订单
   * @param {string} orderId 订单ID
   * @returns {Promise} 删除结果
   */
  static async deleteOrder(orderId) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const orderIndex = mockOrders.findIndex(item => item.id === orderId)
        
        if (orderIndex !== -1) {
          const deletedOrder = mockOrders.splice(orderIndex, 1)[0]
          
          console.log('模拟删除订单:', deletedOrder)
          
          resolve({
            success: true,
            message: '订单删除成功'
          })
        } else {
          reject({
            success: false,
            message: '订单不存在'
          })
        }
      }, 400)
    })
  }
  
  /**
   * 获取客户列表
   * @returns {Promise} 客户列表
   */
  static async getCustomerList() {
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log('模拟获取客户列表:', mockCustomers)
        
        resolve({
          success: true,
          data: mockCustomers
        })
      }, 200)
    })
  }
  /**
   * 格式化订单数据用于显示
   */
  static formatOrderForDisplay(order) {
    const statusMap = {
      'pending': '待确认',
      'confirmed': '已确认',
      'processing': '生产中',
      'completed': '已完成',
      'cancelled': '已取消'
    }

    const priorityMap = {
      'low': '低',
      'normal': '普通',
      'high': '高',
      'urgent': '紧急'
    }

    return {
      ...order,
      statusText: statusMap[order.status] || order.status,
      priorityText: priorityMap[order.priority] || order.priority,
      deliveryDateText: this.formatDate(order.deliveryDate),
      createdAtText: this.formatDateTime(order.createdAt),
      updatedAtText: this.formatDateTime(order.updatedAt),
      customerContact: order.customerContact || order.customerName,
      customerPhone: order.customerPhone || ''
    }
  }

  /**
   * 格式化日期
   */
  static formatDate(dateString) {
    if (!dateString) return ''
    const date = new Date(dateString)
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
  }

  /**
   * 格式化日期时间
   */
  static formatDateTime(dateString) {
    if (!dateString) return ''
    const date = new Date(dateString)
    return `${this.formatDate(dateString)} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
  }
}

module.exports = OrderService
