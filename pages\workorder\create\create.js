// 工单创建页面逻辑
const { formatDate } = require('../../../utils/dateUtils')
const ProcessService = require('../../../services/processService')
const RouteService = require('../../../services/routeService')
const OrderService = require('../../../services/orderService.js')
const TaskService = require('../../../services/taskService.js')

Page({
  data: {
    selectedMode: '', // 'fromOrder' | 'manual'

    // 表单数据
    formData: {
      workOrderName: '',
      productName: '',
      quantity: 1,
      unit: '件',
      priority: 'normal',
      priorityText: '普通',
      startDate: '',
      notes: '',
      processes: [] // 工序列表
    },

    // 订单选择相关
    orderOptions: [],
    selectedOrderIndex: -1,
    selectedOrder: null,

    // 优先级选项
    priorityOptions: [
      { value: 'low', label: '低' },
      { value: 'normal', label: '普通' },
      { value: 'high', label: '高' },
      { value: 'urgent', label: '紧急' }
    ],
    selectedPriorityIndex: 1,

    // 计算属性
    isFormValid: false,
    minDate: '',
    totalProcessHours: 0,

    // 工序管理相关
    availableProcesses: [],
    availableRoutes: [],
    showProcessModal: false,
    showRouteModal: false,
    showEditProcessModal: false,
    processSearchKeyword: '',
    routeSearchKeyword: '',
    editingProcess: {},
    editingProcessIndex: -1
  },

  onLoad(options) {
    console.log('工单创建页面加载', options)
    this.initPage()

    // 如果从订单页面跳转过来，直接选择从订单创建模式
    if (options.orderId) {
      this.setData({ selectedMode: 'fromOrder' })
      this.loadOrderById(options.orderId)
    }
  },

  /**
   * 初始化页面
   */
  async initPage() {
    // 设置最小日期为今天
    const today = new Date()
    const minDate = formatDate(today, 'YYYY-MM-DD')
    this.setData({ minDate })

    // 加载订单列表
    await this.loadOrders()

    // 加载工序和工艺路线数据
    await this.loadProcessData()
  },

  /**
   * 加载订单列表
   */
  async loadOrders() {
    try {
      console.log('开始加载订单列表...')

      // 检查 OrderService 是否正确导入
      if (typeof OrderService === 'undefined') {
        console.error('OrderService 未定义')
        wx.showToast({
          title: 'OrderService 未加载',
          icon: 'error'
        })
        return
      }

      // 使用 OrderService 获取订单列表（只显示已确认和待确认的订单）
      const result = await OrderService.getOrderList({
        page: 1,
        pageSize: 100 // 获取所有订单
      })

      if (result.success) {
        const confirmedOrders = result.data.filter(order =>
          order.status === 'confirmed' || order.status === 'pending'
        )

        this.setData({ orderOptions: confirmedOrders })
        console.log(`成功加载${confirmedOrders.length}个可用订单:`, confirmedOrders)
      } else {
        console.error('获取订单列表失败:', result.message)
        wx.showToast({
          title: '获取订单失败',
          icon: 'error'
        })
      }
    } catch (error) {
      console.error('加载订单列表失败:', error)
      wx.showToast({
        title: '加载订单失败',
        icon: 'error'
      })
    }
  },

  /**
   * 根据ID加载订单
   */
  async loadOrderById(orderId) {
    try {
      console.log('根据ID加载订单:', orderId)

      const result = await OrderService.getOrderById(orderId)

      if (result.success && result.data) {
        const order = result.data
        const index = this.data.orderOptions.findIndex(o => o.id === orderId)

        this.setData({
          selectedOrder: order,
          selectedOrderIndex: index,
          'formData.quantity': order.quantity, // 直接使用订单数量，不允许修改
          'formData.productName': order.productName,
          'formData.unit': order.unit
        })

        console.log('订单加载成功，数量自动设置为:', order.quantity)
        this.validateForm()
      } else {
        console.error('订单不存在:', orderId)
        wx.showToast({
          title: '订单不存在',
          icon: 'error'
        })
      }
    } catch (error) {
      console.error('加载订单失败:', error)
      wx.showToast({
        title: '加载订单失败',
        icon: 'error'
      })
    }
  },

  /**
   * 选择创建模式
   */
  selectMode(e) {
    const mode = e.currentTarget.dataset.mode
    this.setData({ selectedMode: mode })

    if (mode === 'manual') {
      this.validateForm()
    }
  },

  /**
   * 选择订单
   */
  selectOrder(e) {
    const index = e.detail.value
    const order = this.data.orderOptions[index]

    console.log('选择订单索引:', index)
    console.log('订单选项数组:', this.data.orderOptions)
    console.log('选中的订单:', order)

    if (!order) {
      console.error('订单不存在，索引:', index)
      wx.showToast({
        title: '订单数据异常',
        icon: 'error'
      })
      return
    }

    this.setData({
      selectedOrderIndex: index,
      selectedOrder: order,
      'formData.quantity': order.quantity, // 使用订单原始数量，不允许修改
      'formData.productName': order.productName,
      'formData.unit': order.unit
    })

    console.log('选择订单成功:', order.orderNo, '数量:', order.quantity)
    this.validateForm()
  },

  /**
   * 选择优先级
   */
  selectPriority(e) {
    const index = e.detail.value
    const priority = this.data.priorityOptions[index]

    this.setData({
      selectedPriorityIndex: index,
      'formData.priority': priority.value,
      'formData.priorityText': priority.label
    })
  },

  /**
   * 选择开始日期
   */
  selectStartDate(e) {
    this.setData({
      'formData.startDate': e.detail.value
    })
  },

  /**
   * 输入事件处理
   */
  onWorkOrderNameInput(e) {
    this.setData({ 'formData.workOrderName': e.detail.value })
    this.validateForm()
  },

  onProductNameInput(e) {
    this.setData({ 'formData.productName': e.detail.value })
    this.validateForm()
  },

  onQuantityInput(e) {
    // 仅在手动创建模式下允许修改数量
    if (this.data.selectedMode === 'manual') {
      const quantity = parseInt(e.detail.value) || 1
      this.setData({ 'formData.quantity': quantity })
      this.validateForm()
    }
  },

  onUnitInput(e) {
    this.setData({ 'formData.unit': e.detail.value })
  },

  onNotesInput(e) {
    this.setData({ 'formData.notes': e.detail.value })
  },

  /**
   * 数量操作（仅在手动创建模式下可用）
   */
  decreaseQuantity() {
    if (this.data.selectedMode === 'manual') {
      const quantity = Math.max(1, this.data.formData.quantity - 1)
      this.setData({ 'formData.quantity': quantity })
      this.validateForm()
    }
  },

  increaseQuantity() {
    if (this.data.selectedMode === 'manual') {
      const quantity = this.data.formData.quantity + 1
      this.setData({ 'formData.quantity': quantity })
      this.validateForm()
    }
  },

  /**
   * 验证表单
   */
  validateForm() {
    let isValid = false

    if (this.data.selectedMode === 'fromOrder') {
      // 从订单创建：需要选择订单（数量自动从订单获取）
      isValid = this.data.selectedOrder && this.data.formData.quantity > 0
    } else if (this.data.selectedMode === 'manual') {
      // 手动创建：需要工单名称、产品名称和数量
      const { workOrderName, productName, quantity } = this.data.formData
      isValid = workOrderName && productName && quantity > 0
    }

    this.setData({ isFormValid: isValid })
  },

  /**
   * 返回
   */
  goBack() {
    if (this.data.selectedMode) {
      this.setData({ selectedMode: '' })
    } else {
      wx.navigateBack()
    }
  },

  /**
   * 提交工单
   */
  submitWorkOrder() {
    if (!this.data.isFormValid) {
      wx.showToast({
        title: '请完善工单信息',
        icon: 'none'
      })
      return
    }

    wx.showModal({
      title: '确认创建',
      content: '确定要创建此工单吗？',
      success: (res) => {
        if (res.confirm) {
          this.createWorkOrder()
        }
      }
    })
  },

  /**
   * 创建工单
   */
  async createWorkOrder() {
    try {
      wx.showLoading({ title: '创建中...' })

      // 生成工单号
      const workOrderNo = `WO${Date.now()}`

      // 构建工单数据
      const workOrderData = {
        id: `workorder_${Date.now()}`,
        workOrderNo,
        status: 'pending',
        statusText: '待开始',
        priority: this.data.formData.priority,
        startDate: this.data.formData.startDate,
        notes: this.data.formData.notes,
        processes: this.data.formData.processes || [], // 工序列表
        totalProcessHours: this.data.totalProcessHours || 0, // 总工时
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        completedTasks: 0,
        totalTasks: Math.max(3, this.data.formData.processes.length), // 任务数量基于工序数量
        progressPercent: 0
      }

      if (this.data.selectedMode === 'fromOrder') {
        // 从订单创建
        const order = this.data.selectedOrder
        workOrderData.orderNo = order.orderNo
        workOrderData.orderId = order.id
        workOrderData.customerName = order.customerName
        workOrderData.productName = order.productName
        workOrderData.productCode = order.productCode
        workOrderData.specification = order.specification
        workOrderData.quantity = this.data.formData.quantity
        workOrderData.unit = order.unit
        workOrderData.deliveryDate = order.deliveryDate
      } else {
        // 手动创建
        workOrderData.workOrderName = this.data.formData.workOrderName
        workOrderData.productName = this.data.formData.productName
        workOrderData.quantity = this.data.formData.quantity
        workOrderData.unit = this.data.formData.unit
      }

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 保存到本地存储（模拟数据库）
      const workOrders = wx.getStorageSync('workOrders') || []
      workOrders.unshift(workOrderData)
      wx.setStorageSync('workOrders', workOrders)

      // 自动生成任务（从工序创建任务）
      if (workOrderData.processes && workOrderData.processes.length > 0) {
        try {
          console.log('开始为工单创建任务:', workOrderData.workOrderNo)
          const taskResult = await TaskService.createTasksFromWorkOrder(workOrderData)

          if (taskResult.success) {
            console.log('任务创建成功:', taskResult.data.length, '个任务')
          } else {
            console.error('任务创建失败:', taskResult.message)
          }
        } catch (error) {
          console.error('创建任务时出错:', error)
          // 不影响工单创建，只记录错误
        }
      }

      // 如果是从订单创建，更新订单状态
      if (this.data.selectedMode === 'fromOrder' && this.data.selectedOrder) {
        const orders = wx.getStorageSync('orders') || []
        const orderIndex = orders.findIndex(o => o.id === this.data.selectedOrder.id)
        if (orderIndex !== -1) {
          orders[orderIndex].status = 'processing'
          orders[orderIndex].statusText = '生产中'
          wx.setStorageSync('orders', orders)
        }
      }

      wx.hideLoading()

      wx.showToast({
        title: '工单创建成功',
        icon: 'success'
      })

      // 延迟返回
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)

    } catch (error) {
      console.error('创建工单失败:', error)
      wx.hideLoading()
      wx.showToast({
        title: '创建失败',
        icon: 'error'
      })
    }
  },

  /**
   * 加载工序和工艺路线数据
   */
  async loadProcessData() {
    try {
      // 加载工序列表
      const processResult = await ProcessService.getProcessList()
      if (processResult.success) {
        this.setData({ availableProcesses: processResult.data })
      }

      // 加载工艺路线列表
      const routeResult = await RouteService.getRouteList()
      if (routeResult.success) {
        this.setData({ availableRoutes: routeResult.data })
      }
    } catch (error) {
      console.error('加载工序数据失败:', error)
    }
  },

  /**
   * 计算总工时
   */
  calculateTotalProcessHours() {
    const { formData } = this.data
    const totalHours = formData.processes.reduce((sum, process) => {
      return sum + (process.estimatedHours || 0)
    }, 0)

    this.setData({ totalProcessHours: totalHours })
  },

  /**
   * 显示工艺路线选择弹窗
   */
  showRouteSelector() {
    this.setData({
      showRouteModal: true,
      routeSearchKeyword: ''
    })
    this.filterAvailableRoutes()
  },

  /**
   * 隐藏工艺路线选择弹窗
   */
  hideRouteModal() {
    this.setData({
      showRouteModal: false,
      routeSearchKeyword: ''
    })
  },

  /**
   * 工艺路线搜索输入处理
   */
  onRouteSearchInput(e) {
    this.setData({
      routeSearchKeyword: e.detail.value
    })
    this.filterAvailableRoutes()
  },

  /**
   * 筛选可用工艺路线
   */
  filterAvailableRoutes() {
    // 这里可以根据搜索关键词筛选工艺路线
    // 暂时直接显示所有路线
  },

  /**
   * 选择工艺路线
   */
  async selectRoute(e) {
    const route = e.currentTarget.dataset.route

    try {
      // 获取工艺路线的详细工序信息
      const routeResult = await RouteService.getRouteById(route.id)
      if (!routeResult.success) {
        wx.showToast({
          title: '获取工艺路线失败',
          icon: 'none'
        })
        return
      }

      const routeData = routeResult.data
      const processes = []

      // 获取每个工序的详细信息
      for (const routeProcess of routeData.processes) {
        const processResult = await ProcessService.getProcessById(routeProcess.processId)
        if (processResult.success) {
          processes.push({
            id: 'proc_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
            processId: routeProcess.processId,
            name: processResult.data.name,
            description: processResult.data.description,
            estimatedHours: routeProcess.estimatedHours,
            order: routeProcess.order
          })
        }
      }

      // 按顺序排序
      processes.sort((a, b) => a.order - b.order)

      // 更新表单数据
      this.setData({
        'formData.processes': processes,
        showRouteModal: false
      })

      this.calculateTotalProcessHours()
      this.validateForm()

      wx.showToast({
        title: `已应用工艺路线：${route.name}`,
        icon: 'success'
      })

    } catch (error) {
      console.error('应用工艺路线失败:', error)
      wx.showToast({
        title: '应用工艺路线失败',
        icon: 'none'
      })
    }
  },

  /**
   * 显示工序选择弹窗
   */
  showProcessSelector() {
    this.setData({
      showProcessModal: true,
      processSearchKeyword: ''
    })
    this.filterAvailableProcesses()
  },

  /**
   * 隐藏工序选择弹窗
   */
  hideProcessModal() {
    this.setData({
      showProcessModal: false,
      processSearchKeyword: ''
    })
  },

  /**
   * 工序搜索输入处理
   */
  onProcessSearchInput(e) {
    this.setData({
      processSearchKeyword: e.detail.value
    })
    this.filterAvailableProcesses()
  },

  /**
   * 筛选可用工序
   */
  filterAvailableProcesses() {
    // 这里可以根据搜索关键词筛选工序
    // 暂时直接显示所有工序
  },

  /**
   * 选择工序
   */
  selectProcess(e) {
    const process = e.currentTarget.dataset.process
    const { formData } = this.data

    const newProcess = {
      id: 'proc_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
      processId: process.id,
      name: process.name,
      description: process.description,
      estimatedHours: process.estimatedHours,
      order: formData.processes.length + 1
    }

    formData.processes.push(newProcess)

    this.setData({
      'formData.processes': formData.processes,
      showProcessModal: false
    })

    this.calculateTotalProcessHours()
    this.validateForm()

    wx.showToast({
      title: `已添加工序：${process.name}`,
      icon: 'success'
    })
  },

  /**
   * 移除工序
   */
  removeProcess(e) {
    const index = e.currentTarget.dataset.index
    const { formData } = this.data

    formData.processes.splice(index, 1)

    // 重新排序
    formData.processes.forEach((process, i) => {
      process.order = i + 1
    })

    this.setData({
      'formData.processes': formData.processes
    })

    this.calculateTotalProcessHours()
    this.validateForm()
  },

  /**
   * 上移工序
   */
  moveProcessUp(e) {
    const index = e.currentTarget.dataset.index
    if (index === 0) return

    const { formData } = this.data
    const processes = [...formData.processes]

    // 交换位置
    const temp = processes[index]
    processes[index] = processes[index - 1]
    processes[index - 1] = temp

    // 重新排序
    processes.forEach((process, i) => {
      process.order = i + 1
    })

    this.setData({
      'formData.processes': processes
    })
  },

  /**
   * 下移工序
   */
  moveProcessDown(e) {
    const index = e.currentTarget.dataset.index
    const { formData } = this.data

    if (index === formData.processes.length - 1) return

    const processes = [...formData.processes]

    // 交换位置
    const temp = processes[index]
    processes[index] = processes[index + 1]
    processes[index + 1] = temp

    // 重新排序
    processes.forEach((process, i) => {
      process.order = i + 1
    })

    this.setData({
      'formData.processes': processes
    })
  },

  /**
   * 编辑工序
   */
  editProcess(e) {
    const index = e.currentTarget.dataset.index
    const { formData } = this.data
    const process = formData.processes[index]

    this.setData({
      showEditProcessModal: true,
      editingProcessIndex: index,
      editingProcess: {
        name: process.name,
        description: process.description || '',
        estimatedHours: process.estimatedHours.toString()
      }
    })
  },

  /**
   * 隐藏工序编辑弹窗
   */
  hideEditProcessModal() {
    this.setData({
      showEditProcessModal: false,
      editingProcessIndex: -1,
      editingProcess: {}
    })
  },

  /**
   * 编辑工序表单输入处理
   */
  onEditProcessNameInput(e) {
    this.setData({
      'editingProcess.name': e.detail.value
    })
  },

  onEditProcessDescInput(e) {
    this.setData({
      'editingProcess.description': e.detail.value
    })
  },

  onEditProcessHoursInput(e) {
    this.setData({
      'editingProcess.estimatedHours': e.detail.value
    })
  },

  /**
   * 确认编辑工序
   */
  confirmEditProcess() {
    const { editingProcess, editingProcessIndex, formData } = this.data

    if (!editingProcess.name || !editingProcess.name.trim()) {
      wx.showToast({
        title: '请输入工序名称',
        icon: 'none'
      })
      return
    }

    if (!editingProcess.estimatedHours || parseFloat(editingProcess.estimatedHours) <= 0) {
      wx.showToast({
        title: '请输入有效的预计工时',
        icon: 'none'
      })
      return
    }

    // 更新工序信息
    const processes = [...formData.processes]
    processes[editingProcessIndex] = {
      ...processes[editingProcessIndex],
      name: editingProcess.name.trim(),
      description: editingProcess.description.trim(),
      estimatedHours: parseFloat(editingProcess.estimatedHours)
    }

    this.setData({
      'formData.processes': processes,
      showEditProcessModal: false,
      editingProcessIndex: -1,
      editingProcess: {}
    })

    this.calculateTotalProcessHours()
    this.validateForm()

    wx.showToast({
      title: '工序更新成功',
      icon: 'success'
    })
  }
})
