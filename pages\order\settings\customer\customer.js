// 生产管理系统 - 客户管理页面
const CustomerService = require('../../../../services/customerService.js')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    customerList: [],
    showAddForm: false,
    formData: {
      name: '',
      contact: '',
      phone: '',
      address: '',
      email: '',
      notes: ''
    },
    editingId: null,
    loading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    wx.setNavigationBarTitle({
      title: '客户管理'
    })
    this.loadCustomerList()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.loadCustomerList()
  },

  /**
   * 加载客户列表
   */
  async loadCustomerList() {
    try {
      this.setData({ loading: true })
      const customerList = await CustomerService.getCustomerList()
      this.setData({ customerList })
    } catch (error) {
      console.error('加载客户列表失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 显示添加表单
   */
  showAddCustomer() {
    this.setData({
      showAddForm: true,
      editingId: null,
      formData: {
        name: '',
        contact: '',
        phone: '',
        address: '',
        email: '',
        notes: ''
      }
    })
  },

  /**
   * 编辑客户
   */
  editCustomer(e) {
    const customer = e.currentTarget.dataset.customer
    this.setData({
      showAddForm: true,
      editingId: customer.id,
      formData: {
        name: customer.name,
        contact: customer.contact,
        phone: customer.phone,
        address: customer.address || '',
        email: customer.email || '',
        notes: customer.notes || ''
      }
    })
  },

  /**
   * 隐藏表单
   */
  hideForm() {
    this.setData({ showAddForm: false })
  },

  /**
   * 表单输入处理
   */
  onFormInput(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    this.setData({
      [`formData.${field}`]: value
    })
  },

  /**
   * 保存客户
   */
  async saveCustomer() {
    const { formData, editingId } = this.data
    
    // 表单验证
    if (!formData.name.trim()) {
      wx.showToast({
        title: '请输入客户名称',
        icon: 'none'
      })
      return
    }
    
    if (!formData.contact.trim()) {
      wx.showToast({
        title: '请输入联系人',
        icon: 'none'
      })
      return
    }

    try {
      this.setData({ loading: true })
      
      if (editingId) {
        // 更新客户
        await CustomerService.updateCustomer(editingId, formData)
        wx.showToast({
          title: '更新成功',
          icon: 'success'
        })
      } else {
        // 新增客户
        await CustomerService.createCustomer(formData)
        wx.showToast({
          title: '添加成功',
          icon: 'success'
        })
      }
      
      this.hideForm()
      this.loadCustomerList()
    } catch (error) {
      console.error('保存客户失败:', error)
      wx.showToast({
        title: '保存失败',
        icon: 'error'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 删除客户
   */
  async deleteCustomer(e) {
    const { customer } = e.currentTarget.dataset
    
    const result = await new Promise((resolve) => {
      wx.showModal({
        title: '确认删除',
        content: `确定要删除客户"${customer.name}"吗？`,
        success: resolve
      })
    })
    
    if (!result.confirm) return
    
    try {
      this.setData({ loading: true })
      await CustomerService.deleteCustomer(customer.id)
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      })
      this.loadCustomerList()
    } catch (error) {
      console.error('删除客户失败:', error)
      wx.showToast({
        title: '删除失败',
        icon: 'error'
      })
    } finally {
      this.setData({ loading: false })
    }
  }
})
