/**
 * 日期工具函数
 */

/**
 * 格式化日期
 * @param {Date|string} date 日期对象或日期字符串
 * @param {string} format 格式化模板，默认 'YYYY-MM-DD'
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(date, format = 'YYYY-MM-DD') {
  if (!date) return ''
  
  try {
    const d = new Date(date)
    
    // 检查日期是否有效
    if (isNaN(d.getTime())) {
      return ''
    }
    
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    const hour = String(d.getHours()).padStart(2, '0')
    const minute = String(d.getMinutes()).padStart(2, '0')
    const second = String(d.getSeconds()).padStart(2, '0')
    
    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hour)
      .replace('mm', minute)
      .replace('ss', second)
  } catch (error) {
    console.error('日期格式化失败:', error)
    return ''
  }
}

/**
 * 格式化日期范围
 * @param {Date|string} startTime 开始时间
 * @param {Date|string} endTime 结束时间
 * @returns {string} 格式化后的日期范围
 */
function formatDateRange(startTime, endTime) {
  if (!startTime || !endTime) {
    return '待安排'
  }
  
  try {
    const startDate = new Date(startTime)
    const endDate = new Date(endTime)
    
    // 检查日期是否有效
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return '待安排'
    }
    
    const start = startDate.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
    const end = endDate.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
    return `${start} ~ ${end}`
  } catch (error) {
    console.error('日期范围格式化失败:', error)
    return '待安排'
  }
}

/**
 * 获取相对时间描述
 * @param {Date|string} date 日期
 * @returns {string} 相对时间描述
 */
function getRelativeTime(date) {
  if (!date) return ''
  
  try {
    const now = new Date()
    const target = new Date(date)
    const diff = now.getTime() - target.getTime()
    
    const minute = 60 * 1000
    const hour = 60 * minute
    const day = 24 * hour
    const week = 7 * day
    const month = 30 * day
    
    if (diff < minute) {
      return '刚刚'
    } else if (diff < hour) {
      return `${Math.floor(diff / minute)}分钟前`
    } else if (diff < day) {
      return `${Math.floor(diff / hour)}小时前`
    } else if (diff < week) {
      return `${Math.floor(diff / day)}天前`
    } else if (diff < month) {
      return `${Math.floor(diff / week)}周前`
    } else {
      return formatDate(date, 'YYYY-MM-DD')
    }
  } catch (error) {
    console.error('相对时间计算失败:', error)
    return ''
  }
}

/**
 * 判断是否为今天
 * @param {Date|string} date 日期
 * @returns {boolean} 是否为今天
 */
function isToday(date) {
  if (!date) return false
  
  try {
    const today = new Date()
    const target = new Date(date)
    
    return today.getFullYear() === target.getFullYear() &&
           today.getMonth() === target.getMonth() &&
           today.getDate() === target.getDate()
  } catch (error) {
    return false
  }
}

/**
 * 获取日期的开始时间（00:00:00）
 * @param {Date|string} date 日期
 * @returns {Date} 日期的开始时间
 */
function getStartOfDay(date) {
  const d = new Date(date)
  d.setHours(0, 0, 0, 0)
  return d
}

/**
 * 获取日期的结束时间（23:59:59）
 * @param {Date|string} date 日期
 * @returns {Date} 日期的结束时间
 */
function getEndOfDay(date) {
  const d = new Date(date)
  d.setHours(23, 59, 59, 999)
  return d
}

/**
 * 计算两个日期之间的天数差
 * @param {Date|string} startDate 开始日期
 * @param {Date|string} endDate 结束日期
 * @returns {number} 天数差
 */
function getDaysDiff(startDate, endDate) {
  try {
    const start = new Date(startDate)
    const end = new Date(endDate)
    const diffTime = Math.abs(end - start)
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  } catch (error) {
    console.error('计算日期差失败:', error)
    return 0
  }
}

module.exports = {
  formatDate,
  formatDateRange,
  getRelativeTime,
  isToday,
  getStartOfDay,
  getEndOfDay,
  getDaysDiff
}
