// 工单详情页面逻辑
Page({
  data: {
    // 工单基本信息
    workOrderInfo: {
      workOrderNo: 'SO20250001-002',
      plannedTimeRange: '2025-02-28 00:00 - 2025-02-28 23:59',
      productInfo: 'CP031 | 安装板固定件 | A款',
      completedQuantity: 0,
      totalQuantity: 1200,
      progressPercent: 0
    },

    // UI状态
    headerCollapsed: false,
    activeTab: 'progress', // 'progress' | 'materials'

    // 已结束工序
    completedProcesses: [
      {
        id: 'process_001',
        sequence: '①',
        name: '冲压',
        processCode: 'GX2024012',
        plannedQuantity: 1200,
        qualifiedQuantity: 1200,
        defectiveQuantity: 0,
        progressPercent: 100,
        workstation: '一楼车间'
      }
    ],

    // 执行中+逾期工序
    activeProcesses: [
      {
        id: 'process_002',
        sequence: '②',
        name: '电镀（委外）',
        processCode: 'GX2024013',
        plannedQuantity: 1200,
        qualifiedQuantity: 0,
        defectiveQuantity: 0,
        progressPercent: 0,
        workstation: '仓库',
        isOverdue: true
      },
      {
        id: 'process_003',
        sequence: '③',
        name: '包装',
        processCode: 'GX2024014',
        plannedQuantity: 1200,
        qualifiedQuantity: 0,
        defectiveQuantity: 0,
        progressPercent: 0,
        workstation: '四楼（组装）',
        isOverdue: false
      }
    ]
  },

  onLoad(options) {
    console.log('工单详情页面加载', options)
    if (options.id) {
      this.workOrderId = options.id
      this.loadWorkOrderDetail()
    }
  },

  /**
   * 加载工单详情
   */
  loadWorkOrderDetail() {
    // 这里可以调用API加载工单详情数据
    console.log('加载工单详情，ID:', this.workOrderId)
  },

  /**
   * 切换头部折叠状态
   */
  toggleHeaderCollapse() {
    this.setData({
      headerCollapsed: !this.data.headerCollapsed
    })
  },

  /**
   * 切换标签
   */
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab
    this.setData({
      activeTab: tab
    })
  },

  /**
   * 查看产品详情
   */
  viewProductDetail() {
    console.log('查看产品详情')
    wx.showToast({
      title: '产品详情功能开发中',
      icon: 'none'
    })
  },

  /**
   * 查看工序详情
   */
  viewProcessDetail(e) {
    const process = e.currentTarget.dataset.process
    console.log('查看工序详情:', process)
    wx.showToast({
      title: '工序详情功能开发中',
      icon: 'none'
    })
  },

  /**
   * 报工
   */
  reportWork(e) {
    const process = e.currentTarget.dataset.process
    console.log('报工:', process)
    wx.showToast({
      title: '报工功能开发中',
      icon: 'none'
    })
  },

  /**
   * 显示更多操作
   */
  showMoreActions() {
    console.log('显示更多操作')
    wx.showActionSheet({
      itemList: ['导出工单', '打印工单', '复制工单'],
      success: (res) => {
        console.log('选择操作:', res.tapIndex)
      }
    })
  },

  /**
   * 显示快速操作弹窗
   */
  showQuickActionModal() {
    console.log('显示快速操作弹窗')
    wx.showToast({
      title: '快速操作功能开发中',
      icon: 'none'
    })
  },

  /**
   * 结束工单
   */
  finishWorkOrder() {
    console.log('结束工单')
    wx.showModal({
      title: '确认结束',
      content: '确定要结束此工单吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '工单已结束',
            icon: 'success'
          })
        }
      }
    })
  }
})
