<!--工序管理页面 - 简约现代风格-->
<view class="modern-page">
  <!-- 页面头部 -->
  <view class="modern-navbar">
    <view class="header-content">
      <text class="modern-navbar-title">工序管理</text>
      <text class="page-subtitle">管理生产工序模板</text>
    </view>
    <view class="modern-navbar-actions">
      <button class="modern-btn modern-btn-primary modern-btn-small" bindtap="showCreateModal">
        新建工序
      </button>
    </view>
  </view>

  <view class="modern-container">
    <!-- 搜索栏 -->
    <view class="search-container">
      <view class="modern-input-wrapper">
        <input
          class="modern-input"
          placeholder="搜索工序名称或描述"
          value="{{ searchKeyword }}"
          bindinput="onSearchInput"
          bindconfirm="onSearchConfirm"
        />
        <view class="search-icon">🔍</view>
      </view>
    </view>

    <!-- 分类筛选 -->
    <view class="filter-section" wx:if="{{ categories.length > 0 }}">
      <scroll-view class="category-scroll" scroll-x>
        <view class="category-filters">
          <view
            class="category-filter {{ selectedCategory === '' ? 'active' : '' }}"
            bindtap="selectCategory"
            data-category=""
          >
            全部
          </view>
          <view
            class="category-filter {{ selectedCategory === item ? 'active' : '' }}"
            wx:for="{{ categories }}"
            wx:key="*this"
            bindtap="selectCategory"
            data-category="{{ item }}"
          >
            {{ item }}
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 工序列表 -->
    <view class="processes-section">
      <view class="modern-section">
        <view class="modern-section-title">
          <text class="modern-section-icon">⚙️</text>
          <text class="modern-section-text">工序列表</text>
          <view class="process-count">{{ filteredProcesses.length }}</view>
        </view>

        <!-- 工序卡片列表 -->
        <view class="process-list" wx:if="{{ filteredProcesses.length > 0 }}">
          <view
            class="modern-card process-card"
            wx:for="{{ filteredProcesses }}"
            wx:key="id"
            bindtap="viewProcessDetail"
            data-process="{{ item }}"
          >
            <view class="process-header">
              <view class="process-info">
                <text class="process-name">{{ item.name }}</text>
                <view class="process-meta">
                  <text class="process-id">ID: {{ item.id }}</text>
                  <view class="modern-tag modern-tag-primary">{{ item.category }}</view>
                </view>
              </view>
              <view class="process-actions">
                <button
                  class="action-btn edit-btn"
                  bindtap="editProcess"
                  data-process="{{ item }}"
                  catchtap="true"
                >
                  ✏️
                </button>
                <button
                  class="action-btn delete-btn"
                  bindtap="deleteProcess"
                  data-id="{{ item.id }}"
                  data-name="{{ item.name }}"
                  catchtap="true"
                >
                  🗑️
                </button>
              </view>
            </view>

            <view class="process-body">
              <text class="process-description">
                {{ item.description || '暂无描述' }}
              </text>
            </view>

            <view class="process-footer">
              <view class="process-hours">
                <text class="hours-label">预计工时：</text>
                <text class="hours-value">{{ item.estimatedHours }}小时</text>
              </view>
              <text class="process-time">
                {{ item.createdAt }}
              </text>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view class="modern-empty" wx:if="{{ filteredProcesses.length === 0 && !loading }}">
          <text class="modern-empty-icon">⚙️</text>
          <view class="modern-empty-text">
            <view>{{ searchKeyword ? '未找到匹配的工序' : '暂无工序数据' }}</view>
            <view>{{ searchKeyword ? '请尝试其他关键词' : '点击"新建工序"开始添加' }}</view>
          </view>
        </view>

        <!-- 加载状态 -->
        <view class="modern-loading" wx:if="{{ loading }}">
          <text class="modern-loading-text">加载中...</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 创建/编辑工序弹窗 -->
  <view class="modal-overlay" wx:if="{{ showModal }}" bindtap="hideModal">
    <view class="modal-container" catchtap="true">
      <view class="modal-header">
        <text class="modal-title">{{ isEditing ? '编辑工序' : '新建工序' }}</text>
        <button class="modal-close" bindtap="hideModal">✕</button>
      </view>

      <view class="modal-body">
        <view class="form-group">
          <text class="form-label">工序名称 *</text>
          <input
            class="modern-input"
            placeholder="请输入工序名称"
            value="{{ formData.name }}"
            bindinput="onNameInput"
            maxlength="50"
          />
        </view>

        <view class="form-group">
          <text class="form-label">工序分类</text>
          <input
            class="modern-input"
            placeholder="请输入工序分类"
            value="{{ formData.category }}"
            bindinput="onCategoryInput"
            maxlength="20"
          />
        </view>

        <view class="form-group">
          <text class="form-label">预计工时 (小时) *</text>
          <input
            class="modern-input"
            type="digit"
            placeholder="请输入预计工时"
            value="{{ formData.estimatedHours }}"
            bindinput="onHoursInput"
          />
        </view>

        <view class="form-group">
          <text class="form-label">工序描述</text>
          <textarea
            class="modern-textarea"
            placeholder="请输入工序描述（可选）"
            value="{{ formData.description }}"
            bindinput="onDescriptionInput"
            maxlength="500"
            auto-height
          />
        </view>
      </view>

      <view class="modal-footer">
        <button class="modern-btn modern-btn-secondary" bindtap="hideModal">
          取消
        </button>
        <button
          class="modern-btn modern-btn-primary"
          bindtap="saveProcess"
          disabled="{{ !isFormValid || saving }}"
        >
          {{ saving ? '保存中...' : '保存' }}
        </button>
      </view>
    </view>
  </view>

  <!-- 删除确认弹窗 -->
  <view class="modal-overlay" wx:if="{{ showDeleteModal }}" bindtap="hideDeleteModal">
    <view class="modal-container delete-modal" catchtap="true">
      <view class="modal-header">
        <text class="modal-title">确认删除</text>
        <button class="modal-close" bindtap="hideDeleteModal">✕</button>
      </view>

      <view class="modal-body">
        <view class="delete-warning">
          <text class="warning-icon">⚠️</text>
          <view class="warning-text">
            <view>确定要删除工序"{{ deleteTarget.name }}"吗？</view>
            <view class="warning-note">此操作不可撤销</view>
          </view>
        </view>
      </view>

      <view class="modal-footer">
        <button class="modern-btn modern-btn-secondary" bindtap="hideDeleteModal">
          取消
        </button>
        <button
          class="modern-btn delete-confirm-btn"
          bindtap="confirmDelete"
          disabled="{{ deleting }}"
        >
          {{ deleting ? '删除中...' : '确认删除' }}
        </button>
      </view>
    </view>
  </view>
</view>
