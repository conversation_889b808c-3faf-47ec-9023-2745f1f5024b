<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ERP下单页面预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 375px;
            margin: 0 auto;
            background-color: #fff;
            min-height: 100vh;
            position: relative;
        }

        .header {
            background-color: #1976d2;
            color: white;
            padding: 20px 16px 16px;
            text-align: center;
            font-size: 18px;
            font-weight: 500;
        }

        .form-container {
            padding: 16px;
        }

        .form-section {
            background-color: #fff;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #e0e0e0;
        }

        .form-item {
            margin-bottom: 16px;
        }

        .form-item:last-child {
            margin-bottom: 0;
        }

        .label {
            display: block;
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }

        .input, .textarea, .picker-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            font-size: 14px;
            background-color: #fff;
        }

        .input:disabled {
            background-color: #f5f5f5;
            color: #999;
        }

        .picker-input {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
        }

        .picker-arrow {
            color: #999;
        }

        .quantity-control {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .quantity-btn {
            width: 32px;
            height: 32px;
            border: 1px solid #e0e0e0;
            background-color: #fff;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 18px;
            color: #666;
        }

        .quantity-btn:hover {
            background-color: #f5f5f5;
        }

        .quantity-input {
            width: 80px;
            text-align: center;
        }

        .total-section {
            background-color: #fff3e0;
            border: 1px solid #ffb74d;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }

        .total-amount {
            font-size: 18px;
            font-weight: 600;
            color: #e65100;
            text-align: center;
        }

        .button-group {
            display: flex;
            gap: 12px;
            padding: 16px;
            position: sticky;
            bottom: 0;
            background-color: #fff;
            border-top: 1px solid #e0e0e0;
        }

        .btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-cancel {
            background-color: #f5f5f5;
            color: #666;
        }

        .btn-submit {
            background-color: #1976d2;
            color: white;
        }

        .btn:hover {
            opacity: 0.8;
        }

        .packaging-section {
            border-left: 4px solid #ff9800;
        }

        .packaging-section .section-title {
            color: #ff9800;
        }

        .demo-badge {
            position: fixed;
            top: 10px;
            right: 10px;
            background-color: #4caf50;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="demo-badge">预览模式</div>
    
    <div class="container">
        <div class="header">
            📝 创建订单
        </div>

        <div class="form-container">
            <!-- 订单信息 -->
            <div class="form-section">
                <div class="section-title">📋 订单信息</div>
                <div class="form-item">
                    <label class="label">订单编号</label>
                    <input class="input" value="PO1704067200000" disabled>
                </div>
            </div>

            <!-- 客户信息 -->
            <div class="form-section">
                <div class="section-title">👤 客户信息</div>
                <div class="form-item">
                    <label class="label">客户名称 *</label>
                    <div class="picker-input">
                        <span>客户A</span>
                        <span class="picker-arrow">▼</span>
                    </div>
                </div>
                <div class="form-item">
                    <label class="label">联系人</label>
                    <input class="input" value="张先生" placeholder="请输入联系人">
                </div>
                <div class="form-item">
                    <label class="label">联系电话</label>
                    <input class="input" value="13800138001" placeholder="请输入联系电话">
                </div>
            </div>

            <!-- 产品信息 -->
            <div class="form-section">
                <div class="section-title">📦 产品信息</div>
                <div class="form-item">
                    <label class="label">产品名称 *</label>
                    <div class="picker-input">
                        <span>产品A</span>
                        <span class="picker-arrow">▼</span>
                    </div>
                </div>
                <div class="form-item">
                    <label class="label">产品编码</label>
                    <input class="input" value="PA001" disabled>
                </div>
                <div class="form-item">
                    <label class="label">产品规格</label>
                    <input class="input" value="标准规格" placeholder="请输入产品规格">
                </div>
                <div class="form-item">
                    <label class="label">订单数量 *</label>
                    <div class="quantity-control">
                        <div class="quantity-btn">-</div>
                        <input class="input quantity-input" value="100" type="number">
                        <div class="quantity-btn">+</div>
                        <span style="color: #666; font-size: 14px;">件</span>
                    </div>
                </div>
                <div class="form-item">
                    <label class="label">单价</label>
                    <input class="input" value="50.00" placeholder="请输入单价" type="number">
                </div>
            </div>

            <!-- 包装信息 -->
            <div class="form-section packaging-section">
                <div class="section-title">📦 包装信息</div>
                <div class="form-item">
                    <label class="label">包装类型</label>
                    <div class="picker-input">
                        <span>标准包装</span>
                        <span class="picker-arrow">▼</span>
                    </div>
                </div>
                <div class="form-item">
                    <label class="label">包装要求</label>
                    <textarea class="textarea" placeholder="请输入特殊包装要求" rows="3">常规纸箱包装，注意防潮</textarea>
                </div>
            </div>

            <!-- 交付信息 -->
            <div class="form-section">
                <div class="section-title">🚚 交付信息</div>
                <div class="form-item">
                    <label class="label">交付日期 *</label>
                    <input class="input" value="2024-01-15" type="date">
                </div>
                <div class="form-item">
                    <label class="label">优先级</label>
                    <div class="picker-input">
                        <span>普通</span>
                        <span class="picker-arrow">▼</span>
                    </div>
                </div>
                <div class="form-item">
                    <label class="label">交付地址</label>
                    <textarea class="textarea" placeholder="请输入交付地址" rows="2">上海市浦东新区张江高科技园区</textarea>
                </div>
            </div>

            <!-- 备注信息 -->
            <div class="form-section">
                <div class="section-title">📝 备注信息</div>
                <div class="form-item">
                    <label class="label">订单备注</label>
                    <textarea class="textarea" placeholder="请输入订单备注" rows="3">请按时交付，质量要求较高</textarea>
                </div>
            </div>

            <!-- 订单总额 -->
            <div class="total-section">
                <div class="total-amount">
                    订单总额：¥5,000.00
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="button-group">
            <button class="btn btn-cancel">取消</button>
            <button class="btn btn-submit">提交订单</button>
        </div>
    </div>

    <script>
        // 简单的交互演示
        document.querySelector('.btn-submit').addEventListener('click', function() {
            if (confirm('确定要提交此订单吗？')) {
                alert('订单创建成功！\n订单号：PO1704067200000\n是否立即创建工单？');
            }
        });

        document.querySelector('.btn-cancel').addEventListener('click', function() {
            if (confirm('确定要取消吗？未保存的数据将丢失。')) {
                alert('已取消订单创建');
            }
        });

        // 数量控制
        document.querySelector('.quantity-btn:first-child').addEventListener('click', function() {
            const input = document.querySelector('.quantity-input');
            const value = Math.max(1, parseInt(input.value) - 1);
            input.value = value;
            updateTotal();
        });

        document.querySelector('.quantity-btn:last-child').addEventListener('click', function() {
            const input = document.querySelector('.quantity-input');
            const value = parseInt(input.value) + 1;
            input.value = value;
            updateTotal();
        });

        function updateTotal() {
            const quantity = parseInt(document.querySelector('.quantity-input').value) || 0;
            const price = parseFloat(document.querySelector('input[placeholder="请输入单价"]').value) || 0;
            const total = (quantity * price).toFixed(2);
            document.querySelector('.total-amount').textContent = `订单总额：¥${total}`;
        }

        // 监听数量和单价变化
        document.querySelector('.quantity-input').addEventListener('input', updateTotal);
        document.querySelector('input[placeholder="请输入单价"]').addEventListener('input', updateTotal);
    </script>
</body>
</html>
