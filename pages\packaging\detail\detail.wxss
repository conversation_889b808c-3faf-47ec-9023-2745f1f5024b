/* 任务详情页面样式 - 简约现代风格 */
@import '../../../styles/modern-simple.wxss';

.page-container {
  background-color: #FAFAFA;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loading-text {
  font-size: 28rpx;
  color: #8E8E93;
}

/* 详情内容 */
.detail-content {
  padding: 20rpx;
}

/* 通用区块样式 */
.info-section,
.progress-section,
.production-section,
.assignment-section,
.time-section {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

/* 区块头部 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #F2F2F7;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1D1D1F;
}

/* 状态标签 */
.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
  color: white;
}

.status-badge.pending {
  background: #FF9500;
}

.status-badge.in_progress {
  background: #007AFF;
}

.status-badge.completed {
  background: #34C759;
}

.status-badge.paused {
  background: #FF3B30;
}

/* 信息网格 */
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 8rpx;
}

.info-value {
  font-size: 28rpx;
  color: #1D1D1F;
  font-weight: 500;
}

/* 进度区域 */
.progress-percentage {
  font-size: 32rpx;
  font-weight: 600;
  color: #007AFF;
}

.progress-bar {
  height: 16rpx;
  background: #F2F2F7;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.progress-fill {
  height: 100%;
  border-radius: 8rpx;
  transition: width 0.3s ease;
}

.progress-fill.pending {
  background: #FF9500;
}

.progress-fill.in_progress {
  background: #007AFF;
}

.progress-fill.completed {
  background: #34C759;
}

.progress-fill.paused {
  background: #FF3B30;
}

.progress-details {
  text-align: center;
}

.progress-text {
  font-size: 26rpx;
  color: #8E8E93;
}

/* 生产数据区域 */
.unit-text {
  font-size: 24rpx;
  color: #8E8E93;
}

.production-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 24rpx;
}

.production-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx;
  border-radius: 16rpx;
  border: 2rpx solid #F2F2F7;
}

.production-item.planned {
  background: #F8F9FA;
  border-color: #DEE2E6;
}

.production-item.processed {
  background: #E3F2FD;
  border-color: #2196F3;
}

.production-item.qualified {
  background: #E8F5E8;
  border-color: #4CAF50;
}

.production-item.defective {
  background: #FFEBEE;
  border-color: #F44336;
}

.production-label {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 8rpx;
}

.production-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #1D1D1F;
}

.quality-info {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
}

.quality-label {
  font-size: 26rpx;
  color: #8E8E93;
  margin-right: 8rpx;
}

.quality-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #34C759;
}

/* 分配信息 */
.assignment-info {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.assignment-item {
  display: flex;
  align-items: center;
}

.assignment-label {
  font-size: 26rpx;
  color: #8E8E93;
  margin-right: 16rpx;
  min-width: 120rpx;
}

.assignment-value {
  font-size: 28rpx;
  font-weight: 500;
}

.assignment-value.assigned {
  color: #34C759;
}

.assignment-value.unassigned {
  color: #FF9500;
}

/* 时间信息 */
.time-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.time-item {
  display: flex;
  flex-direction: column;
}

.time-label {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 8rpx;
}

.time-value {
  font-size: 26rpx;
  color: #1D1D1F;
  font-weight: 500;
}

/* 操作按钮区域 */
.action-section {
  display: flex;
  gap: 20rpx;
  padding: 0 20rpx;
  margin-top: 40rpx;
}

.action-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  color: white;
}

.action-btn::after {
  border: none;
}

.report-btn {
  background: #34C759;
}

.status-btn {
  background: #007AFF;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.report-modal,
.status-modal {
  background: white;
  border-radius: 20rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 2rpx solid #F2F2F7;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1D1D1F;
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background: #F2F2F7;
  border: none;
  font-size: 32rpx;
  color: #8E8E93;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close::after {
  border: none;
}

/* 报工弹窗内容 */
.report-content {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.report-item {
  margin-bottom: 32rpx;
}

.report-label {
  font-size: 28rpx;
  color: #1D1D1F;
  font-weight: 500;
  display: block;
  margin-bottom: 16rpx;
}

.report-input {
  width: 100%;
  padding: 24rpx;
  background: #F8F9FA;
  border: 2rpx solid #E9ECEF;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #1D1D1F;
}

.report-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 24rpx;
  background: #F8F9FA;
  border: 2rpx solid #E9ECEF;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #1D1D1F;
}

/* 状态弹窗内容 */
.status-content {
  padding: 32rpx;
}

.current-status {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
}

.current-label {
  font-size: 26rpx;
  color: #8E8E93;
  margin-right: 16rpx;
}

.current-value {
  font-size: 28rpx;
  font-weight: 500;
  color: #1D1D1F;
}

.status-select {
  margin-bottom: 40rpx;
}

.select-label {
  font-size: 28rpx;
  color: #1D1D1F;
  font-weight: 500;
  display: block;
  margin-bottom: 16rpx;
}

.picker-display {
  padding: 24rpx;
  background: #F8F9FA;
  border: 2rpx solid #E9ECEF;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #1D1D1F;
}

/* 弹窗操作按钮 */
.modal-actions {
  display: flex;
  gap: 20rpx;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
}

.cancel-btn {
  background: #F2F2F7;
  color: #8E8E93;
}

.confirm-btn {
  background: #007AFF;
  color: white;
}

.cancel-btn::after,
.confirm-btn::after {
  border: none;
}
