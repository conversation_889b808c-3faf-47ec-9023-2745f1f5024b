/* 订单详情页面样式 - 简约现代风格 */

/* 页面容器 */
.page-container {
  background-color: #F2F2F7;
  min-height: 100vh;
  padding: 24rpx;
}

/* 测试信息 */
.test-info {
  background-color: #FFFFFF;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
}

.test-info text {
  display: block;
  margin-bottom: 10rpx;
  font-size: 28rpx;
  color: #333;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #E5E5EA;
  border-top: 4rpx solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 24rpx;
  font-size: 28rpx;
  color: #8E8E93;
}

/* 详情内容 */
.detail-content {
  padding-bottom: 120rpx;
}

/* 详情卡片 */
.detail-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #E5E5EA;
}

/* 卡片头部 */
.card-header {
  margin-bottom: 24rpx;
}

.order-title {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.order-no {
  font-size: 36rpx;
  font-weight: 600;
  color: #1D1D1F;
  flex: 1;
}

.order-status {
  font-size: 24rpx;
  padding: 8rpx 24rpx;
  border-radius: 40rpx;
  font-weight: 500;
}

.order-status.pending {
  background-color: #FFF3CD;
  color: #856404;
}

.order-status.confirmed {
  background-color: #D4EDDA;
  color: #155724;
}

.order-status.processing {
  background-color: #CCE5FF;
  color: #004085;
}

.order-status.completed {
  background-color: #D1ECF1;
  color: #0C5460;
}

.order-status.cancelled {
  background-color: #F8D7DA;
  color: #721C24;
}

.order-meta {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.create-time,
.update-time {
  font-size: 24rpx;
  color: #8E8E93;
}

/* 卡片标题 */
.card-title {
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #E5E5EA;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #1D1D1F;
}

/* 卡片内容 */
.card-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 信息行 */
.info-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  min-height: 44rpx;
}

.info-label {
  font-size: 28rpx;
  color: #48484A;
  font-weight: 500;
  flex-shrink: 0;
  width: 160rpx;
}

.info-value {
  font-size: 28rpx;
  color: #1D1D1F;
  flex: 1;
  text-align: right;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* 特殊样式 */

.phone-number {
  color: #007AFF;
  text-decoration: underline;
}

.priority-high,
.priority-urgent {
  color: #FF3B30;
  font-weight: 500;
}

.priority-normal {
  color: #007AFF;
}

.priority-low {
  color: #8E8E93;
}

/* 操作按钮 */
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  padding: 24rpx;
  border-top: 1rpx solid #E5E5EA;
  display: flex;
  gap: 16rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.08);
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-btn.primary {
  background-color: #007AFF;
  color: #FFFFFF;
}

.action-btn.primary:active {
  background-color: #0056CC;
}

.action-btn.secondary {
  background-color: #F2F2F7;
  color: #007AFF;
  border: 1rpx solid #E5E5EA;
}

.action-btn.secondary:active {
  background-color: #E5E5EA;
}

.action-btn.danger {
  background-color: #FF3B30;
  color: #FFFFFF;
}

.action-btn.danger:active {
  background-color: #D70015;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
}

.modal-content {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E5EA;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1D1D1F;
}

.modal-close {
  font-size: 48rpx;
  color: #8E8E93;
  line-height: 1;
  padding: 8rpx;
}

.modal-body {
  padding: 32rpx;
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx 32rpx;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-btn.secondary {
  background-color: #F2F2F7;
  color: #48484A;
}

.modal-btn.danger {
  background-color: #FF3B30;
  color: #FFFFFF;
}

.modal-btn:disabled {
  opacity: 0.6;
}

/* 状态选择 */
.status-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.status-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background-color: #F2F2F7;
  border-radius: 12rpx;
  transition: all 0.2s ease;
}

.status-option:active {
  background-color: #E5E5EA;
}

.status-option.active {
  background-color: #E3F2FD;
  border: 1rpx solid #007AFF;
}

.status-label {
  font-size: 28rpx;
  color: #1D1D1F;
}

.status-check {
  font-size: 32rpx;
  color: #007AFF;
  font-weight: 600;
}

/* 删除警告 */
.delete-warning {
  font-size: 28rpx;
  color: #1D1D1F;
  margin-bottom: 16rpx;
  display: block;
}

.delete-note {
  font-size: 24rpx;
  color: #8E8E93;
  display: block;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #8E8E93;
  margin-bottom: 32rpx;
}

.btn {
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 32rpx;
}

.btn.btn-primary {
  background-color: #007AFF;
  color: #FFFFFF;
}
