<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>风格2 - 商务专业风格</title>
    <style>
        /*
        设计理念：商务专业风格
        - 色彩：深蓝色主色调，金色点缀，传统商务配色
        - 特点：正式、稳重、权威感强
        - 适用：大型企业、传统制造业、正式商务场合
        - 字体：衬线字体与无衬线字体结合
        - 圆角：较小圆角，正式感
        */
        
        :root {
            --primary-color: #1B365D;
            --secondary-color: #2E5984;
            --accent-color: #D4AF37;
            --success-color: #2E7D32;
            --warning-color: #F57C00;
            --error-color: #C62828;
            
            --text-primary: #1A1A1A;
            --text-secondary: #4A4A4A;
            --text-tertiary: #757575;
            --text-inverse: #FFFFFF;
            
            --bg-primary: #FFFFFF;
            --bg-secondary: #F8F9FA;
            --bg-tertiary: #E8EAF0;
            --bg-dark: #1B365D;
            
            --border-color: #D1D5DB;
            --border-dark: #374151;
            --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.12);
            --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
            --shadow-dark: 0 8px 24px rgba(27, 54, 93, 0.2);
            
            --radius-sm: 4px;
            --radius-md: 6px;
            --radius-lg: 8px;
            
            --spacing-xs: 8px;
            --spacing-sm: 12px;
            --spacing-md: 16px;
            --spacing-lg: 24px;
            --spacing-xl: 32px;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Times New Roman', Georgia, serif;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.6;
            width: 375px;
            margin: 0 auto;
            min-height: 100vh;
        }
        
        .container {
            background-color: var(--bg-secondary);
            min-height: 100vh;
        }
        
        /* 导航栏 */
        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            padding: var(--spacing-lg) var(--spacing-md);
            color: var(--text-inverse);
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: var(--shadow-dark);
        }
        
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-md);
        }
        
        .nav-title {
            font-size: 24px;
            font-weight: 700;
            color: var(--text-inverse);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }
        
        .nav-subtitle {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 400;
            margin-top: 2px;
        }
        
        .nav-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn {
            padding: var(--spacing-sm) var(--spacing-md);
            border: none;
            border-radius: var(--radius-md);
            font-size: 13px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .btn-primary {
            background-color: var(--accent-color);
            color: var(--text-primary);
            box-shadow: 0 2px 8px rgba(212, 175, 55, 0.3);
        }
        
        .btn-secondary {
            background-color: transparent;
            color: var(--text-inverse);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-medium);
        }
        
        /* 订单列表 */
        .order-list {
            padding: var(--spacing-md);
        }
        
        .order-card {
            background-color: var(--bg-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-md);
            box-shadow: var(--shadow-light);
            border: 1px solid var(--border-color);
            border-left: 4px solid var(--primary-color);
            transition: all 0.3s ease;
        }
        
        .order-card:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-medium);
            border-left-color: var(--accent-color);
        }
        
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--spacing-md);
            padding-bottom: var(--spacing-sm);
            border-bottom: 1px solid var(--border-color);
        }
        
        .order-number {
            font-size: 16px;
            font-weight: 700;
            color: var(--primary-color);
            font-family: 'Courier New', monospace;
        }
        
        .order-priority {
            font-size: 10px;
            color: var(--text-tertiary);
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-top: 2px;
        }
        
        .order-status {
            padding: 6px 12px;
            border-radius: var(--radius-sm);
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .status-pending {
            background-color: #FFF8E1;
            color: #E65100;
            border: 1px solid #FFB74D;
        }
        
        .status-confirmed {
            background-color: #E8F5E8;
            color: #2E7D32;
            border: 1px solid #81C784;
        }
        
        .order-section {
            margin-bottom: var(--spacing-md);
        }
        
        .section-title {
            font-size: 11px;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: var(--spacing-xs);
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .section-content {
            font-size: 14px;
            color: var(--text-primary);
            line-height: 1.5;
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .company-name {
            font-weight: 600;
            color: var(--primary-color);
        }
        
        .order-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: var(--spacing-md);
            border-top: 2px solid var(--bg-tertiary);
            font-size: 12px;
            color: var(--text-tertiary);
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .order-date {
            font-weight: 500;
        }
        
        .order-amount {
            font-size: 16px;
            font-weight: 700;
            color: var(--primary-color);
            font-family: 'Courier New', monospace;
        }
        
        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 375px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            padding: var(--spacing-sm) 0;
            display: flex;
            justify-content: space-around;
            box-shadow: 0 -4px 12px rgba(27, 54, 93, 0.2);
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: var(--spacing-xs);
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            font-size: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .nav-item.active {
            color: var(--accent-color);
        }
        
        .nav-icon {
            width: 20px;
            height: 20px;
            background-color: currentColor;
            border-radius: var(--radius-sm);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 导航栏 -->
        <div class="navbar">
            <div class="nav-header">
                <div>
                    <h1 class="nav-title">订单管理系统</h1>
                    <div class="nav-subtitle">Enterprise Resource Planning</div>
                </div>
                <div class="nav-actions">
                    <button class="btn btn-primary">新建</button>
                    <button class="btn btn-secondary">设置</button>
                </div>
            </div>
        </div>
        
        <!-- 订单列表 -->
        <div class="order-list">
            <div class="order-card">
                <div class="order-header">
                    <div>
                        <div class="order-number">PO20240101001</div>
                        <div class="order-priority">高优先级</div>
                    </div>
                    <div class="order-status status-pending">待确认</div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">客户信息</div>
                    <div class="section-content">
                        <div class="company-name">华东机械制造有限公司</div>
                        联系人：张工程师 | 电话：13800138000
                    </div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">产品规格</div>
                    <div class="section-content">
                        精密齿轮 (型号：GEAR001) × 100件<br>
                        规格：高精度传动齿轮，适用于各种机械设备
                    </div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">包装要求</div>
                    <div class="section-content">防潮包装 | 交期：3-5个工作日</div>
                </div>
                
                <div class="order-meta">
                    <span class="order-date">2024-01-15 09:30</span>
                    <span class="order-amount">￥15,000.00</span>
                </div>
            </div>
            
            <div class="order-card">
                <div class="order-header">
                    <div>
                        <div class="order-number">PO20240101002</div>
                        <div class="order-priority">标准优先级</div>
                    </div>
                    <div class="order-status status-confirmed">已确认</div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">客户信息</div>
                    <div class="section-content">
                        <div class="company-name">江南精工科技股份有限公司</div>
                        联系人：李经理 | 电话：13900139000
                    </div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">产品规格</div>
                    <div class="section-content">
                        不锈钢板材 (型号：STEEL002) × 50平方米<br>
                        规格：304不锈钢板材，厚度3mm
                    </div>
                </div>
                
                <div class="order-section">
                    <div class="section-title">包装要求</div>
                    <div class="section-content">标准包装 | 交期：2-3个工作日</div>
                </div>
                
                <div class="order-meta">
                    <span class="order-date">2024-01-14 14:20</span>
                    <span class="order-amount">￥4,275.00</span>
                </div>
            </div>
        </div>
        
        <!-- 底部导航 -->
        <div class="bottom-nav">
            <a href="#" class="nav-item">
                <div class="nav-icon"></div>
                <span>首页</span>
            </a>
            <a href="#" class="nav-item active">
                <div class="nav-icon"></div>
                <span>订单</span>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon"></div>
                <span>工单</span>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon"></div>
                <span>包装</span>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon"></div>
                <span>出入库</span>
            </a>
        </div>
    </div>
</body>
</html>
