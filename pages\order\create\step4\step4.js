// 订单创建第四步：确认提交
const OrderService = require('../../../../services/orderService.js')

Page({
  data: {
    // 订单数据
    orderData: {},
    selectedCustomer: {},
    selectedProducts: [],
    
    // 汇总数据
    totalQuantity: 0,
    maxEstimatedDays: 0,
    priorityLabel: '',
    
    // 工单创建选项
    createWorkOrder: true,
    
    // 提交状态
    submitting: false,
    showSuccessModal: false,
    
    // 优先级选项
    priorityOptions: [
      { label: '普通', value: 'normal' },
      { label: '紧急', value: 'urgent' },
      { label: '特急', value: 'emergency' },
      { label: '试制', value: 'trial' }
    ]
  },

  onLoad() {
    console.log('订单创建第四步页面加载')
    this.loadStepData()
    this.calculateSummary()
  },

  // 加载步骤数据
  loadStepData() {
    try {
      const stepData = wx.getStorageSync('orderStepData')
      if (stepData) {
        // 获取产品列表
        const selectedProducts = stepData.selectedProducts || []

        // 计算优先级标签
        const priorityLabel = this.getPriorityLabel(stepData.orderData?.priority || 'normal')

        this.setData({
          orderData: stepData.orderData || {},
          selectedCustomer: stepData.selectedCustomer || {},
          selectedProducts,
          priorityLabel
        })
      } else {
        wx.showToast({
          title: '数据加载失败',
          icon: 'none'
        })
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      }
    } catch (error) {
      console.error('加载步骤数据失败:', error)
    }
  },

  // 计算汇总信息
  calculateSummary() {
    const { selectedProducts } = this.data

    const totalQuantity = selectedProducts.length
    const maxEstimatedDays = Math.max(...selectedProducts.map(p => p.estimatedDays || 1))

    this.setData({
      totalQuantity,
      maxEstimatedDays
    })
  },

  // 获取优先级标签
  getPriorityLabel(priority) {
    const option = this.data.priorityOptions.find(p => p.value === priority)
    return option ? option.label : '普通'
  },

  // 编辑基本信息
  editBasicInfo() {
    wx.navigateTo({
      url: '/pages/order/create/step1/step1'
    })
  },

  // 编辑产品
  editProducts() {
    wx.navigateTo({
      url: '/pages/order/create/step2/step2'
    })
  },

  // 工单创建选项变化
  onCreateWorkOrderChange(e) {
    this.setData({
      createWorkOrder: e.detail.value.length > 0
    })
  },

  // 返回上一步
  goBack() {
    wx.navigateBack()
  },

  // 保存草稿
  saveDraft() {
    const draftData = {
      step: 4,
      orderData: this.data.orderData,
      selectedCustomer: this.data.selectedCustomer,
      selectedProducts: this.data.selectedProducts,
      createWorkOrder: this.data.createWorkOrder,
      timestamp: new Date().toISOString()
    }
    
    wx.setStorageSync('orderDraft', draftData)
    
    wx.showToast({
      title: '草稿已保存',
      icon: 'success'
    })
  },

  // 提交订单
  async submitOrder() {
    if (this.data.submitting) return
    
    this.setData({ submitting: true })
    
    try {
      // 构建订单提交数据
      const orderSubmitData = {
        ...this.data.orderData,
        customerId: this.data.selectedCustomer.id,
        customerName: this.data.selectedCustomer.name,
        products: this.data.selectedProducts.map(product => ({
          productId: product.id,
          productName: product.name,
          productCode: product.code,
          specification: product.specification,
          quantity: product.quantity,
          unit: product.unit,
          packaging: product.packaging,
          estimatedDays: product.estimatedDays,
          priority: product.priority || 'normal',
          specialRequirements: product.specialRequirements || ''
        })),
        totalQuantity: this.data.totalQuantity,
        maxEstimatedDays: this.data.maxEstimatedDays,
        createWorkOrder: this.data.createWorkOrder,
        status: 'pending',
        createdAt: new Date().toISOString()
      }
      
      // 提交订单
      await OrderService.createOrder(orderSubmitData)
      
      // 显示成功弹窗
      this.setData({ 
        submitting: false,
        showSuccessModal: true 
      })
      
      // 清除草稿和步骤数据
      wx.removeStorageSync('orderDraft')
      wx.removeStorageSync('orderStepData')
      
      // 如果选择创建工单，延迟创建
      if (this.data.createWorkOrder) {
        setTimeout(() => {
          this.createWorkOrderFromOrder(orderSubmitData)
        }, 2000)
      }
      
    } catch (error) {
      console.error('提交订单失败:', error)
      this.setData({ submitting: false })
      wx.showToast({
        title: '提交失败，请重试',
        icon: 'none'
      })
    }
  },



  // 从订单创建工单
  async createWorkOrderFromOrder(orderData) {
    try {
      const workOrders = []
      
      for (const product of orderData.products) {
        const workOrder = {
          id: `WO${Date.now()}_${product.productId}`,
          orderNo: orderData.orderNo,
          productId: product.productId,
          productName: product.productName,
          productCode: product.productCode,
          quantity: product.quantity,
          unit: product.unit,
          packaging: product.packaging,
          estimatedDays: product.estimatedDays,
          priority: product.priority,
          specialRequirements: product.specialRequirements,
          status: 'pending',
          createdAt: new Date().toISOString(),
          processes: [] // 工艺流程将在工单详情中配置
        }
        
        workOrders.push(workOrder)
      }
      
      // 保存工单到本地存储
      const existingWorkOrders = wx.getStorageSync('workOrders') || []
      existingWorkOrders.unshift(...workOrders)
      wx.setStorageSync('workOrders', existingWorkOrders)
      
      console.log('工单创建成功:', workOrders)
      
      wx.showToast({
        title: `已创建${workOrders.length}个工单`,
        icon: 'success'
      })
      
    } catch (error) {
      console.error('创建工单失败:', error)
    }
  },

  // 查看订单
  viewOrder() {
    wx.redirectTo({
      url: `/pages/order/detail/detail?orderNo=${this.data.orderData.orderNo}`
    })
  },

  // 继续创建订单
  createNewOrder() {
    wx.redirectTo({
      url: '/pages/order/create/step1/step1'
    })
  }
})
