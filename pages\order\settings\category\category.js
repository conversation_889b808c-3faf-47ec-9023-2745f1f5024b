// 产品分类管理页面
const CategoryService = require('../../../../services/categoryService')
const { formatDate } = require('../../../../utils/dateUtils')

Page({
  data: {
    loading: false,
    categories: [],
    stats: null,
    searchKeyword: '',
    
    // 弹窗相关
    showModal: false,
    isEditing: false,
    editingId: null,
    saving: false,
    formData: {
      name: '',
      description: ''
    },
    
    // 删除确认弹窗
    showDeleteModal: false,
    deleteTarget: {},
    deleting: false
  },

  onLoad() {
    wx.setNavigationBarTitle({
      title: '产品分类管理'
    })
    this.loadPageData()
  },

  onShow() {
    // 每次显示时刷新数据
    this.loadPageData()
  },

  onPullDownRefresh() {
    this.loadPageData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 加载页面数据
   */
  async loadPageData() {
    this.setData({ loading: true })
    
    try {
      await Promise.all([
        this.loadCategories(),
        this.loadStats()
      ])
    } catch (error) {
      console.error('加载页面数据失败:', error)
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 加载分类列表
   */
  async loadCategories() {
    try {
      const result = await CategoryService.getCategoryList()
      if (result.success) {
        // 格式化时间显示
        const categories = result.data.map(category => ({
          ...category,
          createdAt: formatDate(category.createdAt, 'MM-DD HH:mm'),
          updatedAt: formatDate(category.updatedAt, 'MM-DD HH:mm')
        }))
        
        this.setData({ categories })
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('加载分类列表失败:', error)
      wx.showToast({
        title: '加载分类失败',
        icon: 'none'
      })
    }
  },

  /**
   * 加载统计信息
   */
  async loadStats() {
    try {
      const result = await CategoryService.getCategoryStats()
      if (result.success) {
        this.setData({ stats: result.data })
      }
    } catch (error) {
      console.error('加载统计信息失败:', error)
    }
  },

  /**
   * 搜索输入处理
   */
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    })
  },

  /**
   * 搜索确认
   */
  async onSearchConfirm() {
    const { searchKeyword } = this.data
    this.setData({ loading: true })
    
    try {
      const result = await CategoryService.searchCategories(searchKeyword)
      if (result.success) {
        const categories = result.data.map(category => ({
          ...category,
          createdAt: formatDate(category.createdAt, 'MM-DD HH:mm'),
          updatedAt: formatDate(category.updatedAt, 'MM-DD HH:mm')
        }))
        
        this.setData({ categories })
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('搜索分类失败:', error)
      wx.showToast({
        title: '搜索失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 显示创建分类弹窗
   */
  showCreateModal() {
    this.setData({
      showModal: true,
      isEditing: false,
      editingId: null,
      formData: {
        name: '',
        description: ''
      }
    })
  },

  /**
   * 编辑分类
   */
  editCategory(e) {
    const category = e.currentTarget.dataset.category
    this.setData({
      showModal: true,
      isEditing: true,
      editingId: category.id,
      formData: {
        name: category.name,
        description: category.description || ''
      }
    })
  },

  /**
   * 隐藏弹窗
   */
  hideModal() {
    this.setData({
      showModal: false,
      isEditing: false,
      editingId: null,
      saving: false,
      formData: {
        name: '',
        description: ''
      }
    })
  },

  /**
   * 分类名称输入
   */
  onNameInput(e) {
    this.setData({
      'formData.name': e.detail.value
    })
  },

  /**
   * 分类描述输入
   */
  onDescriptionInput(e) {
    this.setData({
      'formData.description': e.detail.value
    })
  },

  /**
   * 保存分类
   */
  async saveCategory() {
    const { isEditing, editingId, formData } = this.data
    
    if (!formData.name.trim()) {
      wx.showToast({
        title: '请输入分类名称',
        icon: 'none'
      })
      return
    }

    this.setData({ saving: true })

    try {
      let result
      if (isEditing) {
        result = await CategoryService.updateCategory(editingId, formData)
      } else {
        result = await CategoryService.createCategory(formData)
      }

      if (result.success) {
        wx.showToast({
          title: result.message,
          icon: 'success'
        })
        
        this.hideModal()
        this.loadPageData()
      } else {
        wx.showToast({
          title: result.message,
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('保存分类失败:', error)
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      })
    } finally {
      this.setData({ saving: false })
    }
  },

  /**
   * 删除分类
   */
  deleteCategory(e) {
    const { id, name } = e.currentTarget.dataset
    this.setData({
      showDeleteModal: true,
      deleteTarget: { id, name }
    })
  },

  /**
   * 隐藏删除确认弹窗
   */
  hideDeleteModal() {
    this.setData({
      showDeleteModal: false,
      deleteTarget: {},
      deleting: false
    })
  },

  /**
   * 确认删除
   */
  async confirmDelete() {
    const { deleteTarget } = this.data
    
    this.setData({ deleting: true })

    try {
      const result = await CategoryService.deleteCategory(deleteTarget.id)
      
      if (result.success) {
        wx.showToast({
          title: result.message,
          icon: 'success'
        })
        
        this.hideDeleteModal()
        this.loadPageData()
      } else {
        wx.showToast({
          title: result.message,
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('删除分类失败:', error)
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      })
    } finally {
      this.setData({ deleting: false })
    }
  },

  /**
   * 查看分类详情
   */
  viewCategoryDetail(e) {
    const category = e.currentTarget.dataset.category
    wx.showModal({
      title: '分类详情',
      content: `名称：${category.name}\n描述：${category.description || '暂无描述'}\n创建时间：${category.createdAt}`,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack()
  }
})
