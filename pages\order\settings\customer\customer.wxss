/* 生产管理系统 - 客户管理页面样式 - 简约现代风格 */
@import '../../../../styles/design-tokens.wxss';
@import '../../../../styles/modern-components.wxss';

.page-container {
  min-height: 100vh;
  background-color: var(--bg-page);
}

/* 页面头部 */
.page-header {
  background-color: var(--bg-primary);
  padding: var(--spacing-base);
  border-bottom: var(--border);
}

.btn {
  width: 100%;
  height: var(--btn-height-base);
  border-radius: var(--radius-base);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-base);
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:active {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: var(--border);
}

.btn-icon {
  font-size: var(--font-size-lg);
}

/* 客户列表 */
.customer-list {
  padding: var(--spacing-base);
}

.customer-card {
  background-color: var(--bg-primary);
  border-radius: var(--card-border-radius);
  padding: var(--card-padding);
  margin-bottom: var(--spacing-base);
  box-shadow: var(--card-shadow);
  border: var(--border);
}

.customer-info {
  margin-bottom: var(--spacing-base);
}

.customer-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.customer-contact {
  display: flex;
  align-items: center;
  gap: var(--spacing-base);
  margin-bottom: var(--spacing-xs);
}

.contact-person {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
}

.contact-phone {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
}

.customer-address,
.customer-email {
  margin-bottom: var(--spacing-xs);
}

.address-text,
.email-text {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
}

.customer-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.action-btn {
  flex: 1;
  height: var(--btn-height-sm);
  border-radius: var(--radius-base);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  border: none;
  transition: all var(--transition-base);
}

.action-btn.secondary {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: var(--border);
}

.action-btn.danger {
  background-color: var(--error-light);
  color: var(--error-color);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4xl);
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: var(--spacing-base);
}

.empty-text {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.empty-hint {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
}

/* 表单弹窗 */
.form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: var(--z-index-modal);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-base);
}

.form-container {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xl);
  border-bottom: var(--border);
}

.form-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: var(--radius-full);
  background-color: var(--bg-secondary);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
  color: var(--text-tertiary);
}

.form-content {
  flex: 1;
  padding: var(--spacing-xl);
  overflow-y: auto;
}

.form-group {
  margin-bottom: var(--spacing-xl);
}

.form-label {
  display: block;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.form-input {
  width: 100%;
  height: var(--input-height);
  padding: 0 var(--input-padding);
  border: var(--border);
  border-radius: var(--input-border-radius);
  font-size: var(--font-size-base);
  color: var(--text-primary);
  background-color: var(--bg-primary);
}

.form-input:focus {
  border-color: var(--primary-color);
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: var(--input-padding);
  border: var(--border);
  border-radius: var(--input-border-radius);
  font-size: var(--font-size-base);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  resize: vertical;
}

.form-textarea:focus {
  border-color: var(--primary-color);
}

.form-actions {
  display: flex;
  gap: var(--spacing-base);
  padding: var(--spacing-xl);
  border-top: var(--border);
}

.form-actions .btn {
  flex: 1;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: var(--z-index-modal);
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  background-color: var(--bg-primary);
  padding: var(--spacing-2xl);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
}

.loading-text {
  font-size: var(--font-size-base);
  color: var(--text-primary);
}
