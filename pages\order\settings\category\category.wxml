<!--产品分类管理页面 - 简约现代风格-->
<view class="modern-page">
  <!-- 页面头部 -->
  <view class="modern-navbar">
    <view class="header-content">
      <text class="modern-navbar-title">产品分类管理</text>
      <text class="page-subtitle">管理产品分类和标签</text>
    </view>
    <view class="modern-navbar-actions">
      <button class="modern-btn modern-btn-primary modern-btn-small" bindtap="showCreateModal">
        新建分类
      </button>
    </view>
  </view>

  <view class="modern-container">
    <!-- 搜索栏 -->
    <view class="search-container">
      <view class="modern-input-wrapper">
        <input
          class="modern-input"
          placeholder="搜索分类名称或描述"
          value="{{ searchKeyword }}"
          bindinput="onSearchInput"
          bindconfirm="onSearchConfirm"
        />
        <view class="search-icon">🔍</view>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-section" wx:if="{{ stats }}">
      <view class="modern-card stats-card">
        <view class="stats-grid">
          <view class="stat-item">
            <text class="stat-number">{{ stats.total }}</text>
            <text class="stat-label">总分类数</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ stats.recentlyAdded }}</text>
            <text class="stat-label">本周新增</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 分类列表 -->
    <view class="categories-section">
      <view class="modern-section">
        <view class="modern-section-title">
          <text class="modern-section-icon">🏷️</text>
          <text class="modern-section-text">分类列表</text>
          <view class="category-count">{{ categories.length }}</view>
        </view>

        <!-- 分类卡片列表 -->
        <view class="category-list" wx:if="{{ categories.length > 0 }}">
          <view
            class="modern-card category-card"
            wx:for="{{ categories }}"
            wx:key="id"
            bindtap="viewCategoryDetail"
            data-category="{{ item }}"
          >
            <view class="category-header">
              <view class="category-info">
                <text class="category-name">{{ item.name }}</text>
                <text class="category-id">ID: {{ item.id }}</text>
              </view>
              <view class="category-actions">
                <button
                  class="action-btn edit-btn"
                  bindtap="editCategory"
                  data-category="{{ item }}"
                  catchtap="true"
                >
                  ✏️
                </button>
                <button
                  class="action-btn delete-btn"
                  bindtap="deleteCategory"
                  data-id="{{ item.id }}"
                  data-name="{{ item.name }}"
                  catchtap="true"
                >
                  🗑️
                </button>
              </view>
            </view>

            <view class="category-body">
              <text class="category-description">
                {{ item.description || '暂无描述' }}
              </text>
            </view>

            <view class="category-footer">
              <text class="category-time">
                创建时间：{{ item.createdAt }}
              </text>
              <view class="modern-tag modern-tag-neutral" wx:if="{{ item.updatedAt !== item.createdAt }}">
                已更新
              </view>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view class="modern-empty" wx:if="{{ categories.length === 0 && !loading }}">
          <text class="modern-empty-icon">📂</text>
          <view class="modern-empty-text">
            <view>暂无产品分类</view>
            <view>点击"新建分类"开始添加</view>
          </view>
        </view>

        <!-- 加载状态 -->
        <view class="modern-loading" wx:if="{{ loading }}">
          <text class="modern-loading-text">加载中...</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 创建/编辑分类弹窗 -->
  <view class="modal-overlay" wx:if="{{ showModal }}" bindtap="hideModal">
    <view class="modal-container" catchtap="true">
      <view class="modal-header">
        <text class="modal-title">{{ isEditing ? '编辑分类' : '新建分类' }}</text>
        <button class="modal-close" bindtap="hideModal">✕</button>
      </view>

      <view class="modal-body">
        <view class="form-group">
          <text class="form-label">分类名称 *</text>
          <input
            class="modern-input"
            placeholder="请输入分类名称"
            value="{{ formData.name }}"
            bindinput="onNameInput"
            maxlength="50"
          />
        </view>

        <view class="form-group">
          <text class="form-label">分类描述</text>
          <textarea
            class="modern-textarea"
            placeholder="请输入分类描述（可选）"
            value="{{ formData.description }}"
            bindinput="onDescriptionInput"
            maxlength="200"
            auto-height
          />
        </view>
      </view>

      <view class="modal-footer">
        <button class="modern-btn modern-btn-secondary" bindtap="hideModal">
          取消
        </button>
        <button
          class="modern-btn modern-btn-primary"
          bindtap="saveCategory"
          disabled="{{ !formData.name.trim() || saving }}"
        >
          {{ saving ? '保存中...' : '保存' }}
        </button>
      </view>
    </view>
  </view>

  <!-- 删除确认弹窗 -->
  <view class="modal-overlay" wx:if="{{ showDeleteModal }}" bindtap="hideDeleteModal">
    <view class="modal-container delete-modal" catchtap="true">
      <view class="modal-header">
        <text class="modal-title">确认删除</text>
        <button class="modal-close" bindtap="hideDeleteModal">✕</button>
      </view>

      <view class="modal-body">
        <view class="delete-warning">
          <text class="warning-icon">⚠️</text>
          <view class="warning-text">
            <view>确定要删除分类"{{ deleteTarget.name }}"吗？</view>
            <view class="warning-note">此操作不可撤销</view>
          </view>
        </view>
      </view>

      <view class="modal-footer">
        <button class="modern-btn modern-btn-secondary" bindtap="hideDeleteModal">
          取消
        </button>
        <button
          class="modern-btn delete-confirm-btn"
          bindtap="confirmDelete"
          disabled="{{ deleting }}"
        >
          {{ deleting ? '删除中...' : '确认删除' }}
        </button>
      </view>
    </view>
  </view>
</view>
