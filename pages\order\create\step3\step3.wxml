<!--订单创建第三步：配置详情-->
<view class="step-container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <view class="navbar-left" bindtap="goBack">
        <text class="back-icon">‹</text>
        <text class="back-text">上一步</text>
      </view>
      <text class="navbar-title">创建订单 - 配置详情</text>
      <view class="navbar-right"></view>
    </view>
  </view>

  <!-- 步骤指示器 -->
  <view class="step-indicator">
    <view class="step-item">
      <view class="step-number completed">✓</view>
      <text class="step-text">基本信息</text>
    </view>
    <view class="step-line completed"></view>
    <view class="step-item">
      <view class="step-number completed">✓</view>
      <text class="step-text">选择产品</text>
    </view>
    <view class="step-line completed"></view>
    <view class="step-item active">
      <view class="step-number">3</view>
      <text class="step-text">配置详情</text>
    </view>
    <view class="step-line"></view>
    <view class="step-item">
      <view class="step-number">4</view>
      <text class="step-text">确认提交</text>
    </view>
  </view>

  <!-- 产品配置列表 -->
  <view class="config-container">
    <scroll-view class="config-list" scroll-y>
      <view 
        class="config-item"
        wx:for="{{ selectedProducts }}"
        wx:key="id"
      >
        <!-- 产品信息头部 -->
        <view class="product-header">
          <view class="product-info">
            <text class="product-name">{{ item.name }}</text>
            <text class="product-code">{{ item.code }}</text>
          </view>
          <view class="product-quantity">
            <text class="quantity-text">数量：{{ item.quantity }} {{ item.unit }}</text>
          </view>
        </view>

        <!-- 配置表单 -->
        <view class="config-form">
          <!-- 包装配置 -->
          <view class="form-group">
            <text class="form-label">包装类型 <text class="required">*</text></text>
            <picker
              range="{{ packagingOptions }}"
              range-key="name"
              value="{{ item.packagingIndex || 0 }}"
              bindchange="onPackagingChange"
              data-id="{{ item.id }}"
            >
              <view class="picker-input">
                <text class="picker-text {{ !item.packaging ? 'placeholder' : '' }}">
                  {{ item.packaging ? item.packaging.name : '请选择包装类型' }}
                </text>
                <text class="picker-arrow">▼</text>
              </view>
            </picker>
          </view>

          <!-- 预计生产天数 -->
          <view class="form-group">
            <text class="form-label">预计生产天数</text>
            <view class="days-control">
              <button 
                class="days-btn"
                bindtap="decreaseDays"
                data-id="{{ item.id }}"
                disabled="{{ item.estimatedDays <= 1 }}"
              >-</button>
              <input 
                class="days-input"
                type="number"
                value="{{ item.estimatedDays }}"
                bindinput="onDaysInput"
                data-id="{{ item.id }}"
                min="1"
                max="365"
              />
              <button 
                class="days-btn"
                bindtap="increaseDays"
                data-id="{{ item.id }}"
              >+</button>
              <text class="days-unit">天</text>
            </view>
          </view>

          <!-- 优先级 -->
          <view class="form-group">
            <text class="form-label">生产优先级</text>
            <picker
              range="{{ priorityOptions }}"
              range-key="label"
              value="{{ item.priorityIndex || 0 }}"
              bindchange="onPriorityChange"
              data-id="{{ item.id }}"
            >
              <view class="picker-input">
                <text class="picker-text">{{ priorityOptions[item.priorityIndex || 0].label }}</text>
                <text class="picker-arrow">▼</text>
              </view>
            </picker>
          </view>

          <!-- 特殊要求 -->
          <view class="form-group">
            <text class="form-label">特殊要求</text>
            <textarea
              class="form-textarea"
              value="{{ item.specialRequirements }}"
              placeholder="请输入特殊生产要求（可选）"
              bindinput="onRequirementsInput"
              data-id="{{ item.id }}"
              maxlength="200"
            />
            <view class="char-count">{{ item.requirementsLength || 0 }}/200</view>
          </view>

          <!-- 错误提示 -->
          <view class="error-message" wx:if="{{ item.quantityError }}">
            <text class="error-text">{{ item.quantityError }}</text>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{ selectedProducts.length === 0 }}">
        <text class="empty-icon">📋</text>
        <text class="empty-text">暂无产品配置</text>
        <text class="empty-desc">请返回上一步选择产品</text>
      </view>
    </scroll-view>
  </view>

  <!-- 批量操作 -->
  <view class="batch-actions" wx:if="{{ selectedProducts.length > 1 }}">
    <view class="batch-header">
      <text class="batch-title">批量操作</text>
    </view>
    <view class="batch-buttons">
      <button class="batch-btn" bindtap="batchSetPackaging">统一包装</button>
      <button class="batch-btn" bindtap="batchSetDays">统一天数</button>
      <button class="batch-btn" bindtap="batchSetPriority">统一优先级</button>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <button class="btn-secondary" bindtap="addMoreProducts">继续添加产品</button>
    <button 
      class="btn-primary" 
      bindtap="nextStep" 
      disabled="{{ !isFormValid }}"
    >
      下一步：确认提交
    </button>
  </view>
</view>
