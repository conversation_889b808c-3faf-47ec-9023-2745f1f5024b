/**
 * 包装管理服务
 * 提供包装信息的增删改查功能
 */

// 模拟包装数据
let mockPackagings = [
  {
    id: 'packaging_001',
    name: '标准纸箱包装',
    type: '标准包装',
    description: '常规纸箱包装，适用于大部分产品',
    specifications: '尺寸：40×30×20cm，材质：五层瓦楞纸，承重：20kg',
    notes: '环保材料，可回收利用',
    createdAt: '2024-01-01T08:00:00.000Z',
    updatedAt: '2024-01-01T08:00:00.000Z'
  },
  {
    id: 'packaging_002',
    name: '防潮膜包装',
    type: '防潮包装',
    description: '防潮膜+纸箱包装，适用于精密零件',
    specifications: '防潮膜厚度：0.1mm，防潮等级：IP54，纸箱规格：35×25×15cm',
    notes: '适用于金属零件，防止氧化',
    createdAt: '2024-01-02T09:15:00.000Z',
    updatedAt: '2024-01-02T09:15:00.000Z'
  },
  {
    id: 'packaging_003',
    name: '出口木箱包装',
    type: '木箱包装',
    description: '出口木箱包装，符合国际运输标准',
    specifications: '尺寸：100×80×60cm，材质：免熏蒸胶合板，承重：500kg',
    notes: '符合ISPM15标准，可直接出口',
    createdAt: '2024-01-03T11:30:00.000Z',
    updatedAt: '2024-01-03T11:30:00.000Z'
  },
  {
    id: 'packaging_004',
    name: '托盘缠绕膜包装',
    type: '托盘包装',
    description: '托盘+缠绕膜包装，便于叉车作业',
    specifications: '托盘尺寸：120×100cm，缠绕膜厚度：25μm，承重：1000kg',
    notes: '适用于批量货物，便于机械化装卸',
    createdAt: '2024-01-04T14:20:00.000Z',
    updatedAt: '2024-01-04T14:20:00.000Z'
  },
  {
    id: 'packaging_005',
    name: '防静电包装',
    type: '定制包装',
    description: '防静电包装，适用于电子元件',
    specifications: '防静电袋+防静电泡沫+纸箱，表面电阻：10^6-10^9Ω',
    notes: '严格按照电子元件包装标准执行',
    createdAt: '2024-01-05T16:45:00.000Z',
    updatedAt: '2024-01-05T16:45:00.000Z'
  }
]

/**
 * 生成唯一ID
 */
function generateId() {
  return 'packaging_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

/**
 * 获取包装列表
 */
function getPackagingList(params = {}) {
  return new Promise((resolve) => {
    setTimeout(() => {
      let result = [...mockPackagings]
      
      // 搜索过滤
      if (params.keyword) {
        const keyword = params.keyword.toLowerCase()
        result = result.filter(packaging => 
          packaging.name.toLowerCase().includes(keyword) ||
          packaging.type.toLowerCase().includes(keyword) ||
          (packaging.description && packaging.description.toLowerCase().includes(keyword))
        )
      }
      
      // 类型过滤
      if (params.type) {
        result = result.filter(packaging => packaging.type === params.type)
      }
      
      // 排序
      result.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      
      resolve(result)
    }, 300)
  })
}

/**
 * 根据ID获取包装信息
 */
function getPackagingById(id) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const packaging = mockPackagings.find(item => item.id === id)
      if (packaging) {
        resolve(packaging)
      } else {
        reject(new Error('包装不存在'))
      }
    }, 200)
  })
}

/**
 * 创建包装
 */
function createPackaging(packagingData) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      // 验证必填字段
      if (!packagingData.name || !packagingData.type) {
        reject(new Error('包装名称和类型为必填项'))
        return
      }
      
      // 检查包装名称是否重复
      const exists = mockPackagings.some(packaging => packaging.name === packagingData.name)
      if (exists) {
        reject(new Error('包装名称已存在'))
        return
      }
      
      const newPackaging = {
        id: generateId(),
        name: packagingData.name,
        type: packagingData.type,
        description: packagingData.description || '',
        specifications: packagingData.specifications || '',
        notes: packagingData.notes || '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      mockPackagings.unshift(newPackaging)
      resolve(newPackaging)
    }, 500)
  })
}

/**
 * 更新包装信息
 */
function updatePackaging(id, packagingData) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockPackagings.findIndex(packaging => packaging.id === id)
      if (index === -1) {
        reject(new Error('包装不存在'))
        return
      }
      
      // 验证必填字段
      if (!packagingData.name || !packagingData.type) {
        reject(new Error('包装名称和类型为必填项'))
        return
      }
      
      // 检查包装名称是否重复（排除自己）
      const exists = mockPackagings.some(packaging => 
        packaging.name === packagingData.name && packaging.id !== id
      )
      if (exists) {
        reject(new Error('包装名称已存在'))
        return
      }
      
      const updatedPackaging = {
        ...mockPackagings[index],
        name: packagingData.name,
        type: packagingData.type,
        description: packagingData.description || '',
        specifications: packagingData.specifications || '',
        notes: packagingData.notes || '',
        updatedAt: new Date().toISOString()
      }
      
      mockPackagings[index] = updatedPackaging
      resolve(updatedPackaging)
    }, 500)
  })
}

/**
 * 删除包装
 */
function deletePackaging(id) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockPackagings.findIndex(packaging => packaging.id === id)
      if (index === -1) {
        reject(new Error('包装不存在'))
        return
      }
      
      const deletedPackaging = mockPackagings.splice(index, 1)[0]
      resolve(deletedPackaging)
    }, 300)
  })
}

/**
 * 获取包装选项列表（用于下拉选择）
 */
function getPackagingOptions() {
  return new Promise((resolve) => {
    setTimeout(() => {
      const options = mockPackagings.map(packaging => ({
        value: packaging.id,
        label: packaging.name,
        type: packaging.type,
        description: packaging.description
      }))
      resolve(options)
    }, 200)
  })
}

/**
 * 获取包装类型列表
 */
function getPackagingTypes() {
  return new Promise((resolve) => {
    setTimeout(() => {
      const types = [...new Set(mockPackagings.map(packaging => packaging.type).filter(Boolean))]
      resolve(types)
    }, 200)
  })
}

module.exports = {
  getPackagingList,
  getPackagingById,
  createPackaging,
  updatePackaging,
  deletePackaging,
  getPackagingOptions,
  getPackagingTypes
}
