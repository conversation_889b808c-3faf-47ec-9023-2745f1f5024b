# 简约现代风格设计指南

## 概述

本文档定义了生产管理系统小程序的简约现代风格设计规范，确保所有页面和组件保持一致的视觉体验。

## 设计理念

### 核心原则
- **简约至上**：去除不必要的装饰，专注于内容和功能
- **现代感**：使用当代设计语言，符合用户期待
- **一致性**：统一的视觉语言和交互模式
- **可用性**：优先考虑用户体验和操作效率

### 设计特征
- 大量留白，营造呼吸感
- 柔和的圆角设计
- 微妙的阴影效果
- 清晰的层次结构
- 优雅的动画过渡

## 色彩系统

### 主色调
- **主色**：`#007AFF` - 现代蓝色，传达专业和信任
- **主色深色**：`#0056CC` - 用于按钮按下状态
- **主色浅色**：`#E3F2FD` - 用于背景和标签
- **主色超浅色**：`#F0F8FF` - 用于微妙的背景色

### 中性色
- **主要文本**：`#1D1D1F` - 深灰色，确保可读性
- **次要文本**：`#48484A` - 中灰色，用于辅助信息
- **三级文本**：`#8E8E93` - 浅灰色，用于提示文本
- **反色文本**：`#FFFFFF` - 白色，用于深色背景

### 背景色
- **主背景**：`#FFFFFF` - 纯白色，用于卡片和容器
- **次要背景**：`#F2F2F7` - 浅灰色，用于页面背景
- **边框色**：`#E5E5EA` - 极浅灰色，用于分割线

### 状态色
- **成功色**：`#34C759` - 绿色
- **警告色**：`#FF9500` - 橙色
- **错误色**：`#FF3B30` - 红色

## 字体系统

### 字号规范
- **超大标题**：48rpx - 用于页面主标题
- **大标题**：36rpx - 用于区块标题
- **中标题**：32rpx - 用于卡片标题
- **正文**：28rpx - 用于正文内容
- **小字**：24rpx - 用于辅助信息
- **超小字**：20rpx - 用于标签和提示

### 字重规范
- **粗体**：700 - 用于重要标题
- **半粗体**：600 - 用于次要标题
- **中等**：500 - 用于按钮文字
- **常规**：400 - 用于正文内容

## 间距系统

### 基础间距
- **超小间距**：8rpx
- **小间距**：12rpx
- **基础间距**：16rpx
- **中等间距**：24rpx
- **大间距**：32rpx
- **超大间距**：48rpx
- **巨大间距**：64rpx

### 页面间距
- **页面边距**：32rpx
- **区块间距**：24rpx
- **内容最大宽度**：750rpx

## 圆角系统

### 圆角规范
- **超小圆角**：6rpx - 用于小元素
- **小圆角**：8rpx - 用于标签
- **基础圆角**：12rpx - 用于输入框
- **大圆角**：16rpx - 用于按钮
- **超大圆角**：20rpx - 用于卡片
- **巨大圆角**：24rpx - 用于大容器
- **特大圆角**：32rpx - 用于特殊容器

## 阴影系统

### 阴影层级
- **微阴影**：`0 1rpx 2rpx rgba(0, 0, 0, 0.05)` - 用于微妙的层次
- **小阴影**：`0 2rpx 4rpx rgba(0, 0, 0, 0.06)` - 用于按钮
- **基础阴影**：`0 4rpx 8rpx rgba(0, 0, 0, 0.08)` - 用于卡片
- **大阴影**：`0 8rpx 16rpx rgba(0, 0, 0, 0.1)` - 用于弹窗
- **超大阴影**：`0 16rpx 32rpx rgba(0, 0, 0, 0.12)` - 用于浮层
- **卡片阴影**：`0 2rpx 12rpx rgba(0, 122, 255, 0.08)` - 带品牌色的阴影

## 组件规范

### 按钮组件
- **高度**：88rpx（基础）、64rpx（小）、104rpx（大）
- **内边距**：32rpx（水平）
- **圆角**：16rpx
- **阴影**：微阴影到小阴影
- **动画**：0.2s 缓动过渡

### 卡片组件
- **内边距**：32rpx
- **圆角**：20rpx
- **阴影**：卡片阴影
- **顶部装饰**：6rpx 高的渐变条
- **间距**：32rpx（底部）

### 输入框组件
- **高度**：88rpx
- **内边距**：24rpx
- **圆角**：16rpx
- **边框**：2rpx 实线
- **聚焦效果**：边框变色 + 外发光

### 标签组件
- **内边距**：8rpx 16rpx
- **圆角**：16rpx
- **字体**：20rpx 中等字重
- **大写**：是
- **字间距**：1rpx

## 动画系统

### 过渡时间
- **快速**：0.15s - 用于微交互
- **基础**：0.2s - 用于常规过渡
- **慢速**：0.3s - 用于复杂动画
- **平滑**：0.4s - 用于页面切换

### 缓动函数
- **标准缓动**：`cubic-bezier(0.4, 0, 0.2, 1)`
- **回弹缓动**：`cubic-bezier(0.34, 1.56, 0.64, 1)`
- **弹性缓动**：`cubic-bezier(0.175, 0.885, 0.32, 1.275)`

### 交互动画
- **按钮按下**：轻微缩放（0.98）+ 向下移动（2rpx）
- **卡片悬停**：向上移动（-4rpx）+ 阴影增强
- **页面切换**：淡入淡出 + 轻微位移

## 布局规范

### 页面结构
```
页面容器 (modern-page)
├── 导航栏 (modern-navbar)
├── 内容容器 (modern-container)
│   ├── 卡片组件 (modern-card)
│   ├── 列表组件 (modern-list)
│   └── 区块组件 (modern-section)
└── 底部间距
```

### 响应式设计
- **基准宽度**：375px
- **最大内容宽度**：750rpx
- **自适应间距**：根据屏幕宽度调整
- **触摸友好**：最小点击区域 88rpx

## 图标系统

### 图标风格
- **风格**：线性图标为主，填充图标为辅
- **粗细**：2rpx 线宽
- **大小**：48rpx（标准）、32rpx（小）、64rpx（大）
- **颜色**：继承文本颜色，透明度 0.8

### 常用图标
- **新建**：✨ - 现代感的星星图标
- **设置**：⚙️ - 齿轮图标
- **客户**：🏢 - 建筑图标
- **产品**：🔧 - 工具图标
- **包装**：📦 - 包裹图标
- **提示**：💡 - 灯泡图标

## 使用指南

### 开发规范
1. **导入样式**：所有页面必须导入 `modern-components.wxss`
2. **使用类名**：优先使用 `modern-` 前缀的组件类
3. **遵循层级**：按照设计Token系统使用变量
4. **保持一致**：新组件必须符合现有设计规范

### 质量检查
- [ ] 色彩使用是否符合规范
- [ ] 间距是否使用设计Token
- [ ] 圆角是否统一
- [ ] 阴影效果是否合适
- [ ] 动画过渡是否流畅
- [ ] 响应式适配是否正确

## 更新记录

### v1.0.0 (2024-01-01)
- 初始版本发布
- 定义基础设计系统
- 创建组件库
- 建立设计规范

---

*本设计指南将随着产品迭代持续更新，确保设计系统的完整性和一致性。*
