/**
 * ERP小程序统一设计Token系统 - 简约现代风格
 * 所有页面必须使用这些变量，确保设计一致性
 */

/* ==================== 色彩系统 ==================== */
/* 微信小程序CSS变量定义 */
page, .modern-page, .modern-container, .modern-card, .modern-btn, .modern-tag, .modern-section, .modern-list, .modern-navbar {
  /* 主色调 - 简约现代蓝色 */
  --primary-color: #007AFF;
  --primary-dark: #0056CC;
  --primary-light: #E3F2FD;
  --primary-ultra-light: #F0F8FF;

  /* 成功色 */
  --success-color: #34C759;
  --success-light: #E8F5E9;

  /* 警告色 */
  --warning-color: #FF9500;
  --warning-light: #FFF3E0;

  /* 错误色 */
  --error-color: #FF3B30;
  --error-light: #FFEBEE;

  /* 中性色 - 简约现代灰度 */
  --text-primary: #1D1D1F;
  --text-secondary: #48484A;
  --text-tertiary: #8E8E93;
  --text-quaternary: #C7C7CC;
  --text-disabled: #C7C7CC;
  --text-inverse: #FFFFFF;

  /* 背景色 - 简约现代背景 */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F2F2F7;
  --bg-tertiary: #E5E5EA;
  --bg-page: #F2F2F7;
  --border-color: #E5E5EA;

  /* ==================== 字体系统 ==================== */
  /* 字体大小 */
  --font-size-xs: 20rpx;
  --font-size-sm: 24rpx;
  --font-size-base: 28rpx;
  --font-size-lg: 32rpx;
  --font-size-xl: 36rpx;
  --font-size-2xl: 40rpx;
  --font-size-3xl: 48rpx;

  /* 字体粗细 */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* 行高 */
  --line-height-tight: 1.2;
  --line-height-normal: 1.4;
  --line-height-relaxed: 1.6;

  /* ==================== 间距系统 ==================== */
  /* 内边距 */
  --spacing-xs: 8rpx;
  --spacing-sm: 12rpx;
  --spacing-base: 16rpx;
  --spacing-lg: 20rpx;
  --spacing-xl: 24rpx;
  --spacing-2xl: 32rpx;
  --spacing-3xl: 40rpx;
  --spacing-4xl: 48rpx;

  /* 外边距 */
  --margin-xs: 8rpx;
  --margin-sm: 12rpx;
  --margin-base: 16rpx;
  --margin-lg: 20rpx;
  --margin-xl: 24rpx;
  --margin-2xl: 32rpx;

  /* ==================== 圆角系统 - 简约现代圆角 ==================== */
  --radius-xs: 6rpx;
  --radius-sm: 8rpx;
  --radius-base: 12rpx;
  --radius-lg: 16rpx;
  --radius-xl: 20rpx;
  --radius-2xl: 24rpx;
  --radius-3xl: 32rpx;
  --radius-full: 50%;

  /* ==================== 阴影系统 - 简约现代阴影 ==================== */
  --shadow-xs: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 2rpx 4rpx rgba(0, 0, 0, 0.06);
  --shadow-base: 0 4rpx 8rpx rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 16rpx 32rpx rgba(0, 0, 0, 0.12);
  --shadow-card: 0 2rpx 12rpx rgba(0, 122, 255, 0.08);

  /* ==================== 边框系统 ==================== */
  --border-width: 1rpx;
  --border-style: solid;
  --border: var(--border-width) var(--border-style) var(--border-color);

  /* ==================== 层级系统 ==================== */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;

  /* ==================== 动画系统 - 简约现代动画 ==================== */
  --transition-fast: 0.15s;
  --transition-base: 0.2s;
  --transition-slow: 0.3s;
  --transition-smooth: 0.4s;
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-out-back: cubic-bezier(0.34, 1.56, 0.64, 1);
  --ease-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);

  /* ==================== 业务色彩 ==================== */
  /* 订单状态色 */
  --order-pending: #FF9500;
  --order-confirmed: #007AFF;
  --order-processing: #34C759;
  --order-completed: #8E8E93;
  --order-cancelled: #FF3B30;

  /* 工单状态色 */
  --workorder-draft: #8E8E93;
  --workorder-pending: #FF9500;
  --workorder-processing: #007AFF;
  --workorder-completed: #34C759;
  --workorder-cancelled: #FF3B30;

  /* 库存状态色 */
  --inventory-sufficient: #34C759;
  --inventory-warning: #FF9500;
  --inventory-shortage: #FF3B30;

  /* 优先级色彩 */
  --priority-low: #8E8E93;
  --priority-normal: #007AFF;
  --priority-high: #FF9500;
  --priority-urgent: #FF3B30;

  /* ==================== 组件特定变量 - 简约现代组件 ==================== */
  /* 按钮 */
  --btn-height-sm: 64rpx;
  --btn-height-base: 88rpx;
  --btn-height-lg: 104rpx;
  --btn-padding-x: 32rpx;
  --btn-border-radius: var(--radius-lg);

  /* 输入框 */
  --input-height: 88rpx;
  --input-padding: 24rpx;
  --input-border-radius: var(--radius-lg);

  /* 卡片 */
  --card-padding: 32rpx;
  --card-border-radius: var(--radius-xl);
  --card-shadow: var(--shadow-card);
  --card-hover-shadow: var(--shadow-lg);

  /* 列表项 */
  --list-item-height: 120rpx;
  --list-item-padding: 32rpx;

  /* 导航栏 */
  --navbar-height: 88rpx;
  --navbar-padding: 24rpx;

  /* TabBar */
  --tabbar-height: 100rpx;
  --tabbar-icon-size: 48rpx;

  /* 简约现代特有组件 */
  --section-spacing: 24rpx;
  --content-max-width: 750rpx;
  --page-padding: 32rpx;
}
