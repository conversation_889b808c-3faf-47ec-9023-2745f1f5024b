<execution>
  <constraint>
    ## 小程序部署约束
    - **微信平台约束**：必须符合微信小程序审核规范
    - **企业认证约束**：企业小程序需要企业主体认证
    - **功能限制约束**：部分敏感功能需要特殊权限申请
    - **版本管理约束**：生产环境必须使用稳定版本
    - **备案要求约束**：服务器和域名必须完成备案
  </constraint>

  <rule>
    ## 部署强制规则
    - **环境隔离**：开发、测试、生产环境严格隔离
    - **版本控制**：所有代码变更必须通过版本控制
    - **审核流程**：生产部署必须经过代码审核
    - **备份策略**：部署前必须完成数据备份
    - **回滚准备**：必须准备快速回滚方案
    - **监控告警**：部署后必须配置监控和告警
  </rule>

  <guideline>
    ## 部署指导原则
    - **渐进式发布**：采用灰度发布策略降低风险
    - **自动化优先**：优先使用自动化部署工具
    - **文档完整**：部署过程和配置必须有详细文档
    - **团队协作**：建立清晰的部署责任和流程
    - **持续改进**：根据部署经验不断优化流程
  </guideline>

  <process>
    ## 小程序完整部署流程
    
    ### Phase 1: 环境准备 (1天)
    ```mermaid
    flowchart TD
        A[CloudBase环境创建] --> B[域名备案配置]
        B --> C[SSL证书申请]
        C --> D[微信小程序注册]
        D --> E[开发者权限配置]
        E --> F[基础配置完成]
    ```
    
    #### 环境配置清单
    1. **CloudBase环境配置**
       ```bash
       # 创建云开发环境
       tcb env:create --name production-env
       
       # 配置环境变量
       tcb env:config:update --env-id production-env
       ```
    
    2. **域名和证书配置**
       - 申请企业域名
       - 完成ICP备案
       - 申请SSL证书
       - 配置CDN加速
    
    3. **微信小程序配置**
       - 注册企业小程序账号
       - 完成企业认证
       - 配置服务器域名
       - 申请必要的接口权限
    
    ### Phase 2: 代码构建与测试 (2天)
    ```mermaid
    graph LR
        A[代码提交] --> B[自动构建]
        B --> C[单元测试]
        C --> D[集成测试]
        D --> E[性能测试]
        E --> F[安全扫描]
        F --> G[构建产物]
    ```
    
    #### 构建流程配置
    ```yaml
    # .github/workflows/deploy.yml
    name: 小程序部署流程
    on:
      push:
        branches: [main]
    
    jobs:
      build-and-deploy:
        runs-on: ubuntu-latest
        steps:
          - name: 代码检出
            uses: actions/checkout@v2
          
          - name: 安装依赖
            run: npm install
          
          - name: 代码检查
            run: npm run lint
          
          - name: 单元测试
            run: npm run test
          
          - name: 构建项目
            run: npm run build
          
          - name: 部署云函数
            run: tcb functions:deploy
          
          - name: 上传小程序
            run: miniprogram-ci upload
    ```
    
    ### Phase 3: 云端服务部署 (1天)
    ```mermaid
    flowchart TD
        A[云函数部署] --> B[数据库初始化]
        B --> C[存储配置]
        C --> D[静态资源上传]
        D --> E[安全规则配置]
        E --> F[监控配置]
    ```
    
    #### 云端部署步骤
    1. **云函数部署**
       ```bash
       # 部署所有云函数
       tcb functions:deploy --env production-env
       
       # 配置云函数触发器
       tcb functions:trigger:create
       
       # 设置云函数环境变量
       tcb functions:config:update
       ```
    
    2. **数据库配置**
       ```bash
       # 创建数据库集合
       tcb db:collection:create --name orders
       tcb db:collection:create --name products
       tcb db:collection:create --name inventory
       
       # 配置数据库索引
       tcb db:index:create --collection orders
       
       # 设置安全规则
       tcb db:security:update
       ```
    
    3. **存储和CDN配置**
       ```bash
       # 配置云存储
       tcb storage:config:update
       
       # 上传静态资源
       tcb hosting:deploy --dir dist
       
       # 配置CDN缓存策略
       tcb cdn:config:update
       ```
    
    ### Phase 4: 小程序发布 (1天)
    ```mermaid
    graph TD
        A[代码上传] --> B[提交审核]
        B --> C[审核通过]
        C --> D[版本发布]
        D --> E[灰度发布]
        E --> F[全量发布]
        F --> G[发布完成]
        
        B --> H[审核失败]
        H --> I[问题修复]
        I --> A
    ```
    
    #### 发布流程详解
    1. **代码上传**
       ```javascript
       // miniprogram-ci 配置
       const ci = require('miniprogram-ci');
       
       const project = new ci.Project({
         appid: 'your-appid',
         type: 'miniProgram',
         projectPath: './dist',
         privateKeyPath: './private.key'
       });
       
       // 上传代码
       await ci.upload({
         project,
         version: '1.0.0',
         desc: '生产管理系统v1.0.0',
         setting: {
           es6: true,
           minify: true
         }
       });
       ```
    
    2. **审核和发布**
       - 在微信公众平台提交审核
       - 等待微信审核通过
       - 设置发布时间和策略
       - 监控发布状态
    
    ### Phase 5: 监控和维护 (持续)
    ```mermaid
    mindmap
      root((运维监控))
        性能监控
          响应时间
          错误率
          并发数
        业务监控
          用户活跃度
          功能使用率
          业务指标
        安全监控
          异常访问
          权限变更
          数据安全
        告警机制
          邮件通知
          短信告警
          企业微信
    ```
    
    #### 监控配置
    ```javascript
    // 云函数监控配置
    const monitor = {
      // 性能监控
      performance: {
        responseTime: { threshold: 1000 },
        errorRate: { threshold: 0.01 },
        concurrency: { threshold: 100 }
      },
      
      // 业务监控
      business: {
        dailyActiveUsers: { threshold: 50 },
        orderCount: { threshold: 100 },
        systemUsage: { threshold: 0.8 }
      },
      
      // 告警配置
      alerts: {
        email: ['<EMAIL>'],
        sms: ['***********'],
        webhook: 'https://api.company.com/alert'
      }
    };
    ```
  </process>

  <criteria>
    ## 部署质量标准
    
    ### 部署成功率
    - ✅ 自动化部署成功率 > 95%
    - ✅ 回滚时间 < 5分钟
    - ✅ 部署过程零停机
    - ✅ 配置一致性100%
    
    ### 性能指标
    - ✅ 小程序启动时间 < 3秒
    - ✅ 页面切换时间 < 1秒
    - ✅ 接口响应时间 < 500ms
    - ✅ 资源加载时间 < 2秒
    
    ### 安全标准
    - ✅ HTTPS全站加密
    - ✅ 接口权限验证
    - ✅ 数据传输加密
    - ✅ 安全漏洞扫描通过
    
    ### 可用性保障
    - ✅ 系统可用性 > 99.9%
    - ✅ 故障恢复时间 < 30分钟
    - ✅ 数据备份完整性
    - ✅ 监控告警及时性
    
    ### 用户体验
    - ✅ 功能完整可用
    - ✅ 界面显示正常
    - ✅ 交互流程顺畅
    - ✅ 错误处理友好
  </criteria>
</execution>
