/**
 * 系统常量定义
 * 统一管理所有常量，避免魔法数字和字符串
 */

// 订单状态
const ORDER_STATUS = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
}

// 订单状态文本映射
const ORDER_STATUS_TEXT = {
  [ORDER_STATUS.PENDING]: '待确认',
  [ORDER_STATUS.CONFIRMED]: '已确认',
  [ORDER_STATUS.PROCESSING]: '生产中',
  [ORDER_STATUS.COMPLETED]: '已完成',
  [ORDER_STATUS.CANCELLED]: '已取消'
}

// 订单状态颜色映射
const ORDER_STATUS_COLOR = {
  [ORDER_STATUS.PENDING]: '#FF9500',
  [ORDER_STATUS.CONFIRMED]: '#007AFF',
  [ORDER_STATUS.PROCESSING]: '#34C759',
  [ORDER_STATUS.COMPLETED]: '#8E8E93',
  [ORDER_STATUS.CANCELLED]: '#FF3B30'
}

// 优先级
const PRIORITY = {
  LOW: 'low',
  NORMAL: 'normal',
  HIGH: 'high',
  URGENT: 'urgent'
}

// 优先级文本映射
const PRIORITY_TEXT = {
  [PRIORITY.LOW]: '低',
  [PRIORITY.NORMAL]: '普通',
  [PRIORITY.HIGH]: '高',
  [PRIORITY.URGENT]: '紧急'
}

// 优先级颜色映射
const PRIORITY_COLOR = {
  [PRIORITY.LOW]: '#8E8E93',
  [PRIORITY.NORMAL]: '#007AFF',
  [PRIORITY.HIGH]: '#FF9500',
  [PRIORITY.URGENT]: '#FF3B30'
}

// 工单状态
const WORK_ORDER_STATUS = {
  NOT_STARTED: 'not_started',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
}

// 工单状态文本映射
const WORK_ORDER_STATUS_TEXT = {
  [WORK_ORDER_STATUS.NOT_STARTED]: '未开始',
  [WORK_ORDER_STATUS.IN_PROGRESS]: '进行中',
  [WORK_ORDER_STATUS.COMPLETED]: '已完成',
  [WORK_ORDER_STATUS.CANCELLED]: '已取消'
}

// 任务状态
const TASK_STATUS = {
  NOT_STARTED: 'not_started',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
}

// 任务状态文本映射
const TASK_STATUS_TEXT = {
  [TASK_STATUS.NOT_STARTED]: '未开始',
  [TASK_STATUS.IN_PROGRESS]: '进行中',
  [TASK_STATUS.COMPLETED]: '已完成',
  [TASK_STATUS.CANCELLED]: '已取消'
}

// 库存状态
const INVENTORY_STATUS = {
  SUFFICIENT: 'sufficient',
  WARNING: 'warning',
  INSUFFICIENT: 'insufficient'
}

// 库存状态文本映射
const INVENTORY_STATUS_TEXT = {
  [INVENTORY_STATUS.SUFFICIENT]: '充足',
  [INVENTORY_STATUS.WARNING]: '预警',
  [INVENTORY_STATUS.INSUFFICIENT]: '不足'
}

// 库存状态颜色映射
const INVENTORY_STATUS_COLOR = {
  [INVENTORY_STATUS.SUFFICIENT]: '#34C759',
  [INVENTORY_STATUS.WARNING]: '#FF9500',
  [INVENTORY_STATUS.INSUFFICIENT]: '#FF3B30'
}

// 包装类型
const PACKAGING_TYPE = {
  STANDARD: 'standard',
  MOISTURE_PROOF: 'moisture_proof',
  WOODEN_BOX: 'wooden_box',
  PALLET: 'pallet',
  CUSTOM: 'custom'
}

// 包装类型文本映射
const PACKAGING_TYPE_TEXT = {
  [PACKAGING_TYPE.STANDARD]: '标准包装',
  [PACKAGING_TYPE.MOISTURE_PROOF]: '防潮包装',
  [PACKAGING_TYPE.WOODEN_BOX]: '木箱包装',
  [PACKAGING_TYPE.PALLET]: '托盘包装',
  [PACKAGING_TYPE.CUSTOM]: '定制包装'
}

// 数据类型
const DATA_TYPE = {
  STRING: 'string',
  NUMBER: 'number',
  BOOLEAN: 'boolean',
  ARRAY: 'array',
  OBJECT: 'object',
  DATE: 'date'
}

// 错误码
const ERROR_CODE = {
  // 通用错误
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  
  // 数据验证错误
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  REQUIRED_FIELD_MISSING: 'REQUIRED_FIELD_MISSING',
  INVALID_FORMAT: 'INVALID_FORMAT',
  
  // 业务逻辑错误
  NOT_FOUND: 'NOT_FOUND',
  ALREADY_EXISTS: 'ALREADY_EXISTS',
  STATUS_NOT_ALLOWED: 'STATUS_NOT_ALLOWED',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  
  // 存储错误
  SAVE_ERROR: 'SAVE_ERROR',
  DELETE_ERROR: 'DELETE_ERROR',
  UPDATE_ERROR: 'UPDATE_ERROR'
}

// 错误消息映射
const ERROR_MESSAGE = {
  [ERROR_CODE.UNKNOWN_ERROR]: '未知错误',
  [ERROR_CODE.NETWORK_ERROR]: '网络连接失败',
  [ERROR_CODE.TIMEOUT_ERROR]: '请求超时',
  [ERROR_CODE.VALIDATION_ERROR]: '数据验证失败',
  [ERROR_CODE.REQUIRED_FIELD_MISSING]: '必填字段缺失',
  [ERROR_CODE.INVALID_FORMAT]: '数据格式不正确',
  [ERROR_CODE.NOT_FOUND]: '数据不存在',
  [ERROR_CODE.ALREADY_EXISTS]: '数据已存在',
  [ERROR_CODE.STATUS_NOT_ALLOWED]: '当前状态不允许此操作',
  [ERROR_CODE.PERMISSION_DENIED]: '权限不足',
  [ERROR_CODE.SAVE_ERROR]: '保存失败',
  [ERROR_CODE.DELETE_ERROR]: '删除失败',
  [ERROR_CODE.UPDATE_ERROR]: '更新失败'
}

// 分页配置
const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100
}

// 日期格式
const DATE_FORMAT = {
  FULL: 'YYYY-MM-DD HH:mm:ss',
  DATE: 'YYYY-MM-DD',
  TIME: 'HH:mm:ss',
  MONTH_DAY: 'MM-DD',
  HOUR_MINUTE: 'HH:mm'
}

// 文件大小单位
const FILE_SIZE_UNIT = {
  B: 'B',
  KB: 'KB',
  MB: 'MB',
  GB: 'GB',
  TB: 'TB'
}

// 正则表达式
const REGEX = {
  PHONE: /^1[3-9]\d{9}$/,
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  ID_CARD: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
  CHINESE: /^[\u4e00-\u9fa5]+$/,
  NUMBER: /^\d+$/,
  DECIMAL: /^\d+(\.\d+)?$/,
  URL: /^https?:\/\/.+/
}

// 存储键名
const STORAGE_KEY = {
  USER_INFO: 'user_info',
  SETTINGS: 'app_settings',
  CACHE_PREFIX: 'cache_',
  TEMP_PREFIX: 'temp_'
}

// 事件名称
const EVENT_NAME = {
  ORDER_CREATED: 'order_created',
  ORDER_UPDATED: 'order_updated',
  ORDER_DELETED: 'order_deleted',
  WORK_ORDER_CREATED: 'work_order_created',
  WORK_ORDER_UPDATED: 'work_order_updated',
  TASK_COMPLETED: 'task_completed',
  INVENTORY_WARNING: 'inventory_warning'
}

// 导出所有常量
module.exports = {
  // 状态相关
  ORDER_STATUS,
  ORDER_STATUS_TEXT,
  ORDER_STATUS_COLOR,
  PRIORITY,
  PRIORITY_TEXT,
  PRIORITY_COLOR,
  WORK_ORDER_STATUS,
  WORK_ORDER_STATUS_TEXT,
  TASK_STATUS,
  TASK_STATUS_TEXT,
  INVENTORY_STATUS,
  INVENTORY_STATUS_TEXT,
  INVENTORY_STATUS_COLOR,
  PACKAGING_TYPE,
  PACKAGING_TYPE_TEXT,
  
  // 数据类型
  DATA_TYPE,
  
  // 错误处理
  ERROR_CODE,
  ERROR_MESSAGE,
  
  // 配置
  PAGINATION,
  DATE_FORMAT,
  FILE_SIZE_UNIT,
  
  // 正则表达式
  REGEX,
  
  // 存储
  STORAGE_KEY,
  
  // 事件
  EVENT_NAME,

  /**
   * 获取状态文本
   * @param {string} status 状态值
   * @param {string} type 状态类型
   * @returns {string} 状态文本
   */
  getStatusText(status, type = 'order') {
    const statusMaps = {
      order: ORDER_STATUS_TEXT,
      workOrder: WORK_ORDER_STATUS_TEXT,
      task: TASK_STATUS_TEXT,
      inventory: INVENTORY_STATUS_TEXT
    }
    
    const statusMap = statusMaps[type]
    return statusMap ? statusMap[status] || status : status
  },

  /**
   * 获取优先级文本
   * @param {string} priority 优先级值
   * @returns {string} 优先级文本
   */
  getPriorityText(priority) {
    return PRIORITY_TEXT[priority] || priority
  },

  /**
   * 获取状态颜色
   * @param {string} status 状态值
   * @param {string} type 状态类型
   * @returns {string} 颜色值
   */
  getStatusColor(status, type = 'order') {
    const colorMaps = {
      order: ORDER_STATUS_COLOR,
      priority: PRIORITY_COLOR,
      inventory: INVENTORY_STATUS_COLOR
    }
    
    const colorMap = colorMaps[type]
    return colorMap ? colorMap[status] || '#8E8E93' : '#8E8E93'
  },

  /**
   * 获取错误消息
   * @param {string} errorCode 错误码
   * @returns {string} 错误消息
   */
  getErrorMessage(errorCode) {
    return ERROR_MESSAGE[errorCode] || ERROR_MESSAGE[ERROR_CODE.UNKNOWN_ERROR]
  }
}
