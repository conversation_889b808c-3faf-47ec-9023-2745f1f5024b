<!--生产管理系统 - 客户管理页面 - 简约现代风格-->
<view class="modern-page">
  <!-- 页面头部 - 简约现代风格 -->
  <view class="modern-navbar">
    <text class="modern-navbar-title">客户管理</text>
    <view class="modern-navbar-actions">
      <button class="modern-btn modern-btn-primary" bindtap="showAddCustomer">
        <text class="btn-icon">✨</text>
        <text class="btn-text">新建客户</text>
      </button>
    </view>
  </view>

  <!-- 客户列表 - 简约现代风格 -->
  <view class="modern-container">
    <view class="customer-list" wx:if="{{ customerList.length > 0 }}">
      <view
        class="modern-card customer-card"
        wx:for="{{ customerList }}"
        wx:key="id"
      >
        <!-- 客户信息 -->
        <view class="modern-card-header">
          <view class="customer-main">
            <view class="customer-name">{{ item.name }}</view>
            <view class="customer-contact">
              <text class="contact-person">👤 {{ item.contact }}</text>
              <text class="contact-phone" wx:if="{{ item.phone }}">📞 {{ item.phone }}</text>
            </view>
          </view>
          <view class="customer-status">
            <view class="modern-tag modern-tag-primary">活跃</view>
          </view>
        </view>

        <view class="modern-card-body">
          <view class="customer-details">
            <view class="detail-item" wx:if="{{ item.address }}">
              <text class="detail-icon">📍</text>
              <text class="detail-text">{{ item.address }}</text>
            </view>
            <view class="detail-item" wx:if="{{ item.email }}">
              <text class="detail-icon">📧</text>
              <text class="detail-text">{{ item.email }}</text>
            </view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="modern-card-footer">
          <button
            class="modern-btn modern-btn-ghost modern-btn-small"
            bindtap="editCustomer"
            data-customer="{{ item }}"
          >
            编辑
          </button>
          <button
            class="modern-btn modern-btn-secondary modern-btn-small"
            bindtap="deleteCustomer"
            data-customer="{{ item }}"
          >
            删除
          </button>
        </view>
      </view>
    </view>

    <!-- 空状态 - 简约现代风格 -->
    <view class="modern-empty" wx:else>
      <text class="modern-empty-icon">🏢</text>
      <view class="modern-empty-text">
        <text>暂无客户信息</text>
        <text>点击右上角按钮添加客户</text>
      </view>
    </view>
  </view>

  <!-- 添加/编辑表单弹窗 -->
  <view class="form-overlay" wx:if="{{ showAddForm }}" bindtap="hideForm">
    <view class="form-container" catchtap="true">
      <view class="form-header">
        <text class="form-title">{{ editingId ? '编辑客户' : '新建客户' }}</text>
        <button class="close-btn" bindtap="hideForm">✕</button>
      </view>

      <view class="form-content">
        <view class="form-group">
          <text class="form-label">客户名称 *</text>
          <input 
            class="form-input"
            value="{{ formData.name }}"
            placeholder="请输入客户名称"
            data-field="name"
            bindinput="onFormInput"
          />
        </view>

        <view class="form-group">
          <text class="form-label">联系人 *</text>
          <input 
            class="form-input"
            value="{{ formData.contact }}"
            placeholder="请输入联系人姓名"
            data-field="contact"
            bindinput="onFormInput"
          />
        </view>

        <view class="form-group">
          <text class="form-label">联系电话</text>
          <input 
            class="form-input"
            value="{{ formData.phone }}"
            placeholder="请输入联系电话"
            data-field="phone"
            bindinput="onFormInput"
          />
        </view>

        <view class="form-group">
          <text class="form-label">客户地址</text>
          <textarea 
            class="form-textarea"
            value="{{ formData.address }}"
            placeholder="请输入客户地址"
            data-field="address"
            bindinput="onFormInput"
          />
        </view>

        <view class="form-group">
          <text class="form-label">邮箱地址</text>
          <input 
            class="form-input"
            value="{{ formData.email }}"
            placeholder="请输入邮箱地址"
            data-field="email"
            bindinput="onFormInput"
          />
        </view>

        <view class="form-group">
          <text class="form-label">备注信息</text>
          <textarea 
            class="form-textarea"
            value="{{ formData.notes }}"
            placeholder="请输入备注信息"
            data-field="notes"
            bindinput="onFormInput"
          />
        </view>
      </view>

      <view class="form-actions">
        <button class="btn btn-secondary" bindtap="hideForm">取消</button>
        <button class="btn btn-primary" bindtap="saveCustomer" disabled="{{ loading }}">
          {{ loading ? '保存中...' : '保存' }}
        </button>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{ loading }}">
    <view class="loading-content">
      <text class="loading-text">处理中...</text>
    </view>
  </view>
</view>
