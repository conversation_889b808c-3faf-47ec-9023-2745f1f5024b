/**
 * 简化版现代组件样式 - 确保在小程序中正确工作
 */

/* ==================== 页面布局 ==================== */
.modern-page {
  min-height: 100vh;
  background-color: #F2F2F7;
  padding-bottom: 40rpx;
}

.modern-container {
  max-width: 750rpx;
  margin: 0 auto;
  padding: 0 32rpx;
}

/* ==================== 导航栏 ==================== */
.modern-navbar {
  background-color: #FFFFFF;
  padding: 24rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  box-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
}

.modern-navbar-title {
  font-size: 48rpx;
  font-weight: 700;
  color: #1D1D1F;
  background: linear-gradient(135deg, #007AFF, #0056CC);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.modern-navbar-actions {
  display: flex;
  gap: 16rpx;
}

/* ==================== 按钮组件 ==================== */
.modern-btn {
  height: 88rpx;
  padding: 0 32rpx;
  border-radius: 16rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.modern-btn-primary {
  background: linear-gradient(135deg, #007AFF, #0056CC);
  color: #FFFFFF;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.06);
}

.modern-btn-primary:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
}

.modern-btn-secondary {
  background-color: #FFFFFF;
  color: #1D1D1F;
  border: 2rpx solid #E5E5EA;
}

.modern-btn-secondary:active {
  background-color: #F2F2F7;
  transform: translateY(2rpx) scale(0.98);
}

.modern-btn-ghost {
  background-color: transparent;
  color: #007AFF;
  border: 2rpx solid #007AFF;
}

.modern-btn-ghost:active {
  background-color: #E3F2FD;
  transform: translateY(2rpx) scale(0.98);
}

.modern-btn-small {
  height: 64rpx;
  font-size: 24rpx;
  padding: 0 24rpx;
}

.modern-btn-large {
  height: 104rpx;
  font-size: 32rpx;
  padding: 0 40rpx;
}

/* ==================== 卡片组件 ==================== */
.modern-card {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 122, 255, 0.08);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.modern-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(135deg, #007AFF, #0056CC);
}

.modern-card:active {
  transform: translateY(2rpx) scale(0.99);
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
}

.modern-card-header {
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #E5E5EA;
  margin-bottom: 16rpx;
}

.modern-card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1D1D1F;
}

.modern-card-body {
  color: #48484A;
  line-height: 1.6;
}

.modern-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #E5E5EA;
}

/* ==================== 标签组件 ==================== */
.modern-tag {
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
  font-weight: 500;
  line-height: 1.2;
}

.modern-tag-primary {
  background-color: #E3F2FD;
  color: #007AFF;
}

.modern-tag-success {
  background-color: #E8F5E9;
  color: #34C759;
}

.modern-tag-warning {
  background-color: #FFF3E0;
  color: #FF9500;
}

.modern-tag-error {
  background-color: #FFEBEE;
  color: #FF3B30;
}

.modern-tag-neutral {
  background-color: #F2F2F7;
  color: #8E8E93;
}

/* ==================== 信息区块 ==================== */
.modern-section {
  margin-bottom: 32rpx;
}

.modern-section-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.modern-section-icon {
  font-size: 32rpx;
  opacity: 0.8;
}

.modern-section-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #1D1D1F;
  flex: 1;
}

.modern-section-content {
  color: #48484A;
  line-height: 1.6;
}

/* ==================== 列表组件 ==================== */
.modern-list {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 122, 255, 0.08);
}

.modern-list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E5EA;
  transition: all 0.2s ease;
}

.modern-list-item:last-child {
  border-bottom: none;
}

.modern-list-item:active {
  background-color: #F2F2F7;
}

/* ==================== 空状态 ==================== */
.modern-empty {
  text-align: center;
  padding: 80rpx 40rpx;
  color: #8E8E93;
}

.modern-empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
  display: block;
}

.modern-empty-text {
  font-size: 28rpx;
  line-height: 1.5;
}

/* ==================== 加载状态 ==================== */
.modern-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  color: #8E8E93;
}

.modern-loading-text {
  margin-left: 16rpx;
  font-size: 28rpx;
}
