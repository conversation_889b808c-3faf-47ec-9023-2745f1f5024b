/**
 * 产品管理服务
 * 提供产品信息的增删改查功能
 */

// 模拟产品数据
let mockProducts = [
  {
    id: 'product_001',
    name: '精密齿轮组件',
    code: 'GEAR-001',
    specification: '模数2.5，齿数40，材质45#钢',
    unit: '件',
    category: '机械零件',
    notes: '高精度齿轮，用于传动系统',
    createdAt: '2024-01-01T08:00:00.000Z',
    updatedAt: '2024-01-01T08:00:00.000Z'
  },
  {
    id: 'product_002',
    name: '传动轴承座',
    code: 'BEARING-002',
    specification: '内径50mm，外径120mm，高度30mm',
    unit: '件',
    category: '机械零件',
    notes: '标准轴承座，适用于各种传动设备',
    createdAt: '2024-01-02T09:15:00.000Z',
    updatedAt: '2024-01-02T09:15:00.000Z'
  },
  {
    id: 'product_003',
    name: '液压缸体',
    code: 'CYLINDER-003',
    specification: '缸径100mm，行程200mm，压力16MPa',
    unit: '件',
    category: '机械零件',
    notes: '重型液压缸体，适用于工程机械',
    createdAt: '2024-01-03T11:30:00.000Z',
    updatedAt: '2024-01-03T11:30:00.000Z'
  },
  {
    id: 'product_004',
    name: '电机外壳',
    code: 'MOTOR-004',
    specification: '铝合金材质，防护等级IP65',
    unit: '件',
    category: '电子元件',
    notes: '防水防尘电机外壳，表面阳极氧化处理',
    createdAt: '2024-01-04T14:20:00.000Z',
    updatedAt: '2024-01-04T14:20:00.000Z'
  },
  {
    id: 'product_005',
    name: '不锈钢板材',
    code: 'STEEL-005',
    specification: '304不锈钢，厚度3mm',
    unit: 'm²',
    category: '原材料',
    notes: '食品级不锈钢板材，表面光洁度高',
    createdAt: '2024-01-05T16:45:00.000Z',
    updatedAt: '2024-01-05T16:45:00.000Z'
  }
]

/**
 * 生成唯一ID
 */
function generateId() {
  return 'product_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

/**
 * 获取产品列表
 */
function getProductList(params = {}) {
  return new Promise((resolve) => {
    setTimeout(() => {
      let result = [...mockProducts]
      
      // 搜索过滤
      if (params.keyword) {
        const keyword = params.keyword.toLowerCase()
        result = result.filter(product => 
          product.name.toLowerCase().includes(keyword) ||
          product.code.toLowerCase().includes(keyword) ||
          (product.specification && product.specification.toLowerCase().includes(keyword))
        )
      }
      
      // 分类过滤
      if (params.category) {
        result = result.filter(product => product.category === params.category)
      }
      
      // 排序
      result.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      
      resolve(result)
    }, 300)
  })
}

/**
 * 根据ID获取产品信息
 */
function getProductById(id) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const product = mockProducts.find(item => item.id === id)
      if (product) {
        resolve(product)
      } else {
        reject(new Error('产品不存在'))
      }
    }, 200)
  })
}

/**
 * 创建产品
 */
function createProduct(productData) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      // 验证必填字段
      if (!productData.name || !productData.code || !productData.unit) {
        reject(new Error('产品名称、编码和单位为必填项'))
        return
      }
      
      // 检查产品编码是否重复
      const exists = mockProducts.some(product => product.code === productData.code)
      if (exists) {
        reject(new Error('产品编码已存在'))
        return
      }
      
      const newProduct = {
        id: generateId(),
        name: productData.name,
        code: productData.code,
        specification: productData.specification || '',
        unit: productData.unit,
        price: productData.price || 0,
        category: productData.category || '',
        notes: productData.notes || '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      mockProducts.unshift(newProduct)
      resolve(newProduct)
    }, 500)
  })
}

/**
 * 更新产品信息
 */
function updateProduct(id, productData) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockProducts.findIndex(product => product.id === id)
      if (index === -1) {
        reject(new Error('产品不存在'))
        return
      }
      
      // 验证必填字段
      if (!productData.name || !productData.code || !productData.unit) {
        reject(new Error('产品名称、编码和单位为必填项'))
        return
      }
      
      // 检查产品编码是否重复（排除自己）
      const exists = mockProducts.some(product => 
        product.code === productData.code && product.id !== id
      )
      if (exists) {
        reject(new Error('产品编码已存在'))
        return
      }
      
      const updatedProduct = {
        ...mockProducts[index],
        name: productData.name,
        code: productData.code,
        specification: productData.specification || '',
        unit: productData.unit,
        price: productData.price || 0,
        category: productData.category || '',
        notes: productData.notes || '',
        updatedAt: new Date().toISOString()
      }
      
      mockProducts[index] = updatedProduct
      resolve(updatedProduct)
    }, 500)
  })
}

/**
 * 删除产品
 */
function deleteProduct(id) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockProducts.findIndex(product => product.id === id)
      if (index === -1) {
        reject(new Error('产品不存在'))
        return
      }
      
      const deletedProduct = mockProducts.splice(index, 1)[0]
      resolve(deletedProduct)
    }, 300)
  })
}

/**
 * 获取产品选项列表（用于下拉选择）
 */
function getProductOptions() {
  return new Promise((resolve) => {
    setTimeout(() => {
      const options = mockProducts.map(product => ({
        value: product.id,
        label: product.name,
        code: product.code,
        unit: product.unit,
        price: product.price
      }))
      resolve(options)
    }, 200)
  })
}

/**
 * 获取产品分类列表
 */
function getProductCategories() {
  return new Promise((resolve) => {
    setTimeout(() => {
      const categories = [...new Set(mockProducts.map(product => product.category).filter(Boolean))]
      resolve(categories)
    }, 200)
  })
}

module.exports = {
  getProductList,
  getProductById,
  createProduct,
  updateProduct,
  deleteProduct,
  getProductOptions,
  getProductCategories
}
