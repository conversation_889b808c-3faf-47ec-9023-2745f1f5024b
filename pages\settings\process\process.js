// 工序管理页面
const ProcessService = require('../../../services/processService')
const { formatDate } = require('../../../utils/dateUtils')

Page({
  data: {
    loading: false,
    processes: [],
    filteredProcesses: [],
    categories: [],
    selectedCategory: '',
    searchKeyword: '',
    
    // 弹窗相关
    showModal: false,
    isEditing: false,
    editingId: null,
    saving: false,
    isFormValid: false,
    formData: {
      name: '',
      category: '',
      estimatedHours: '',
      description: ''
    },
    
    // 删除确认弹窗
    showDeleteModal: false,
    deleteTarget: {},
    deleting: false
  },

  onLoad() {
    wx.setNavigationBarTitle({
      title: '工序管理'
    })
    this.loadPageData()
  },

  onShow() {
    // 每次显示时刷新数据
    this.loadPageData()
  },

  onPullDownRefresh() {
    this.loadPageData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 加载页面数据
   */
  async loadPageData() {
    this.setData({ loading: true })
    
    try {
      await Promise.all([
        this.loadProcesses(),
        this.loadCategories()
      ])
      this.filterProcesses()
    } catch (error) {
      console.error('加载页面数据失败:', error)
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 加载工序列表
   */
  async loadProcesses() {
    try {
      const result = await ProcessService.getProcessList()
      if (result.success) {
        // 格式化时间显示
        const processes = result.data.map(process => ({
          ...process,
          createdAt: formatDate(process.createdAt, 'MM-DD HH:mm')
        }))
        
        this.setData({ processes })
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('加载工序列表失败:', error)
      wx.showToast({
        title: '加载工序失败',
        icon: 'none'
      })
    }
  },

  /**
   * 加载分类列表
   */
  async loadCategories() {
    try {
      const result = await ProcessService.getProcessCategories()
      if (result.success) {
        this.setData({ categories: result.data })
      }
    } catch (error) {
      console.error('加载分类列表失败:', error)
    }
  },

  /**
   * 筛选工序
   */
  filterProcesses() {
    const { processes, selectedCategory, searchKeyword } = this.data
    let filtered = [...processes]

    // 分类筛选
    if (selectedCategory) {
      filtered = filtered.filter(process => process.category === selectedCategory)
    }

    // 关键词搜索
    if (searchKeyword.trim()) {
      const keyword = searchKeyword.toLowerCase()
      filtered = filtered.filter(process =>
        process.name.toLowerCase().includes(keyword) ||
        process.description.toLowerCase().includes(keyword) ||
        process.category.toLowerCase().includes(keyword)
      )
    }

    this.setData({ filteredProcesses: filtered })
  },

  /**
   * 搜索输入处理
   */
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    })
  },

  /**
   * 搜索确认
   */
  onSearchConfirm() {
    this.filterProcesses()
  },

  /**
   * 选择分类
   */
  selectCategory(e) {
    const category = e.currentTarget.dataset.category
    this.setData({
      selectedCategory: category
    })
    this.filterProcesses()
  },

  /**
   * 显示创建工序弹窗
   */
  showCreateModal() {
    this.setData({
      showModal: true,
      isEditing: false,
      editingId: null,
      formData: {
        name: '',
        category: '',
        estimatedHours: '',
        description: ''
      }
    })
    this.validateForm()
  },

  /**
   * 编辑工序
   */
  editProcess(e) {
    const process = e.currentTarget.dataset.process
    this.setData({
      showModal: true,
      isEditing: true,
      editingId: process.id,
      formData: {
        name: process.name,
        category: process.category || '',
        estimatedHours: process.estimatedHours.toString(),
        description: process.description || ''
      }
    })
    this.validateForm()
  },

  /**
   * 隐藏弹窗
   */
  hideModal() {
    this.setData({
      showModal: false,
      isEditing: false,
      editingId: null,
      saving: false,
      formData: {
        name: '',
        category: '',
        estimatedHours: '',
        description: ''
      }
    })
  },

  /**
   * 表单输入处理
   */
  onNameInput(e) {
    this.setData({
      'formData.name': e.detail.value
    })
    this.validateForm()
  },

  onCategoryInput(e) {
    this.setData({
      'formData.category': e.detail.value
    })
  },

  onHoursInput(e) {
    this.setData({
      'formData.estimatedHours': e.detail.value
    })
    this.validateForm()
  },

  onDescriptionInput(e) {
    this.setData({
      'formData.description': e.detail.value
    })
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { formData } = this.data
    const isValid = formData.name.trim() && 
                   formData.estimatedHours && 
                   parseFloat(formData.estimatedHours) > 0
    
    this.setData({ isFormValid: isValid })
  },

  /**
   * 保存工序
   */
  async saveProcess() {
    const { isEditing, editingId, formData } = this.data
    
    if (!formData.name.trim()) {
      wx.showToast({
        title: '请输入工序名称',
        icon: 'none'
      })
      return
    }

    if (!formData.estimatedHours || parseFloat(formData.estimatedHours) <= 0) {
      wx.showToast({
        title: '请输入有效的预计工时',
        icon: 'none'
      })
      return
    }

    this.setData({ saving: true })

    try {
      let result
      if (isEditing) {
        result = await ProcessService.updateProcess(editingId, formData)
      } else {
        result = await ProcessService.createProcess(formData)
      }

      if (result.success) {
        wx.showToast({
          title: result.message,
          icon: 'success'
        })
        
        this.hideModal()
        this.loadPageData()
      } else {
        wx.showToast({
          title: result.message,
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('保存工序失败:', error)
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      })
    } finally {
      this.setData({ saving: false })
    }
  },

  /**
   * 删除工序
   */
  deleteProcess(e) {
    const { id, name } = e.currentTarget.dataset
    this.setData({
      showDeleteModal: true,
      deleteTarget: { id, name }
    })
  },

  /**
   * 隐藏删除确认弹窗
   */
  hideDeleteModal() {
    this.setData({
      showDeleteModal: false,
      deleteTarget: {},
      deleting: false
    })
  },

  /**
   * 确认删除
   */
  async confirmDelete() {
    const { deleteTarget } = this.data
    
    this.setData({ deleting: true })

    try {
      const result = await ProcessService.deleteProcess(deleteTarget.id)
      
      if (result.success) {
        wx.showToast({
          title: result.message,
          icon: 'success'
        })
        
        this.hideDeleteModal()
        this.loadPageData()
      } else {
        wx.showToast({
          title: result.message,
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('删除工序失败:', error)
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      })
    } finally {
      this.setData({ deleting: false })
    }
  },

  /**
   * 查看工序详情
   */
  viewProcessDetail(e) {
    const process = e.currentTarget.dataset.process
    wx.showModal({
      title: '工序详情',
      content: `名称：${process.name}\n分类：${process.category}\n预计工时：${process.estimatedHours}小时\n描述：${process.description || '暂无描述'}\n创建时间：${process.createdAt}`,
      showCancel: false,
      confirmText: '知道了'
    })
  }
})
