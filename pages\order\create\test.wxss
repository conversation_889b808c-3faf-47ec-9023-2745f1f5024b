/* 简化测试样式 */
.page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.container {
  padding: 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 40rpx;
  color: #333;
}

.form-group {
  background-color: #fff;
  margin-bottom: 20rpx;
  padding: 20rpx;
  border-radius: 8rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 1px solid #ddd;
  border-radius: 4rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.input[disabled] {
  background-color: #f5f5f5;
  color: #999;
}

.picker {
  height: 80rpx;
  line-height: 80rpx;
  padding: 0 20rpx;
  border: 1px solid #ddd;
  border-radius: 4rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.quantity-row {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.qty-btn {
  width: 60rpx;
  height: 60rpx;
  border: 1px solid #ddd;
  border-radius: 4rpx;
  background-color: #fff;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  padding: 0;
}

.qty-btn::after {
  border: none;
}

.qty-input {
  flex: 1;
  height: 60rpx;
  text-align: center;
  border: 1px solid #ddd;
  border-radius: 4rpx;
  font-size: 28rpx;
}



.total-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #e65100;
}

.bottom-bar {
  position: fixed;
  bottom: 100rpx;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx;
  border-top: 1px solid #ddd;
  display: flex;
  gap: 20rpx;
}

.btn {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn::after {
  border: none;
}

.btn-cancel {
  background-color: #f5f5f5;
  color: #666;
}

.btn-submit {
  background-color: #007AFF;
  color: #fff;
}
