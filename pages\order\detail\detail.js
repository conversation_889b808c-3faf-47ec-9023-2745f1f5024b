// 订单详情页面逻辑
const OrderService = require('../../../services/orderService.js')
const { formatDate } = require('../../../utils/dateUtils.js')
const { getStatusText, getPriorityText } = require('../../../utils/common.js')

Page({
  data: {
    // 订单数据
    orderInfo: null,
    loading: true,

    // 状态选项
    statusOptions: [
      { label: '待确认', value: 'pending' },
      { label: '已确认', value: 'confirmed' },
      { label: '生产中', value: 'processing' },
      { label: '已完成', value: 'completed' },
      { label: '已取消', value: 'cancelled' }
    ],

    // UI状态
    showStatusModal: false,
    showDeleteModal: false,
    saving: false
  },

  onLoad(options) {
    console.log('订单详情页面加载', options)

    if (options.id) {
      this.loadOrderDetail(options.id)
    } else {
      wx.showToast({
        title: '订单ID不能为空',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  onShow() {
    // 从编辑页面返回时刷新数据
    if (this.data.orderInfo) {
      this.loadOrderDetail(this.data.orderInfo.id)
    }
  },

  /**
   * 加载订单详情
   */
  async loadOrderDetail(orderId) {
    console.log('开始加载订单详情, ID:', orderId)
    this.setData({ loading: true })

    try {
      console.log('调用OrderService.getOrderById')
      const result = await OrderService.getOrderById(orderId)
      console.log('OrderService返回结果:', result)

      if (result.success) {
        console.log('开始格式化订单数据')
        const orderInfo = OrderService.formatOrderForDisplay(result.data)
        console.log('格式化后的订单数据:', orderInfo)

        this.setData({ orderInfo })

        // 设置页面标题
        wx.setNavigationBarTitle({
          title: `订单详情 - ${orderInfo.orderNo}`
        })

        console.log('订单详情加载成功:', orderInfo)
      } else {
        console.log('订单加载失败:', result.message)
        wx.showToast({
          title: result.message || '订单不存在',
          icon: 'none'
        })
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      }
    } catch (error) {
      console.error('加载订单详情失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 编辑订单
   */
  editOrder() {
    const { orderInfo } = this.data
    wx.navigateTo({
      url: `/pages/order/create/create?id=${orderInfo.id}&mode=edit`
    })
  },

  /**
   * 显示状态修改弹窗
   */
  showStatusModal() {
    this.setData({ showStatusModal: true })
  },

  /**
   * 隐藏状态修改弹窗
   */
  hideStatusModal() {
    this.setData({ showStatusModal: false })
  },

  /**
   * 修改订单状态
   */
  async changeOrderStatus(e) {
    const newStatus = e.currentTarget.dataset.status
    const { orderInfo } = this.data

    if (newStatus === orderInfo.status) {
      this.hideStatusModal()
      return
    }

    this.setData({ saving: true })

    try {
      const result = await OrderService.updateOrder(orderInfo.id, {
        status: newStatus,
        updatedAt: new Date().toISOString()
      })

      if (result.success) {
        wx.showToast({
          title: '状态更新成功',
          icon: 'success'
        })

        this.hideStatusModal()
        this.loadOrderDetail(orderInfo.id)
      } else {
        wx.showToast({
          title: result.message || '更新失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('更新订单状态失败:', error)
      wx.showToast({
        title: '更新失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ saving: false })
    }
  },

  /**
   * 显示删除确认弹窗
   */
  showDeleteModal() {
    this.setData({ showDeleteModal: true })
  },

  /**
   * 隐藏删除确认弹窗
   */
  hideDeleteModal() {
    this.setData({ showDeleteModal: false })
  },

  /**
   * 删除订单
   */
  async deleteOrder() {
    const { orderInfo } = this.data
    this.setData({ saving: true })

    try {
      const result = await OrderService.deleteOrder(orderInfo.id)

      if (result.success) {
        wx.showToast({
          title: '订单删除成功',
          icon: 'success'
        })

        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        wx.showToast({
          title: result.message || '删除失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('删除订单失败:', error)
      wx.showToast({
        title: '删除失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ saving: false })
      this.hideDeleteModal()
    }
  },

  /**
   * 创建工单
   */
  createWorkOrder() {
    const { orderInfo } = this.data

    if (orderInfo.status !== 'confirmed') {
      wx.showToast({
        title: '只有已确认的订单才能创建工单',
        icon: 'none'
      })
      return
    }

    wx.navigateTo({
      url: `/pages/workorder/create/create?orderId=${orderInfo.id}`
    })
  },

  /**
   * 复制订单号
   */
  copyOrderNo() {
    const { orderInfo } = this.data
    wx.setClipboardData({
      data: orderInfo.orderNo,
      success: () => {
        wx.showToast({
          title: '订单号已复制',
          icon: 'success'
        })
      }
    })
  },

  /**
   * 拨打电话
   */
  makePhoneCall() {
    const { orderInfo } = this.data
    if (orderInfo.customerPhone) {
      wx.makePhoneCall({
        phoneNumber: orderInfo.customerPhone
      })
    }
  },

  /**
   * 返回上一页
   */
  navigateBack() {
    wx.navigateBack()
  }
})
