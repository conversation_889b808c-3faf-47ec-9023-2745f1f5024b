/**
 * 任务服务 - 管理工序任务数据
 */

// 模拟任务数据
const mockTasks = [
  {
    id: 'task_001',
    processId: 'process_001',
    processName: '去毛刺',
    workOrderId: 'workorder_001',
    workOrderNo: 'GD20250701001',
    productName: '精密齿轮组件',
    productCode: 'CP20240006',
    
    // 任务状态
    status: 'in_progress', // 'pending', 'in_progress', 'completed', 'paused'
    assignedTo: null, // 分配给的员工ID
    assignedEmployee: null, // 员工信息
    
    // 生产数据
    plannedQuantity: 1000, // 计划数量 (kg)
    processedQuantity: 0, // 已加工数量 (kg)
    qualifiedQuantity: 0, // 良品数量 (kg)
    defectiveQuantity: 0, // 不良品数量 (kg)
    
    // 进度计算
    progress: 0, // 进度百分比 (0-100)
    
    // 时间信息
    plannedStartTime: '2025-07-31 00:00',
    plannedEndTime: '2025-07-31 23:59',
    actualStartTime: null,
    actualEndTime: null,
    
    // 工位信息
    workstation: '办公室',
    workshop: '压铸车间',
    
    createdAt: '2025-01-01T08:00:00.000Z',
    updatedAt: '2025-01-01T08:00:00.000Z'
  },
  {
    id: 'task_002',
    processId: 'process_002',
    processName: '压铸',
    workOrderId: 'workorder_001',
    workOrderNo: 'GD20250701001',
    productName: '精密齿轮组件',
    productCode: 'CP20240006',
    
    status: 'completed',
    assignedTo: 'employee_001',
    assignedEmployee: {
      id: 'employee_001',
      name: '系统管理员',
      department: '生产部'
    },
    
    plannedQuantity: 1000,
    processedQuantity: 1000,
    qualifiedQuantity: 1000,
    defectiveQuantity: 0,
    progress: 100,
    
    plannedStartTime: '2025-07-31 00:00',
    plannedEndTime: '2025-07-31 23:59',
    actualStartTime: '2025-07-31 08:00',
    actualEndTime: '2025-07-31 16:00',
    
    workstation: '办公室',
    workshop: '压铸车间',
    
    createdAt: '2025-01-01T08:00:00.000Z',
    updatedAt: '2025-01-01T16:00:00.000Z'
  }
]

// 模拟员工数据
const mockEmployees = [
  { id: 'employee_001', name: '系统管理员', department: '生产部' },
  { id: 'employee_002', name: '张师傅', department: '生产部' },
  { id: 'employee_003', name: '李师傅', department: '生产部' },
  { id: 'employee_004', name: '王师傅', department: '生产部' }
]

class TaskService {
  /**
   * 获取任务列表
   * @param {Object} params 查询参数
   * @returns {Promise} 任务列表
   */
  static async getTaskList(params = {}) {
    return new Promise((resolve) => {
      setTimeout(() => {
        let filteredTasks = [...mockTasks]
        
        // 状态筛选
        if (params.status && params.status !== 'all') {
          filteredTasks = filteredTasks.filter(task => task.status === params.status)
        }
        
        // 员工筛选
        if (params.employeeId) {
          filteredTasks = filteredTasks.filter(task => task.assignedTo === params.employeeId)
        }
        
        // 关键词搜索
        if (params.keyword) {
          const keyword = params.keyword.toLowerCase()
          filteredTasks = filteredTasks.filter(task => 
            task.processName.toLowerCase().includes(keyword) ||
            task.workOrderNo.toLowerCase().includes(keyword) ||
            task.productName.toLowerCase().includes(keyword)
          )
        }
        
        // 分页处理
        const page = params.page || 1
        const pageSize = params.pageSize || 10
        const start = (page - 1) * pageSize
        const end = start + pageSize
        const paginatedTasks = filteredTasks.slice(start, end)
        
        console.log('模拟获取任务列表:', paginatedTasks)
        
        resolve({
          success: true,
          data: paginatedTasks,
          total: filteredTasks.length,
          page,
          pageSize,
          hasMore: end < filteredTasks.length
        })
      }, 500)
    })
  }
  
  /**
   * 根据ID获取任务详情
   * @param {string} taskId 任务ID
   * @returns {Promise} 任务详情
   */
  static async getTaskById(taskId) {
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log('TaskService.getTaskById 查询任务ID:', taskId)
        console.log('TaskService.getTaskById 可用任务列表:', mockTasks.map(t => ({ id: t.id, name: t.processName })))

        const task = mockTasks.find(item => item.id === taskId)

        console.log('TaskService.getTaskById 查询结果:', task ? '找到任务' : '未找到任务')

        if (task) {
          resolve({
            success: true,
            data: task,
            message: '获取成功'
          })
        } else {
          resolve({
            success: false,
            data: null,
            message: `任务不存在，查询ID: ${taskId}`
          })
        }
      }, 300)
    })
  }
  
  /**
   * 更新任务
   * @param {string} taskId 任务ID
   * @param {Object} updateData 更新数据
   * @returns {Promise} 更新结果
   */
  static async updateTask(taskId, updateData) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const taskIndex = mockTasks.findIndex(item => item.id === taskId)
        
        if (taskIndex !== -1) {
          // 更新任务数据
          mockTasks[taskIndex] = {
            ...mockTasks[taskIndex],
            ...updateData,
            updatedAt: new Date().toISOString()
          }
          
          // 重新计算进度
          const task = mockTasks[taskIndex]
          if (task.plannedQuantity > 0) {
            task.progress = Math.round((task.processedQuantity / task.plannedQuantity) * 100)
          }
          
          console.log('模拟更新任务:', mockTasks[taskIndex])
          
          resolve({
            success: true,
            data: mockTasks[taskIndex],
            message: '任务更新成功'
          })
        } else {
          resolve({
            success: false,
            message: '任务不存在'
          })
        }
      }, 600)
    })
  }
  
  /**
   * 分配任务给员工
   * @param {string} taskId 任务ID
   * @param {string} employeeId 员工ID
   * @returns {Promise} 分配结果
   */
  static async assignTask(taskId, employeeId) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const taskIndex = mockTasks.findIndex(item => item.id === taskId)
        const employee = mockEmployees.find(emp => emp.id === employeeId)
        
        if (taskIndex !== -1 && employee) {
          mockTasks[taskIndex].assignedTo = employeeId
          mockTasks[taskIndex].assignedEmployee = employee
          mockTasks[taskIndex].updatedAt = new Date().toISOString()
          
          console.log('模拟分配任务:', mockTasks[taskIndex])
          
          resolve({
            success: true,
            data: mockTasks[taskIndex],
            message: '任务分配成功'
          })
        } else {
          resolve({
            success: false,
            message: '任务或员工不存在'
          })
        }
      }, 400)
    })
  }
  
  /**
   * 获取员工列表
   * @returns {Promise} 员工列表
   */
  static async getEmployeeList() {
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log('模拟获取员工列表:', mockEmployees)
        
        resolve({
          success: true,
          data: mockEmployees
        })
      }, 200)
    })
  }
  
  /**
   * 从工单工序创建任务
   * @param {Object} workOrderData 工单数据
   * @returns {Promise} 创建结果
   */
  static async createTasksFromWorkOrder(workOrderData) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newTasks = []
        
        // 为每个工序创建对应的任务
        workOrderData.processes.forEach((process, index) => {
          const newTask = {
            id: `task_${Date.now()}_${index}`,
            processId: process.id,
            processName: process.name,
            workOrderId: workOrderData.id,
            workOrderNo: workOrderData.workOrderNo,
            productName: workOrderData.productName,
            productCode: workOrderData.productCode,
            
            status: 'pending',
            assignedTo: null,
            assignedEmployee: null,
            
            plannedQuantity: workOrderData.quantity,
            processedQuantity: 0,
            qualifiedQuantity: 0,
            defectiveQuantity: 0,
            progress: 0,
            
            plannedStartTime: workOrderData.startDate + ' 08:00',
            plannedEndTime: workOrderData.startDate + ' 18:00',
            actualStartTime: null,
            actualEndTime: null,
            
            workstation: process.workstation || '待分配',
            workshop: process.workshop || '生产车间',
            
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
          
          newTasks.push(newTask)
          mockTasks.unshift(newTask)
        })
        
        console.log('从工单创建任务:', newTasks)
        
        resolve({
          success: true,
          data: newTasks,
          message: `成功创建${newTasks.length}个任务`
        })
      }, 800)
    })
  }
}

module.exports = TaskService
