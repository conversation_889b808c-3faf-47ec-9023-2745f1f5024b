<!--工单创建页面-->
<view class="page-container">
  <!-- 创建方式选择 -->
  <view class="creation-mode" wx:if="{{ !selectedMode }}">
    <view class="mode-header">
      <text class="mode-title">选择创建方式</text>
      <text class="mode-subtitle">请选择工单创建方式</text>
    </view>

    <view class="mode-options">
      <view class="mode-card" bindtap="selectMode" data-mode="fromOrder">
        <view class="mode-icon">📋</view>
        <view class="mode-info">
          <text class="mode-name">从订单创建</text>
          <text class="mode-desc">基于现有订单创建生产工单</text>
        </view>
        <text class="mode-arrow">→</text>
      </view>

      <view class="mode-card" bindtap="selectMode" data-mode="manual">
        <view class="mode-icon">✏️</view>
        <view class="mode-info">
          <text class="mode-name">手动创建</text>
          <text class="mode-desc">手动填写工单信息</text>
        </view>
        <text class="mode-arrow">→</text>
      </view>
    </view>
  </view>

  <!-- 从订单创建 -->
  <view class="form-container" wx:if="{{ selectedMode === 'fromOrder' }}">
    <!-- 订单选择 -->
    <view class="form-section">
      <view class="section-title">选择订单</view>

      <view class="form-item">
        <text class="label">关联订单 <text class="required">*</text></text>
        <picker
          range="{{ orderOptions }}"
          range-key="orderNo"
          bindchange="selectOrder"
          value="{{ selectedOrderIndex }}"
        >
          <view class="picker-input">
            {{ selectedOrder ? selectedOrder.orderNo + ' - ' + selectedOrder.productName : '请选择订单' }}
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>

      <!-- 订单信息展示 -->
      <view class="order-info" wx:if="{{ selectedOrder }}">
        <view class="info-row">
          <text class="info-label">客户名称：</text>
          <text class="info-value">{{ selectedOrder.customerName }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">产品名称：</text>
          <text class="info-value">{{ selectedOrder.productName }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">订单数量：</text>
          <text class="info-value">{{ selectedOrder.quantity }} {{ selectedOrder.unit }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">交付日期：</text>
          <text class="info-value">{{ selectedOrder.deliveryDate }}</text>
        </view>
      </view>

      <!-- 数量说明 -->
      <view class="quantity-note">
        <text class="note-text">💡 工单生产数量将使用订单数量：{{ selectedOrder.quantity }} {{ selectedOrder.unit }}</text>
      </view>
    </view>

    <!-- 生产配置 -->
    <view class="form-section" wx:if="{{ selectedOrder }}">
      <view class="section-title">生产配置</view>

      <view class="form-item">
        <text class="label">优先级</text>
        <picker
          range="{{ priorityOptions }}"
          range-key="label"
          bindchange="selectPriority"
          value="{{ selectedPriorityIndex }}"
        >
          <view class="picker-input">
            {{ formData.priorityText || '普通' }}
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="label">预计开始时间</text>
        <picker
          mode="date"
          value="{{ formData.startDate }}"
          start="{{ minDate }}"
          bindchange="selectStartDate"
        >
          <view class="picker-input">
            {{ formData.startDate || '请选择开始时间' }}
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="label">工单备注</text>
        <textarea
          class="textarea"
          value="{{ formData.notes }}"
          placeholder="请输入工单备注"
          bindinput="onNotesInput"
        />
      </view>
    </view>

    <!-- 工序管理 -->
    <view class="form-section" wx:if="{{ selectedOrder }}">
      <view class="section-title">
        <text>工序配置</text>
        <view class="section-actions">
          <button class="btn-route" bindtap="showRouteSelector">应用工艺路线</button>
          <button class="btn-process" bindtap="showProcessSelector">添加工序</button>
        </view>
      </view>

      <!-- 已选工序列表 -->
      <view class="process-list" wx:if="{{ formData.processes.length > 0 }}">
        <view
          class="process-item"
          wx:for="{{ formData.processes }}"
          wx:key="id"
          wx:for-item="process"
        >
          <view class="process-order">{{ index + 1 }}</view>
          <view class="process-info">
            <text class="process-name">{{ process.name }}</text>
            <text class="process-desc">{{ process.description || '暂无描述' }}</text>
            <text class="process-hours">预计工时：{{ process.estimatedHours }}小时</text>
          </view>
          <view class="process-controls">
            <button
              class="control-btn"
              bindtap="moveProcessUp"
              data-index="{{ index }}"
              disabled="{{ index === 0 }}"
            >
              ↑
            </button>
            <button
              class="control-btn"
              bindtap="moveProcessDown"
              data-index="{{ index }}"
              disabled="{{ index === formData.processes.length - 1 }}"
            >
              ↓
            </button>
            <button
              class="control-btn edit-btn"
              bindtap="editProcess"
              data-index="{{ index }}"
            >
              ✏️
            </button>
            <button
              class="control-btn remove-btn"
              bindtap="removeProcess"
              data-index="{{ index }}"
            >
              ✕
            </button>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-processes" wx:if="{{ formData.processes.length === 0 }}">
        <text class="empty-icon">⚙️</text>
        <view class="empty-text">
          <view>暂未配置工序</view>
          <view>可以应用工艺路线或手动添加工序</view>
        </view>
      </view>

      <!-- 总工时显示 -->
      <view class="total-hours" wx:if="{{ formData.processes.length > 0 }}">
        <text class="total-label">总预计工时：</text>
        <text class="total-value">{{ totalProcessHours }}小时</text>
      </view>
    </view>
  </view>

  <!-- 手动创建 -->
  <view class="form-container" wx:if="{{ selectedMode === 'manual' }}">
    <!-- 基本信息 -->
    <view class="form-section">
      <view class="section-title">基本信息</view>

      <view class="form-item">
        <text class="label">工单名称 <text class="required">*</text></text>
        <input
          class="input"
          value="{{ formData.workOrderName }}"
          placeholder="请输入工单名称"
          bindinput="onWorkOrderNameInput"
        />
      </view>

      <view class="form-item">
        <text class="label">产品名称 <text class="required">*</text></text>
        <input
          class="input"
          value="{{ formData.productName }}"
          placeholder="请输入产品名称"
          bindinput="onProductNameInput"
        />
      </view>

      <view class="form-item">
        <text class="label">生产数量 <text class="required">*</text></text>
        <view class="quantity-input">
          <button class="quantity-btn" bindtap="decreaseQuantity">-</button>
          <input
            class="quantity-value"
            type="number"
            value="{{ formData.quantity }}"
            bindinput="onQuantityInput"
          />
          <button class="quantity-btn" bindtap="increaseQuantity">+</button>
          <input
            class="unit-input"
            value="{{ formData.unit }}"
            placeholder="单位"
            bindinput="onUnitInput"
          />
        </view>
      </view>

      <view class="form-item">
        <text class="label">优先级</text>
        <picker
          range="{{ priorityOptions }}"
          range-key="label"
          bindchange="selectPriority"
          value="{{ selectedPriorityIndex }}"
        >
          <view class="picker-input">
            {{ formData.priorityText || '普通' }}
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="label">预计开始时间</text>
        <picker
          mode="date"
          value="{{ formData.startDate }}"
          start="{{ minDate }}"
          bindchange="selectStartDate"
        >
          <view class="picker-input">
            {{ formData.startDate || '请选择开始时间' }}
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="label">工单备注</text>
        <textarea
          class="textarea"
          value="{{ formData.notes }}"
          placeholder="请输入工单备注"
          bindinput="onNotesInput"
        />
      </view>
    </view>

    <!-- 工序管理 (手动创建模式) -->
    <view class="form-section">
      <view class="section-title">
        <text>工序配置</text>
        <view class="section-actions">
          <button class="btn-route" bindtap="showRouteSelector">应用工艺路线</button>
          <button class="btn-process" bindtap="showProcessSelector">添加工序</button>
        </view>
      </view>

      <!-- 已选工序列表 -->
      <view class="process-list" wx:if="{{ formData.processes.length > 0 }}">
        <view
          class="process-item"
          wx:for="{{ formData.processes }}"
          wx:key="id"
          wx:for-item="process"
        >
          <view class="process-order">{{ index + 1 }}</view>
          <view class="process-info">
            <text class="process-name">{{ process.name }}</text>
            <text class="process-desc">{{ process.description || '暂无描述' }}</text>
            <text class="process-hours">预计工时：{{ process.estimatedHours }}小时</text>
          </view>
          <view class="process-controls">
            <button
              class="control-btn"
              bindtap="moveProcessUp"
              data-index="{{ index }}"
              disabled="{{ index === 0 }}"
            >
              ↑
            </button>
            <button
              class="control-btn"
              bindtap="moveProcessDown"
              data-index="{{ index }}"
              disabled="{{ index === formData.processes.length - 1 }}"
            >
              ↓
            </button>
            <button
              class="control-btn edit-btn"
              bindtap="editProcess"
              data-index="{{ index }}"
            >
              ✏️
            </button>
            <button
              class="control-btn remove-btn"
              bindtap="removeProcess"
              data-index="{{ index }}"
            >
              ✕
            </button>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-processes" wx:if="{{ formData.processes.length === 0 }}">
        <text class="empty-icon">⚙️</text>
        <view class="empty-text">
          <view>暂未配置工序</view>
          <view>可以应用工艺路线或手动添加工序</view>
        </view>
      </view>

      <!-- 总工时显示 -->
      <view class="total-hours" wx:if="{{ formData.processes.length > 0 }}">
        <text class="total-label">总预计工时：</text>
        <text class="total-value">{{ totalProcessHours }}小时</text>
      </view>
    </view>
  </view>

  <!-- 工艺路线选择弹窗 -->
  <view class="modal-overlay" wx:if="{{ showRouteModal }}" bindtap="hideRouteModal">
    <view class="modal-container" catchtap="true">
      <view class="modal-header">
        <text class="modal-title">选择工艺路线</text>
        <button class="modal-close" bindtap="hideRouteModal">✕</button>
      </view>

      <view class="modal-body">
        <!-- 工艺路线搜索 -->
        <view class="route-search">
          <input
            class="search-input"
            placeholder="搜索工艺路线名称"
            value="{{ routeSearchKeyword }}"
            bindinput="onRouteSearchInput"
          />
        </view>

        <!-- 可选工艺路线列表 -->
        <view class="available-routes">
          <view
            class="available-route-item"
            wx:for="{{ availableRoutes }}"
            wx:key="id"
            bindtap="selectRoute"
            data-route="{{ item }}"
          >
            <view class="route-info">
              <text class="route-name">{{ item.name }}</text>
              <text class="route-desc">{{ item.description }}</text>
              <view class="route-stats">
                <text class="route-processes">{{ item.processes.length }}个工序</text>
                <text class="route-hours">{{ item.totalHours }}小时</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" wx:if="{{ availableRoutes.length === 0 }}">
          <text class="empty-icon">🛤️</text>
          <view class="empty-text">
            <view>暂无可选工艺路线</view>
            <view>请先在设置中添加工艺路线</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 工序选择弹窗 -->
  <view class="modal-overlay" wx:if="{{ showProcessModal }}" bindtap="hideProcessModal">
    <view class="modal-container" catchtap="true">
      <view class="modal-header">
        <text class="modal-title">选择工序</text>
        <button class="modal-close" bindtap="hideProcessModal">✕</button>
      </view>

      <view class="modal-body">
        <!-- 工序搜索 -->
        <view class="process-search">
          <input
            class="search-input"
            placeholder="搜索工序名称"
            value="{{ processSearchKeyword }}"
            bindinput="onProcessSearchInput"
          />
        </view>

        <!-- 可选工序列表 -->
        <view class="available-processes">
          <view
            class="available-process-item"
            wx:for="{{ availableProcesses }}"
            wx:key="id"
            bindtap="selectProcess"
            data-process="{{ item }}"
          >
            <view class="process-info">
              <text class="process-name">{{ item.name }}</text>
              <text class="process-category">{{ item.category }}</text>
              <text class="process-desc">{{ item.description }}</text>
            </view>
            <text class="process-hours">{{ item.estimatedHours }}小时</text>
          </view>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" wx:if="{{ availableProcesses.length === 0 }}">
          <text class="empty-icon">⚙️</text>
          <view class="empty-text">
            <view>暂无可选工序</view>
            <view>请先在设置中添加工序</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 工序编辑弹窗 -->
  <view class="modal-overlay" wx:if="{{ showEditProcessModal }}" bindtap="hideEditProcessModal">
    <view class="modal-container" catchtap="true">
      <view class="modal-header">
        <text class="modal-title">编辑工序</text>
        <button class="modal-close" bindtap="hideEditProcessModal">✕</button>
      </view>

      <view class="modal-body">
        <view class="form-group">
          <text class="form-label">工序名称</text>
          <input
            class="form-input"
            value="{{ editingProcess.name }}"
            bindinput="onEditProcessNameInput"
            placeholder="请输入工序名称"
          />
        </view>

        <view class="form-group">
          <text class="form-label">工序描述</text>
          <textarea
            class="form-textarea"
            value="{{ editingProcess.description }}"
            bindinput="onEditProcessDescInput"
            placeholder="请输入工序描述"
            auto-height
          />
        </view>

        <view class="form-group">
          <text class="form-label">预计工时 (小时)</text>
          <input
            class="form-input"
            type="digit"
            value="{{ editingProcess.estimatedHours }}"
            bindinput="onEditProcessHoursInput"
            placeholder="请输入预计工时"
          />
        </view>
      </view>

      <view class="modal-footer">
        <button class="btn-cancel" bindtap="hideEditProcessModal">取消</button>
        <button class="btn-confirm" bindtap="confirmEditProcess">确认</button>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions" wx:if="{{ selectedMode }}">
    <view class="action-buttons">
      <button class="btn-back" bindtap="goBack">返回</button>
      <button class="btn-submit" bindtap="submitWorkOrder" disabled="{{ !isFormValid }}">创建工单</button>
    </view>
  </view>
</view>
