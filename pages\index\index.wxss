/* 首页样式 - 简约现代风格 */
@import '../../styles/modern-simple.wxss';

/* 统计卡片样式增强 */
.stats-container {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.stats-card {
  flex: 1;
  text-align: center;
  position: relative;
}

.stats-content {
  padding: var(--spacing-xl);
}

.stats-number {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  line-height: 1.2;
  margin-bottom: var(--spacing-xs);
}

.stats-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-sm);
}

.stats-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
}

.trend-icon {
  opacity: 0.8;
}

.trend-text {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  background-color: var(--bg-secondary);
  padding: 6rpx 12rpx;
  border-radius: var(--radius-lg);
}

/* 快捷操作网格布局 */
.action-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-base);
  margin-top: var(--spacing-lg);
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-sm);
  text-align: center;
  transition: all var(--transition-base);
}

.action-item:active {
  transform: scale(0.95);
}

/* 移除图标样式，使用纯文字 */

.action-text {
  font-size: 28rpx;
  color: #1D1D1F;
  font-weight: 500;
  text-align: center;
}

/* 订单相关样式 */
.order-item {
  margin-bottom: var(--spacing-base);
}

.order-no {
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.order-status {
  font-size: var(--font-size-xs);
}

.customer-name, .product-name {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-right: var(--spacing-sm);
}

.order-time {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

.view-all {
  font-size: var(--font-size-sm);
}

/* 任务相关样式 - 使用现代组件 */
.task-count {
  margin-left: var(--spacing-xs);
}

/* 移除任务图标样式 */

.task-content {
  flex: 1;
}

.task-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.task-desc {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.task-time {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

.task-arrow {
  font-size: var(--font-size-lg);
  color: var(--text-quaternary);
  margin-left: var(--spacing-xs);
}

/* 订单状态样式 */
.order-status.pending {
  background-color: var(--warning-light);
  color: var(--warning-color);
}

.order-status.confirmed {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.order-status.processing {
  background-color: var(--success-light);
  color: var(--success-color);
}

.order-status.completed {
  background-color: var(--bg-secondary);
  color: var(--text-tertiary);
}

.order-status.cancelled {
  background-color: var(--error-light);
  color: var(--error-color);
}
