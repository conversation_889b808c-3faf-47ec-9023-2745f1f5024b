<!--工单详情页面-->
<view class="page-container">

  <!-- 模块1：工单头部信息区（可折叠） -->
  <view class="header-info-section">
    <view class="header-content">
      <!-- 第一行：状态标签 + 工单编号 -->
      <view class="header-row">
        <view class="status-tags">
          <view class="status-tag executing">执行中</view>
          <view class="status-tag overdue">逾期</view>
        </view>
        <text class="workorder-no">{{ workOrderInfo.workOrderNo }}</text>
      </view>

      <!-- 第二行：计划时间 -->
      <view class="header-row">
        <view class="time-info">
          <text class="calendar-icon">📅</text>
          <text class="time-text">{{ workOrderInfo.plannedTimeRange }}</text>
        </view>
      </view>

      <!-- 第三行：产品信息 -->
      <view class="header-row">
        <view class="product-info" bindtap="viewProductDetail">
          <text class="product-icon">📦</text>
          <text class="product-text">{{ workOrderInfo.productInfo }}</text>
        </view>
      </view>

      <!-- 第四行：工单进度 -->
      <view class="header-row">
        <view class="progress-info">
          <text class="progress-icon">📊</text>
          <text class="progress-text">工单进度：{{ workOrderInfo.completedQuantity }}/{{ workOrderInfo.totalQuantity }} 个</text>
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{ workOrderInfo.progressPercent }}%"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部：折叠箭头 -->
    <view class="collapse-toggle" bindtap="toggleHeaderCollapse">
      <text class="collapse-icon {{ headerCollapsed ? 'collapsed' : '' }}">▼</text>
    </view>
  </view>

  <!-- 模块2：标签切换栏 -->
  <view class="tab-switch-section">
    <view class="tab-container">
      <view
        class="tab-item {{ activeTab === 'progress' ? 'active' : '' }}"
        bindtap="switchTab"
        data-tab="progress"
      >
        生产进度
      </view>
      <view
        class="tab-item {{ activeTab === 'materials' ? 'active' : '' }}"
        bindtap="switchTab"
        data-tab="materials"
      >
        用料明细
      </view>
    </view>
  </view>

  <!-- 模块3：工序卡片列表 -->
  <view class="process-cards-section" wx:if="{{ activeTab === 'progress' }}">

    <!-- 已结束工序 -->
    <view class="process-group" wx:if="{{ completedProcesses.length > 0 }}">
      <view
        class="process-card completed"
        wx:for="{{ completedProcesses }}"
        wx:key="id"
      >
        <!-- 顶部行：序号+状态标签 + 工序名 + 计划数 -->
        <view class="card-header">
          <view class="left-section">
            <view class="process-number completed">{{ item.sequence }}</view>
            <view class="status-tag completed">已结束</view>
            <text class="process-name">{{ item.name }}</text>
          </view>
          <view class="plan-quantity-tag">
            {{ item.plannedQuantity }} 计划数
          </view>
        </view>

        <!-- 中部行：工序编号 + 数据 -->
        <view class="card-middle">
          <view class="process-code">
            <text class="file-icon">📄</text>
            <text class="code-text">{{ item.processCode }}</text>
          </view>
          <view class="quantity-data">
            <text class="data-item">计划数 {{ item.plannedQuantity }}</text>
            <text class="data-item qualified">良品数 {{ item.qualifiedQuantity }}</text>
            <text class="data-item defective">不良品数 {{ item.defectiveQuantity }}</text>
          </view>
        </view>

        <!-- 底部行：进度条 + 权限 + 操作 -->
        <view class="card-footer">
          <view class="progress-section">
            <view class="progress-bar completed">
              <view class="progress-fill" style="width: {{ item.progressPercent }}%"></view>
            </view>
            <text class="progress-percent">{{ item.progressPercent }}%</text>
          </view>
          <view class="permission-info">
            <text class="permission-icon">🔑</text>
            <text class="permission-text">{{ item.workstation }}</text>
          </view>
          <view class="action-buttons">
            <button class="action-btn detail-btn" bindtap="viewProcessDetail" data-process="{{ item }}">详情</button>
          </view>
        </view>
      </view>
    </view>

    <!-- 执行中+逾期工序 -->
    <view class="process-group" wx:if="{{ activeProcesses.length > 0 }}">
      <view
        class="process-card active"
        wx:for="{{ activeProcesses }}"
        wx:key="id"
      >
        <!-- 顶部行：序号+状态标签 + 工序名 + 计划数 -->
        <view class="card-header">
          <view class="left-section">
            <view class="process-number active">{{ item.sequence }}</view>
            <view class="status-tag executing">执行中</view>
            <view class="status-tag overdue" wx:if="{{ item.isOverdue }}">逾期</view>
            <text class="process-name">{{ item.name }}</text>
          </view>
          <view class="plan-quantity-tag">
            {{ item.plannedQuantity }} 计划数
          </view>
        </view>

        <!-- 中部行：工序编号 + 数据 -->
        <view class="card-middle">
          <view class="process-code">
            <text class="file-icon">📄</text>
            <text class="code-text">{{ item.processCode }}</text>
          </view>
          <view class="quantity-data">
            <text class="data-item">计划数 {{ item.plannedQuantity }}</text>
            <text class="data-item">良品数 {{ item.qualifiedQuantity }}</text>
            <text class="data-item defective">不良品数 {{ item.defectiveQuantity }}</text>
          </view>
        </view>

        <!-- 底部行：进度条 + 权限 + 操作 -->
        <view class="card-footer">
          <view class="progress-section">
            <view class="progress-bar">
              <view class="progress-fill" style="width: {{ item.progressPercent }}%"></view>
            </view>
            <text class="progress-percent">{{ item.progressPercent }}%</text>
          </view>
          <view class="permission-info">
            <text class="permission-icon">🔑</text>
            <text class="permission-text">{{ item.workstation }}</text>
          </view>
          <view class="action-buttons">
            <button class="action-btn detail-btn" bindtap="viewProcessDetail" data-process="{{ item }}">详情</button>
            <button class="action-btn report-btn" bindtap="reportWork" data-process="{{ item }}">报工</button>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 用料明细内容（暂时隐藏） -->
  <view class="materials-section" wx:if="{{ activeTab === 'materials' }}">
    <view class="placeholder-content">
      <text class="placeholder-text">用料明细功能开发中...</text>
    </view>
  </view>

  <!-- 模块4：底部操作区 -->
  <view class="workorder-detail-bottom-actions">
    <view class="workorder-detail-left-actions">
      <button class="workorder-detail-more-btn" bindtap="showMoreActions">
        <text class="workorder-detail-more-icon">▲</text>
        <text class="workorder-detail-more-text">更多</text>
      </button>
    </view>
    <view class="workorder-detail-right-actions">
      <button class="workorder-detail-priority-btn" bindtap="showQuickActionModal">优先级</button>
      <button class="workorder-detail-finish-btn" bindtap="finishWorkOrder">结束</button>
    </view>
  </view>

</view>
