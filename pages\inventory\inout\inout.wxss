/* 库存出入库页面样式 */
.page-container {
  background-color: var(--bg-page);
  min-height: 100vh;
  padding-bottom: 140rpx;
}

/* 操作类型选择 */
.operation-tabs {
  display: flex;
  background-color: #FFFFFF;
  margin: 20rpx;
  border-radius: var(--radius-lg);
  padding: 8rpx;
  box-shadow: var(--shadow-sm);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx 16rpx;
  border-radius: var(--radius-base);
  transition: all 0.2s ease;
}

.tab-item.active {
  background-color: var(--primary-color);
  color: #FFFFFF;
}

.tab-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.tab-text {
  font-size: 24rpx;
  font-weight: var(--font-weight-medium);
}

/* 表单容器 */
.form-container {
  padding: 0 20rpx;
}

.form-section {
  background-color: #FFFFFF;
  border-radius: var(--radius-lg);
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: var(--shadow-sm);
}

.section-title {
  font-size: 32rpx;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: 24rpx;
  padding-bottom: 12rpx;
  border-bottom: 2rpx solid var(--primary-light);
}

/* 表单项 */
.form-item {
  margin-bottom: 24rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.label {
  display: block;
  font-size: 28rpx;
  color: var(--text-primary);
  margin-bottom: 12rpx;
  font-weight: var(--font-weight-medium);
}

.required {
  color: var(--error-color);
}

/* 操作类型显示 */
.operation-display {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 20rpx;
  background-color: var(--primary-light);
  border-radius: var(--radius-base);
}

.operation-icon {
  font-size: 28rpx;
}

.operation-text {
  font-size: 28rpx;
  font-weight: var(--font-weight-medium);
  color: var(--primary-color);
}

/* 库存显示 */
.stock-display {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 20rpx;
  background-color: var(--bg-secondary);
  border-radius: var(--radius-base);
}

.stock-display.negative {
  background-color: var(--error-light);
}

.stock-value {
  font-size: 32rpx;
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.stock-display.negative .stock-value {
  color: var(--error-color);
}

.stock-unit {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.stock-warning {
  font-size: 24rpx;
  color: var(--error-color);
  margin-left: 16rpx;
}

/* 输入框 */
.input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-base);
  font-size: 28rpx;
  color: var(--text-primary);
  background-color: #FFFFFF;
  box-sizing: border-box;
}

.input:focus {
  border-color: var(--primary-color);
}

.textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 16rpx 20rpx;
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-base);
  font-size: 28rpx;
  color: var(--text-primary);
  background-color: #FFFFFF;
  box-sizing: border-box;
}

.textarea:focus {
  border-color: var(--primary-color);
}

/* 选择器 */
.picker-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-base);
  font-size: 28rpx;
  color: var(--text-primary);
  background-color: #FFFFFF;
}

.picker-arrow {
  color: var(--text-tertiary);
  font-size: 24rpx;
}

/* 数量输入 */
.quantity-input {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-base);
  background-color: #FFFFFF;
  color: var(--text-primary);
  font-size: 32rpx;
  font-weight: var(--font-weight-bold);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.quantity-btn:active {
  background-color: var(--bg-secondary);
}

.quantity-value {
  flex: 1;
  height: 60rpx;
  text-align: center;
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-base);
  font-size: 28rpx;
  color: var(--text-primary);
}

.unit {
  font-size: 24rpx;
  color: var(--text-secondary);
  min-width: 40rpx;
}

/* 操作预览 */
.preview-card {
  background-color: var(--bg-secondary);
  border-radius: var(--radius-base);
  padding: 20rpx;
}

.preview-row {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.preview-row:last-child {
  margin-bottom: 0;
}

.preview-label {
  font-size: 24rpx;
  color: var(--text-secondary);
  min-width: 140rpx;
}

.preview-value {
  font-size: 24rpx;
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

.preview-value.negative {
  color: var(--error-color);
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  padding: 20rpx;
  border-top: 1rpx solid var(--border-color);
  z-index: 100;
}

.action-buttons {
  display: flex;
  gap: 16rpx;
}

.action-buttons button {
  flex: 1;
  height: 80rpx;
  border-radius: var(--radius-base);
  font-size: 28rpx;
  font-weight: var(--font-weight-medium);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-cancel {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 2rpx solid var(--border-color);
}

.btn-submit {
  background-color: var(--primary-color);
  color: #FFFFFF;
}

.btn-submit[disabled] {
  background-color: var(--bg-secondary);
  color: var(--text-tertiary);
}

/* 错误状态样式 */
:root {
  --error-light: #FFEBEE;
}
