<!--库存出入库页面-->
<view class="page-container">
  <!-- 操作类型选择 -->
  <view class="operation-tabs">
    <view 
      class="tab-item {{ operationType === 'in' ? 'active' : '' }}" 
      bindtap="switchOperation" 
      data-type="in"
    >
      <text class="tab-icon">📦</text>
      <text class="tab-text">入库</text>
    </view>
    <view 
      class="tab-item {{ operationType === 'out' ? 'active' : '' }}" 
      bindtap="switchOperation" 
      data-type="out"
    >
      <text class="tab-icon">📤</text>
      <text class="tab-text">出库</text>
    </view>
  </view>

  <!-- 表单内容 -->
  <view class="form-container">
    <!-- 基本信息 -->
    <view class="form-section">
      <view class="section-title">基本信息</view>
      
      <view class="form-item">
        <text class="label">操作类型</text>
        <view class="operation-display">
          <text class="operation-icon">{{ operationType === 'in' ? '📦' : '📤' }}</text>
          <text class="operation-text">{{ operationType === 'in' ? '入库' : '出库' }}</text>
        </view>
      </view>
      
      <view class="form-item">
        <text class="label">产品名称 <text class="required">*</text></text>
        <picker 
          range="{{ productOptions }}" 
          range-key="name"
          bindchange="selectProduct"
          value="{{ selectedProductIndex }}"
        >
          <view class="picker-input">
            {{ formData.productName || '请选择产品' }}
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>
      
      <view class="form-item" wx:if="{{ selectedProduct }}">
        <text class="label">当前库存</text>
        <view class="stock-display">
          <text class="stock-value">{{ selectedProduct.stock || 0 }}</text>
          <text class="stock-unit">{{ selectedProduct.unit }}</text>
        </view>
      </view>
      
      <view class="form-item">
        <text class="label">{{ operationType === 'in' ? '入库' : '出库' }}数量 <text class="required">*</text></text>
        <view class="quantity-input">
          <button class="quantity-btn" bindtap="decreaseQuantity">-</button>
          <input 
            class="quantity-value" 
            type="number"
            value="{{ formData.quantity }}" 
            bindinput="onQuantityInput"
          />
          <button class="quantity-btn" bindtap="increaseQuantity">+</button>
          <text class="unit">{{ formData.unit || '件' }}</text>
        </view>
      </view>
      
      <view class="form-item" wx:if="{{ selectedProduct && operationType === 'out' }}">
        <text class="label">操作后库存</text>
        <view class="stock-display {{ afterStock < 0 ? 'negative' : '' }}">
          <text class="stock-value">{{ afterStock }}</text>
          <text class="stock-unit">{{ selectedProduct.unit }}</text>
          <text class="stock-warning" wx:if="{{ afterStock < 0 }}">库存不足</text>
        </view>
      </view>
    </view>

    <!-- 详细信息 -->
    <view class="form-section">
      <view class="section-title">详细信息</view>
      
      <view class="form-item">
        <text class="label">操作原因 <text class="required">*</text></text>
        <picker 
          range="{{ reasonOptions }}" 
          range-key="label"
          bindchange="selectReason"
          value="{{ selectedReasonIndex }}"
        >
          <view class="picker-input">
            {{ formData.reasonText || '请选择操作原因' }}
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>
      
      <view class="form-item" wx:if="{{ operationType === 'in' }}">
        <text class="label">供应商</text>
        <input 
          class="input" 
          value="{{ formData.supplier }}" 
          placeholder="请输入供应商名称"
          bindinput="onSupplierInput"
        />
      </view>
      
      <view class="form-item" wx:if="{{ operationType === 'out' }}">
        <text class="label">领用部门</text>
        <picker 
          range="{{ departmentOptions }}" 
          range-key="name"
          bindchange="selectDepartment"
          value="{{ selectedDepartmentIndex }}"
        >
          <view class="picker-input">
            {{ formData.departmentName || '请选择部门' }}
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>
      
      <view class="form-item" wx:if="{{ operationType === 'out' }}">
        <text class="label">领用人</text>
        <input 
          class="input" 
          value="{{ formData.recipient }}" 
          placeholder="请输入领用人姓名"
          bindinput="onRecipientInput"
        />
      </view>
      
      <view class="form-item">
        <text class="label">操作日期</text>
        <picker 
          mode="date"
          value="{{ formData.operationDate }}"
          end="{{ maxDate }}"
          bindchange="selectOperationDate"
        >
          <view class="picker-input">
            {{ formData.operationDate || '请选择操作日期' }}
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>
      
      <view class="form-item">
        <text class="label">备注</text>
        <textarea 
          class="textarea" 
          value="{{ formData.notes }}" 
          placeholder="请输入操作备注"
          bindinput="onNotesInput"
        />
      </view>
    </view>

    <!-- 操作预览 -->
    <view class="form-section" wx:if="{{ isFormValid }}">
      <view class="section-title">操作预览</view>
      <view class="preview-card">
        <view class="preview-row">
          <text class="preview-label">操作类型：</text>
          <text class="preview-value">{{ operationType === 'in' ? '入库' : '出库' }}</text>
        </view>
        <view class="preview-row">
          <text class="preview-label">产品名称：</text>
          <text class="preview-value">{{ formData.productName }}</text>
        </view>
        <view class="preview-row">
          <text class="preview-label">操作数量：</text>
          <text class="preview-value">{{ formData.quantity }} {{ formData.unit }}</text>
        </view>
        <view class="preview-row" wx:if="{{ selectedProduct }}">
          <text class="preview-label">当前库存：</text>
          <text class="preview-value">{{ selectedProduct.stock || 0 }} {{ selectedProduct.unit }}</text>
        </view>
        <view class="preview-row" wx:if="{{ selectedProduct }}">
          <text class="preview-label">操作后库存：</text>
          <text class="preview-value {{ afterStock < 0 ? 'negative' : '' }}">
            {{ afterStock }} {{ selectedProduct.unit }}
          </text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <view class="action-buttons">
      <button class="btn-cancel" bindtap="cancel">取消</button>
      <button class="btn-submit" bindtap="submitOperation" disabled="{{ !isFormValid || (operationType === 'out' && afterStock < 0) }}">
        确认{{ operationType === 'in' ? '入库' : '出库' }}
      </button>
    </view>
  </view>
</view>
